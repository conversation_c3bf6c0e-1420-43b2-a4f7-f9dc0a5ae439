# Core dependencies for the IMEI unlock application
wxpython>=4.1.0
requests>=2.25.0
selenium>=4.0.0
configparser>=5.0.0

# Optional dependencies for enhanced functionality
# Note: These may need to be installed manually or from specific sources
# DrissionPage>=3.0.0  # For Chrome automation - install with: pip install DrissionPage
# CloudflareBypasser    # Custom module - may need manual installation

# Development and testing dependencies (optional)
pytest>=6.0.0
pytest-cov>=2.10.0

# Additional utilities
python-dateutil>=2.8.0
