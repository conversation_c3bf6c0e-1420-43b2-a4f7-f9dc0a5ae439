#!/usr/bin/env python3
"""
Dependency checker for IMEI Unlock Application
Run this script to verify all required dependencies are installed.
"""

import sys
import importlib
import subprocess
import os

def check_python_version():
    """Check if Python version is compatible"""
    print("🐍 Checking Python version...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 7:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - OK")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.7+")
        return False

def check_package(package_name, import_name=None, optional=False):
    """Check if a Python package is installed"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        status = "✅" if not optional else "✅ (optional)"
        print(f"{status} {package_name} - OK")
        return True
    except ImportError:
        status = "❌" if not optional else "⚠️ (optional)"
        print(f"{status} {package_name} - Not found")
        if not optional:
            print(f"   Install with: pip install {package_name}")
        return optional  # Return True for optional packages

def check_file_exists(file_path, description):
    """Check if a required file exists"""
    if os.path.exists(file_path):
        print(f"✅ {description} - Found at {file_path}")
        return True
    else:
        print(f"❌ {description} - Not found at {file_path}")
        return False

def main():
    print("🔍 IMEI Unlock Application - Dependency Check")
    print("=" * 50)
    
    all_good = True
    
    # Check Python version
    all_good &= check_python_version()
    print()
    
    # Check required packages
    print("📦 Checking required Python packages...")
    required_packages = [
        ("wxpython", "wx"),
        ("requests", "requests"),
        ("selenium", "selenium"),
    ]
    
    for package, import_name in required_packages:
        all_good &= check_package(package, import_name)
    
    print()
    
    # Check optional packages
    print("📦 Checking optional Python packages...")
    optional_packages = [
        ("DrissionPage", "DrissionPage"),
        ("CloudflareBypasser", "CloudflareBypasser"),
    ]
    
    for package, import_name in optional_packages:
        check_package(package, import_name, optional=True)
    
    print()
    
    # Check required files
    print("📁 Checking required files and directories...")
    required_files = [
        ("tool/adb/adb.exe", "Android Debug Bridge"),
        ("tool/chromedriver.exe", "Chrome WebDriver"),
    ]
    
    for file_path, description in required_files:
        file_exists = check_file_exists(file_path, description)
        if not file_exists:
            all_good = False
    
    print()
    
    # Check configuration
    print("⚙️ Checking configuration...")
    config_exists = check_file_exists("tool/解锁配置.ini", "Configuration file")
    if not config_exists:
        print("   Note: Configuration file will be created automatically on first run")
    
    print()
    print("=" * 50)
    
    if all_good:
        print("🎉 All required dependencies are satisfied!")
        print("   You can now run the application with: python run.py")
    else:
        print("⚠️ Some required dependencies are missing.")
        print("   Please install the missing packages and files before running the application.")
        print("   See SETUP_GUIDE.md for detailed instructions.")
    
    return 0 if all_good else 1

if __name__ == "__main__":
    sys.exit(main())
