(['C:\\Users\\<USER>\\Desktop\\unlock1\\run.py'],
 ['C:\\Users\\<USER>\\Desktop\\unlock1',
  'C:\\Users\\<USER>\\Desktop\\unlock1\\fileimei.py'],
 ['sb', 'do', 'au', 'txt', 'update', 'CloudflareBypasser'],
 [('C:\\Users\\<USER>\\Desktop\\unlock1\\run\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.10.9 (tags/v3.10.9:1dd9be6, Dec  6 2022, 20:01:21) [MSC v.1934 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('run', 'C:\\Users\\<USER>\\Desktop\\unlock1\\run.py', 'PYSOURCE')],
 [('multiprocessing.spawn',
   'C:\\Python310\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Python310\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('signal', 'C:\\Python310\\lib\\signal.py', 'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Python310\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Python310\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Python310\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Python310\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client', 'C:\\Python310\\lib\\xmlrpc\\client.py', 'PYMODULE'),
  ('xmlrpc', 'C:\\Python310\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('gzip', 'C:\\Python310\\lib\\gzip.py', 'PYMODULE'),
  ('argparse', 'C:\\Python310\\lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'C:\\Python310\\lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'C:\\Python310\\lib\\copy.py', 'PYMODULE'),
  ('gettext', 'C:\\Python310\\lib\\gettext.py', 'PYMODULE'),
  ('_compression', 'C:\\Python310\\lib\\_compression.py', 'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Python310\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers', 'C:\\Python310\\lib\\xml\\parsers\\__init__.py', 'PYMODULE'),
  ('xml', 'C:\\Python310\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Python310\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils', 'C:\\Python310\\lib\\xml\\sax\\saxutils.py', 'PYMODULE'),
  ('urllib.request', 'C:\\Python310\\lib\\urllib\\request.py', 'PYMODULE'),
  ('urllib', 'C:\\Python310\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('fnmatch', 'C:\\Python310\\lib\\fnmatch.py', 'PYMODULE'),
  ('getpass', 'C:\\Python310\\lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'C:\\Python310\\lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'C:\\Python310\\lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'C:\\Python310\\lib\\netrc.py', 'PYMODULE'),
  ('shlex', 'C:\\Python310\\lib\\shlex.py', 'PYMODULE'),
  ('mimetypes', 'C:\\Python310\\lib\\mimetypes.py', 'PYMODULE'),
  ('getopt', 'C:\\Python310\\lib\\getopt.py', 'PYMODULE'),
  ('email.utils', 'C:\\Python310\\lib\\email\\utils.py', 'PYMODULE'),
  ('email.charset', 'C:\\Python310\\lib\\email\\charset.py', 'PYMODULE'),
  ('email.encoders', 'C:\\Python310\\lib\\email\\encoders.py', 'PYMODULE'),
  ('quopri', 'C:\\Python310\\lib\\quopri.py', 'PYMODULE'),
  ('email.errors', 'C:\\Python310\\lib\\email\\errors.py', 'PYMODULE'),
  ('email.quoprimime', 'C:\\Python310\\lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.base64mime', 'C:\\Python310\\lib\\email\\base64mime.py', 'PYMODULE'),
  ('email._parseaddr', 'C:\\Python310\\lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('calendar', 'C:\\Python310\\lib\\calendar.py', 'PYMODULE'),
  ('random', 'C:\\Python310\\lib\\random.py', 'PYMODULE'),
  ('statistics', 'C:\\Python310\\lib\\statistics.py', 'PYMODULE'),
  ('fractions', 'C:\\Python310\\lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'C:\\Python310\\lib\\numbers.py', 'PYMODULE'),
  ('http.cookiejar', 'C:\\Python310\\lib\\http\\cookiejar.py', 'PYMODULE'),
  ('http', 'C:\\Python310\\lib\\http\\__init__.py', 'PYMODULE'),
  ('ssl', 'C:\\Python310\\lib\\ssl.py', 'PYMODULE'),
  ('urllib.response', 'C:\\Python310\\lib\\urllib\\response.py', 'PYMODULE'),
  ('urllib.error', 'C:\\Python310\\lib\\urllib\\error.py', 'PYMODULE'),
  ('contextlib', 'C:\\Python310\\lib\\contextlib.py', 'PYMODULE'),
  ('string', 'C:\\Python310\\lib\\string.py', 'PYMODULE'),
  ('email', 'C:\\Python310\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser', 'C:\\Python310\\lib\\email\\parser.py', 'PYMODULE'),
  ('email._policybase',
   'C:\\Python310\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.feedparser', 'C:\\Python310\\lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.message', 'C:\\Python310\\lib\\email\\message.py', 'PYMODULE'),
  ('email.policy', 'C:\\Python310\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.contentmanager',
   'C:\\Python310\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Python310\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'C:\\Python310\\lib\\email\\iterators.py', 'PYMODULE'),
  ('email.generator', 'C:\\Python310\\lib\\email\\generator.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Python310\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('uu', 'C:\\Python310\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'C:\\Python310\\lib\\optparse.py', 'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Python310\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header', 'C:\\Python310\\lib\\email\\header.py', 'PYMODULE'),
  ('bisect', 'C:\\Python310\\lib\\bisect.py', 'PYMODULE'),
  ('xml.sax', 'C:\\Python310\\lib\\xml\\sax\\__init__.py', 'PYMODULE'),
  ('xml.sax.handler', 'C:\\Python310\\lib\\xml\\sax\\handler.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Python310\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Python310\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('urllib.parse', 'C:\\Python310\\lib\\urllib\\parse.py', 'PYMODULE'),
  ('http.client', 'C:\\Python310\\lib\\http\\client.py', 'PYMODULE'),
  ('decimal', 'C:\\Python310\\lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'C:\\Python310\\lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars', 'C:\\Python310\\lib\\contextvars.py', 'PYMODULE'),
  ('datetime', 'C:\\Python310\\lib\\datetime.py', 'PYMODULE'),
  ('_strptime', 'C:\\Python310\\lib\\_strptime.py', 'PYMODULE'),
  ('base64', 'C:\\Python310\\lib\\base64.py', 'PYMODULE'),
  ('hmac', 'C:\\Python310\\lib\\hmac.py', 'PYMODULE'),
  ('struct', 'C:\\Python310\\lib\\struct.py', 'PYMODULE'),
  ('selectors', 'C:\\Python310\\lib\\selectors.py', 'PYMODULE'),
  ('tempfile', 'C:\\Python310\\lib\\tempfile.py', 'PYMODULE'),
  ('shutil', 'C:\\Python310\\lib\\shutil.py', 'PYMODULE'),
  ('zipfile', 'C:\\Python310\\lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'C:\\Python310\\lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\Python310\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib', 'C:\\Python310\\lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._common',
   'C:\\Python310\\lib\\importlib\\_common.py',
   'PYMODULE'),
  ('importlib._adapters',
   'C:\\Python310\\lib\\importlib\\_adapters.py',
   'PYMODULE'),
  ('typing', 'C:\\Python310\\lib\\typing.py', 'PYMODULE'),
  ('importlib.abc', 'C:\\Python310\\lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib._abc', 'C:\\Python310\\lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Python310\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Python310\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Python310\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Python310\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Python310\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Python310\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Python310\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Python310\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Python310\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('csv', 'C:\\Python310\\lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'C:\\Python310\\lib\\importlib\\readers.py',
   'PYMODULE'),
  ('tokenize', 'C:\\Python310\\lib\\tokenize.py', 'PYMODULE'),
  ('token', 'C:\\Python310\\lib\\token.py', 'PYMODULE'),
  ('pathlib', 'C:\\Python310\\lib\\pathlib.py', 'PYMODULE'),
  ('importlib.util', 'C:\\Python310\\lib\\importlib\\util.py', 'PYMODULE'),
  ('tarfile', 'C:\\Python310\\lib\\tarfile.py', 'PYMODULE'),
  ('lzma', 'C:\\Python310\\lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'C:\\Python310\\lib\\bz2.py', 'PYMODULE'),
  ('logging', 'C:\\Python310\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('pickle', 'C:\\Python310\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'C:\\Python310\\lib\\pprint.py', 'PYMODULE'),
  ('dataclasses', 'C:\\Python310\\lib\\dataclasses.py', 'PYMODULE'),
  ('inspect', 'C:\\Python310\\lib\\inspect.py', 'PYMODULE'),
  ('dis', 'C:\\Python310\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'C:\\Python310\\lib\\opcode.py', 'PYMODULE'),
  ('ast', 'C:\\Python310\\lib\\ast.py', 'PYMODULE'),
  ('_compat_pickle', 'C:\\Python310\\lib\\_compat_pickle.py', 'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Python310\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Python310\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Python310\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Python310\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Python310\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Python310\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Python310\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes', 'C:\\Python310\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian', 'C:\\Python310\\lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Python310\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Python310\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Python310\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'C:\\Python310\\lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Python310\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Python310\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Python310\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Python310\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'C:\\Python310\\lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Python310\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Python310\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy', 'C:\\Python310\\lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'C:\\Python310\\lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'C:\\Python310\\lib\\zipimport.py', 'PYMODULE'),
  ('multiprocessing',
   'C:\\Python310\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('CloudflareBypasser',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\CloudflareBypasser.py',
   'PYMODULE'),
  ('DrissionPage',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\__init__.py',
   'PYMODULE'),
  ('DrissionPage.version',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\version.py',
   'PYMODULE'),
  ('DrissionPage._pages.web_page',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_pages\\web_page.py',
   'PYMODULE'),
  ('DrissionPage._pages', '-', 'PYMODULE'),
  ('DrissionPage._units.setter',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_units\\setter.py',
   'PYMODULE'),
  ('DrissionPage._units', '-', 'PYMODULE'),
  ('DrissionPage.errors',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\errors.py',
   'PYMODULE'),
  ('DrissionPage._functions.web',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_functions\\web.py',
   'PYMODULE'),
  ('DrissionPage._functions', '-', 'PYMODULE'),
  ('DataRecorder.tools',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DataRecorder\\tools.py',
   'PYMODULE'),
  ('DataRecorder',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DataRecorder\\__init__.py',
   'PYMODULE'),
  ('DataRecorder.recorder',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DataRecorder\\recorder.py',
   'PYMODULE'),
  ('json', 'C:\\Python310\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder', 'C:\\Python310\\lib\\json\\encoder.py', 'PYMODULE'),
  ('json.decoder', 'C:\\Python310\\lib\\json\\decoder.py', 'PYMODULE'),
  ('json.scanner', 'C:\\Python310\\lib\\json\\scanner.py', 'PYMODULE'),
  ('DataRecorder.style.cell_style',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DataRecorder\\style\\cell_style.py',
   'PYMODULE'),
  ('DataRecorder.style',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DataRecorder\\style\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('__future__', 'C:\\Python310\\lib\\__future__.py', 'PYMODULE'),
  ('et_xmlfile',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Python310\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Python310\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Python310\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Python310\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree', 'C:\\Python310\\lib\\xml\\etree\\__init__.py', 'PYMODULE'),
  ('lxml',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('html.entities', 'C:\\Python310\\lib\\html\\entities.py', 'PYMODULE'),
  ('lxml.html.html5parser',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('difflib', 'C:\\Python310\\lib\\difflib.py', 'PYMODULE'),
  ('lxml.html.defs',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.clean',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\html\\clean.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('webbrowser', 'C:\\Python310\\lib\\webbrowser.py', 'PYMODULE'),
  ('glob', 'C:\\Python310\\lib\\glob.py', 'PYMODULE'),
  ('lxml.doctestcompare',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('cgi', 'C:\\Python310\\lib\\cgi.py', 'PYMODULE'),
  ('doctest', 'C:\\Python310\\lib\\doctest.py', 'PYMODULE'),
  ('unittest', 'C:\\Python310\\lib\\unittest\\__init__.py', 'PYMODULE'),
  ('unittest.async_case',
   'C:\\Python310\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('asyncio', 'C:\\Python310\\lib\\asyncio\\__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Python310\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log', 'C:\\Python310\\lib\\asyncio\\log.py', 'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Python310\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Python310\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Python310\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Python310\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Python310\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads', 'C:\\Python310\\lib\\asyncio\\threads.py', 'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Python310\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams', 'C:\\Python310\\lib\\asyncio\\streams.py', 'PYMODULE'),
  ('asyncio.queues', 'C:\\Python310\\lib\\asyncio\\queues.py', 'PYMODULE'),
  ('asyncio.runners', 'C:\\Python310\\lib\\asyncio\\runners.py', 'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Python310\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Python310\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Python310\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Python310\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Python310\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent', 'C:\\Python310\\lib\\concurrent\\__init__.py', 'PYMODULE'),
  ('asyncio.trsock', 'C:\\Python310\\lib\\asyncio\\trsock.py', 'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Python310\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks', 'C:\\Python310\\lib\\asyncio\\tasks.py', 'PYMODULE'),
  ('asyncio.locks', 'C:\\Python310\\lib\\asyncio\\locks.py', 'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Python310\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.mixins', 'C:\\Python310\\lib\\asyncio\\mixins.py', 'PYMODULE'),
  ('asyncio.sslproto', 'C:\\Python310\\lib\\asyncio\\sslproto.py', 'PYMODULE'),
  ('asyncio.transports',
   'C:\\Python310\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Python310\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures', 'C:\\Python310\\lib\\asyncio\\futures.py', 'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Python310\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events', 'C:\\Python310\\lib\\asyncio\\events.py', 'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Python310\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Python310\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Python310\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Python310\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('unittest.signals', 'C:\\Python310\\lib\\unittest\\signals.py', 'PYMODULE'),
  ('unittest.main', 'C:\\Python310\\lib\\unittest\\main.py', 'PYMODULE'),
  ('unittest.runner', 'C:\\Python310\\lib\\unittest\\runner.py', 'PYMODULE'),
  ('unittest.loader', 'C:\\Python310\\lib\\unittest\\loader.py', 'PYMODULE'),
  ('unittest.suite', 'C:\\Python310\\lib\\unittest\\suite.py', 'PYMODULE'),
  ('unittest.case', 'C:\\Python310\\lib\\unittest\\case.py', 'PYMODULE'),
  ('unittest._log', 'C:\\Python310\\lib\\unittest\\_log.py', 'PYMODULE'),
  ('unittest.result', 'C:\\Python310\\lib\\unittest\\result.py', 'PYMODULE'),
  ('unittest.util', 'C:\\Python310\\lib\\unittest\\util.py', 'PYMODULE'),
  ('pdb', 'C:\\Python310\\lib\\pdb.py', 'PYMODULE'),
  ('pydoc', 'C:\\Python310\\lib\\pydoc.py', 'PYMODULE'),
  ('http.server', 'C:\\Python310\\lib\\http\\server.py', 'PYMODULE'),
  ('socketserver', 'C:\\Python310\\lib\\socketserver.py', 'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Python310\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data', 'C:\\Python310\\lib\\pydoc_data\\__init__.py', 'PYMODULE'),
  ('tty', 'C:\\Python310\\lib\\tty.py', 'PYMODULE'),
  ('sysconfig', 'C:\\Python310\\lib\\sysconfig.py', 'PYMODULE'),
  ('_aix_support', 'C:\\Python310\\lib\\_aix_support.py', 'PYMODULE'),
  ('_bootsubprocess', 'C:\\Python310\\lib\\_bootsubprocess.py', 'PYMODULE'),
  ('platform', 'C:\\Python310\\lib\\platform.py', 'PYMODULE'),
  ('code', 'C:\\Python310\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'C:\\Python310\\lib\\codeop.py', 'PYMODULE'),
  ('bdb', 'C:\\Python310\\lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'C:\\Python310\\lib\\cmd.py', 'PYMODULE'),
  ('lxml.cssselect',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('cssselect',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\cssselect\\__init__.py',
   'PYMODULE'),
  ('cssselect.xpath',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\cssselect\\xpath.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('cssselect.parser',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\cssselect\\parser.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('openpyxl',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.strings',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy._utils',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.char',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.rec',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy._core.records',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy.f2py',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('fileinput', 'C:\\Python310\\lib\\fileinput.py', 'PYMODULE'),
  ('numpy.f2py.symbolic',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.matlib',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('psutil',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._common',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('ipaddress', 'C:\\Python310\\lib\\ipaddress.py', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('DataRecorder.setter',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DataRecorder\\setter.py',
   'PYMODULE'),
  ('DataRecorder.base',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DataRecorder\\base.py',
   'PYMODULE'),
  ('DataRecorder.filler',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DataRecorder\\filler.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('DataRecorder.db_recorder',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DataRecorder\\db_recorder.py',
   'PYMODULE'),
  ('sqlite3', 'C:\\Python310\\lib\\sqlite3\\__init__.py', 'PYMODULE'),
  ('sqlite3.dump', 'C:\\Python310\\lib\\sqlite3\\dump.py', 'PYMODULE'),
  ('sqlite3.dbapi2', 'C:\\Python310\\lib\\sqlite3\\dbapi2.py', 'PYMODULE'),
  ('DataRecorder.byte_recorder',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DataRecorder\\byte_recorder.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('html', 'C:\\Python310\\lib\\html\\__init__.py', 'PYMODULE'),
  ('DrissionPage._functions.tools',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_functions\\tools.py',
   'PYMODULE'),
  ('DrissionPage._configs.options_manage',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_configs\\options_manage.py',
   'PYMODULE'),
  ('DrissionPage._configs', '-', 'PYMODULE'),
  ('configparser', 'C:\\Python310\\lib\\configparser.py', 'PYMODULE'),
  ('DrissionPage._units.cookies_setter',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_units\\cookies_setter.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('idna',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.http2',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('socks',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\socks.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Python310\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies', 'C:\\Python310\\lib\\http\\cookies.py', 'PYMODULE'),
  ('DrissionPage._functions.settings',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_functions\\settings.py',
   'PYMODULE'),
  ('DrissionPage._functions.texts',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_functions\\texts.py',
   'PYMODULE'),
  ('DrissionPage._functions.cookies',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_functions\\cookies.py',
   'PYMODULE'),
  ('tldextract',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\tldextract\\__init__.py',
   'PYMODULE'),
  ('tldextract.tldextract',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\tldextract\\tldextract.py',
   'PYMODULE'),
  ('tldextract.suffix_list',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\tldextract\\suffix_list.py',
   'PYMODULE'),
  ('requests_file',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\requests_file.py',
   'PYMODULE'),
  ('tldextract.remote',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\tldextract\\remote.py',
   'PYMODULE'),
  ('tldextract.cache',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\tldextract\\cache.py',
   'PYMODULE'),
  ('filelock',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\filelock\\__init__.py',
   'PYMODULE'),
  ('filelock.version',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\filelock\\version.py',
   'PYMODULE'),
  ('filelock.asyncio',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\filelock\\asyncio.py',
   'PYMODULE'),
  ('filelock._windows',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\filelock\\_windows.py',
   'PYMODULE'),
  ('filelock._util',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\filelock\\_util.py',
   'PYMODULE'),
  ('filelock._unix',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\filelock\\_unix.py',
   'PYMODULE'),
  ('filelock._soft',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\filelock\\_soft.py',
   'PYMODULE'),
  ('filelock._error',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\filelock\\_error.py',
   'PYMODULE'),
  ('filelock._api',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\filelock\\_api.py',
   'PYMODULE'),
  ('tldextract._version',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\tldextract\\_version.py',
   'PYMODULE'),
  ('DrissionPage._base.base',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_base\\base.py',
   'PYMODULE'),
  ('DrissionPage._base', '-', 'PYMODULE'),
  ('DrissionPage._functions.locator',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_functions\\locator.py',
   'PYMODULE'),
  ('DrissionPage._functions.by',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_functions\\by.py',
   'PYMODULE'),
  ('DrissionPage._functions.elements',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_functions\\elements.py',
   'PYMODULE'),
  ('DrissionPage._elements.none_element',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_elements\\none_element.py',
   'PYMODULE'),
  ('DrissionPage._elements', '-', 'PYMODULE'),
  ('DownloadKit',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DownloadKit\\__init__.py',
   'PYMODULE'),
  ('DownloadKit.downloadKit',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DownloadKit\\downloadKit.py',
   'PYMODULE'),
  ('DownloadKit.setter',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DownloadKit\\setter.py',
   'PYMODULE'),
  ('DownloadKit.mission',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DownloadKit\\mission.py',
   'PYMODULE'),
  ('DownloadKit._funcs',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DownloadKit\\_funcs.py',
   'PYMODULE'),
  ('DrissionPage._pages.session_page',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_pages\\session_page.py',
   'PYMODULE'),
  ('DrissionPage._elements.session_element',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_elements\\session_element.py',
   'PYMODULE'),
  ('DrissionPage._pages.chromium_page',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_pages\\chromium_page.py',
   'PYMODULE'),
  ('DrissionPage._units.waiter',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_units\\waiter.py',
   'PYMODULE'),
  ('DrissionPage._pages.chromium_base',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_pages\\chromium_base.py',
   'PYMODULE'),
  ('DrissionPage._units.states',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_units\\states.py',
   'PYMODULE'),
  ('DrissionPage._units.scroller',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_units\\scroller.py',
   'PYMODULE'),
  ('DrissionPage._units.screencast',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_units\\screencast.py',
   'PYMODULE'),
  ('DrissionPage._units.rect',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_units\\rect.py',
   'PYMODULE'),
  ('DrissionPage._units.listener',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_units\\listener.py',
   'PYMODULE'),
  ('DrissionPage._base.driver',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_base\\driver.py',
   'PYMODULE'),
  ('websocket',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\websocket\\__init__.py',
   'PYMODULE'),
  ('websocket._socket',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\websocket\\_socket.py',
   'PYMODULE'),
  ('websocket._utils',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\websocket\\_utils.py',
   'PYMODULE'),
  ('websocket._ssl_compat',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\websocket\\_ssl_compat.py',
   'PYMODULE'),
  ('websocket._exceptions',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\websocket\\_exceptions.py',
   'PYMODULE'),
  ('websocket._core',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\websocket\\_core.py',
   'PYMODULE'),
  ('websocket._http',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\websocket\\_http.py',
   'PYMODULE'),
  ('websocket._url',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\websocket\\_url.py',
   'PYMODULE'),
  ('websocket._handshake',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\websocket\\_handshake.py',
   'PYMODULE'),
  ('websocket._cookiejar',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\websocket\\_cookiejar.py',
   'PYMODULE'),
  ('websocket._app',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\websocket\\_app.py',
   'PYMODULE'),
  ('websocket._logging',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\websocket\\_logging.py',
   'PYMODULE'),
  ('websocket._abnf',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\websocket\\_abnf.py',
   'PYMODULE'),
  ('DrissionPage._units.console',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_units\\console.py',
   'PYMODULE'),
  ('DrissionPage._units.actions',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_units\\actions.py',
   'PYMODULE'),
  ('DrissionPage._functions.keys',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_functions\\keys.py',
   'PYMODULE'),
  ('DrissionPage._elements.chromium_element',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_elements\\chromium_element.py',
   'PYMODULE'),
  ('DrissionPage._pages.chromium_frame',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_pages\\chromium_frame.py',
   'PYMODULE'),
  ('DrissionPage._units.selector',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_units\\selector.py',
   'PYMODULE'),
  ('DrissionPage._units.clicker',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_units\\clicker.py',
   'PYMODULE'),
  ('DrissionPage._units.downloader',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_units\\downloader.py',
   'PYMODULE'),
  ('DrissionPage._configs.session_options',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_configs\\session_options.py',
   'PYMODULE'),
  ('DrissionPage._configs.chromium_options',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_configs\\chromium_options.py',
   'PYMODULE'),
  ('DrissionPage._base.chromium',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_base\\chromium.py',
   'PYMODULE'),
  ('DrissionPage._pages.mix_tab',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_pages\\mix_tab.py',
   'PYMODULE'),
  ('DrissionPage._pages.chromium_tab',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_pages\\chromium_tab.py',
   'PYMODULE'),
  ('DrissionPage._functions.browser',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\DrissionPage\\_functions\\browser.py',
   'PYMODULE'),
  ('au', 'C:\\Users\\<USER>\\Desktop\\unlock1\\au.py', 'PYMODULE'),
  ('zmailcode',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\zmailcode.py',
   'PYMODULE'),
  ('zmail',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\zmail\\__init__.py',
   'PYMODULE'),
  ('zmail.settings',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\zmail\\settings.py',
   'PYMODULE'),
  ('zmail.api',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\zmail\\api.py',
   'PYMODULE'),
  ('zmail.utils',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\zmail\\utils.py',
   'PYMODULE'),
  ('zmail.structures',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\zmail\\structures.py',
   'PYMODULE'),
  ('zmail.compat',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\zmail\\compat.py',
   'PYMODULE'),
  ('zmail.parser',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\zmail\\parser.py',
   'PYMODULE'),
  ('zmail.exceptions',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\zmail\\exceptions.py',
   'PYMODULE'),
  ('zmail.helpers',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\zmail\\helpers.py',
   'PYMODULE'),
  ('zmail.server',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\zmail\\server.py',
   'PYMODULE'),
  ('zmail.mime',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\zmail\\mime.py',
   'PYMODULE'),
  ('email.mime.text', 'C:\\Python310\\lib\\email\\mime\\text.py', 'PYMODULE'),
  ('email.mime', 'C:\\Python310\\lib\\email\\mime\\__init__.py', 'PYMODULE'),
  ('email.mime.nonmultipart',
   'C:\\Python310\\lib\\email\\mime\\nonmultipart.py',
   'PYMODULE'),
  ('email.mime.multipart',
   'C:\\Python310\\lib\\email\\mime\\multipart.py',
   'PYMODULE'),
  ('email.mime.base', 'C:\\Python310\\lib\\email\\mime\\base.py', 'PYMODULE'),
  ('zmail.abc',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\zmail\\abc.py',
   'PYMODULE'),
  ('smtplib', 'C:\\Python310\\lib\\smtplib.py', 'PYMODULE'),
  ('zmail.info',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\zmail\\info.py',
   'PYMODULE'),
  ('poplib', 'C:\\Python310\\lib\\poplib.py', 'PYMODULE'),
  ('do', 'C:\\Users\\<USER>\\Desktop\\unlock1\\do.py', 'PYMODULE'),
  ('selenium.webdriver.common.by',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\by.py',
   'PYMODULE'),
  ('selenium.webdriver.common',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.service',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\service.py',
   'PYMODULE'),
  ('selenium.types',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\types.py',
   'PYMODULE'),
  ('selenium',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\__init__.py',
   'PYMODULE'),
  ('selenium.common.exceptions',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\common\\exceptions.py',
   'PYMODULE'),
  ('selenium.common',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\common\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.utils',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\utils.py',
   'PYMODULE'),
  ('selenium.webdriver.common.keys',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\keys.py',
   'PYMODULE'),
  ('selenium.webdriver',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.webdriver',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.driver_finder',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\driver_finder.py',
   'PYMODULE'),
  ('selenium.webdriver.common.selenium_manager',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\selenium_manager.py',
   'PYMODULE'),
  ('selenium.webdriver.common.options',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.service',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.options',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.webdriver',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.service',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.options',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.webdriver',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\safari\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.safari',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\safari\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.remote_connection',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\safari\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.remote_connection',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\remote\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.errorhandler',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\remote\\errorhandler.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.command',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\remote\\command.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.utils',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\remote\\utils.py',
   'PYMODULE'),
  ('selenium.webdriver.remote',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\remote\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.client_config',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\remote\\client_config.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.service',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\safari\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.options',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\safari\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.webdriver',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\remote\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.support.wait',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\support\\wait.py',
   'PYMODULE'),
  ('selenium.webdriver.support',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\support\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.support.expected_conditions',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\support\\expected_conditions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.alert',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\alert.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.remote_connection',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\firefox\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\firefox\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.remote_connection',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\edge\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.edge',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\edge\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.remote_connection',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\chromium\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\chromium\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.service',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\chromium\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.remote_connection',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\chrome\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\chrome\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.websocket_connection',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\remote\\websocket_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.webelement',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\remote\\webelement.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.switch_to',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\remote\\switch_to.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.shadowroot',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\remote\\shadowroot.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.script_key',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\remote\\script_key.py',
   'PYMODULE'),
  ('uuid', 'C:\\Python310\\lib\\uuid.py', 'PYMODULE'),
  ('selenium.webdriver.remote.mobile',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\remote\\mobile.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.locator_converter',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\remote\\locator_converter.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.file_detector',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\remote\\file_detector.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.fedcm',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\remote\\fedcm.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.bidi_connection',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\remote\\bidi_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.common.fedcm.dialog',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\fedcm\\dialog.py',
   'PYMODULE'),
  ('selenium.webdriver.common.fedcm',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\fedcm\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.fedcm.account',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\fedcm\\account.py',
   'PYMODULE'),
  ('selenium.webdriver.support.relative_locator',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\support\\relative_locator.py',
   'PYMODULE'),
  ('selenium.webdriver.common.virtual_authenticator',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\virtual_authenticator.py',
   'PYMODULE'),
  ('selenium.webdriver.common.timeouts',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\timeouts.py',
   'PYMODULE'),
  ('selenium.webdriver.common.print_page_options',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\print_page_options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.webextension',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\webextension.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.common',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\common.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.storage',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\storage.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.session',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\session.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.script',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\script.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.log',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\log.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.permissions',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\permissions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.network',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\network.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.browsing_context',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\browsing_context.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.browser',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\browser.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.webdriver',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\ie\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.ie',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\ie\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.service',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\ie\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.options',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\ie\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.webdriver',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\firefox\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.service',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\firefox\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.options',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\firefox\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.firefox_binary',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\firefox\\firefox_binary.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.firefox_profile',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\firefox\\firefox_profile.py',
   'PYMODULE'),
  ('xml.dom.minidom', 'C:\\Python310\\lib\\xml\\dom\\minidom.py', 'PYMODULE'),
  ('xml.dom.pulldom', 'C:\\Python310\\lib\\xml\\dom\\pulldom.py', 'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Python310\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Python310\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Python310\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Python310\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg', 'C:\\Python310\\lib\\xml\\dom\\domreg.py', 'PYMODULE'),
  ('xml.dom', 'C:\\Python310\\lib\\xml\\dom\\__init__.py', 'PYMODULE'),
  ('selenium.webdriver.edge.webdriver',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\edge\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.webdriver',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\chromium\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.service',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\edge\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.options',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\edge\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.options',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\chromium\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.proxy',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\proxy.py',
   'PYMODULE'),
  ('selenium.webdriver.common.desired_capabilities',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\desired_capabilities.py',
   'PYMODULE'),
  ('selenium.webdriver.common.action_chains',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\action_chains.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.wheel_input',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\wheel_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.input_device',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\input_device.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.interaction',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\interaction.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.pointer_input',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\pointer_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.key_input',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.action_builder',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\action_builder.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.wheel_actions',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\wheel_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.pointer_actions',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\pointer_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.mouse_button',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\mouse_button.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.key_actions',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.webdriver',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\chrome\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.service',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\chrome\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.options',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\chrome\\options.py',
   'PYMODULE'),
  ('sb', 'C:\\Users\\<USER>\\Desktop\\unlock1\\sb.py', 'PYMODULE'),
  ('_py_abc', 'C:\\Python310\\lib\\_py_abc.py', 'PYMODULE'),
  ('stringprep', 'C:\\Python310\\lib\\stringprep.py', 'PYMODULE'),
  ('tracemalloc', 'C:\\Python310\\lib\\tracemalloc.py', 'PYMODULE'),
  ('fileimei',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\fileimei.py',
   'PYMODULE'),
  ('wx',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\wx\\__init__.py',
   'PYMODULE'),
  ('wx.core',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\wx\\core.py',
   'PYMODULE'),
  ('wx.adv',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\wx\\adv.py',
   'PYMODULE'),
  ('wx.html',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\wx\\html.py',
   'PYMODULE'),
  ('wx.msw',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\wx\\msw.py',
   'PYMODULE'),
  ('wx.lib.colourutils',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\wx\\lib\\colourutils.py',
   'PYMODULE'),
  ('wx.lib',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\wx\\lib\\__init__.py',
   'PYMODULE'),
  ('wx.__version__',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\wx\\__version__.py',
   'PYMODULE'),
  ('update', 'C:\\Users\\<USER>\\Desktop\\unlock1\\update.py', 'PYMODULE'),
  ('tkinter.ttk', 'C:\\Python310\\lib\\tkinter\\ttk.py', 'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Python310\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Python310\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter', 'C:\\Python310\\lib\\tkinter\\__init__.py', 'PYMODULE'),
  ('tkinter.constants',
   'C:\\Python310\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('txt', 'C:\\Users\\<USER>\\Desktop\\unlock1\\txt.py', 'PYMODULE'),
  ('wx.stc',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\wx\\stc.py',
   'PYMODULE'),
  ('wx.lib.agw.hyperlink',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\wx\\lib\\agw\\hyperlink.py',
   'PYMODULE'),
  ('wx.lib.agw',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\wx\\lib\\agw\\__init__.py',
   'PYMODULE'),
  ('hashlib', 'C:\\Python310\\lib\\hashlib.py', 'PYMODULE'),
  ('socket', 'C:\\Python310\\lib\\socket.py', 'PYMODULE'),
  ('threading', 'C:\\Python310\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local', 'C:\\Python310\\lib\\_threading_local.py', 'PYMODULE'),
  ('subprocess', 'C:\\Python310\\lib\\subprocess.py', 'PYMODULE')],
 [('selenium\\webdriver\\common\\windows\\selenium-manager.exe',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\windows\\selenium-manager.exe',
   'BINARY'),
  ('python310.dll', 'C:\\Python310\\python310.dll', 'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('_multiprocessing.pyd',
   'C:\\Python310\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'C:\\Python310\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'C:\\Python310\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('unicodedata.pyd', 'C:\\Python310\\DLLs\\unicodedata.pyd', 'EXTENSION'),
  ('_decimal.pyd', 'C:\\Python310\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'C:\\Python310\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('select.pyd', 'C:\\Python310\\DLLs\\select.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'C:\\Python310\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Python310\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'C:\\Python310\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\Python310\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_elementtree.pyd', 'C:\\Python310\\DLLs\\_elementtree.pyd', 'EXTENSION'),
  ('lxml\\etree.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\etree.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\_elementpath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\sax.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\objectify.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\html\\diff.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\_difflib.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\html\\_difflib.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd', 'C:\\Python310\\DLLs\\_overlapped.pyd', 'EXTENSION'),
  ('_asyncio.pyd', 'C:\\Python310\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_socket.pyd', 'C:\\Python310\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('lxml\\builder.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\builder.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\charset_normalizer\\md.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\random\\_philox.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\random\\_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\random\\_common.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd', 'C:\\Python310\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('_uuid.pyd', 'C:\\Python310\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('wx\\_adv.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\wx\\_adv.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('wx\\_html.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\wx\\_html.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('wx\\_msw.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\wx\\_msw.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('wx\\siplib.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\wx\\siplib.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('wx\\_core.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\wx\\_core.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter.pyd', 'C:\\Python310\\DLLs\\_tkinter.pyd', 'EXTENSION'),
  ('wx\\_stc.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\wx\\_stc.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll', 'C:\\Python310\\VCRUNTIME140.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'C:\\Python310\\VCRUNTIME140_1.dll', 'BINARY'),
  ('libssl-1_1.dll', 'C:\\Python310\\DLLs\\libssl-1_1.dll', 'BINARY'),
  ('libcrypto-1_1.dll', 'C:\\Python310\\DLLs\\libcrypto-1_1.dll', 'BINARY'),
  ('libffi-7.dll', 'C:\\Python310\\DLLs\\libffi-7.dll', 'BINARY'),
  ('python3.dll', 'C:\\Python310\\python3.dll', 'BINARY'),
  ('sqlite3.dll', 'C:\\Python310\\DLLs\\sqlite3.dll', 'BINARY'),
  ('wx\\wxbase32u_vc140_x64.dll',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\wx\\wxbase32u_vc140_x64.dll',
   'BINARY'),
  ('wx\\VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\wx\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('wx\\wxmsw32u_core_vc140_x64.dll',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\wx\\wxmsw32u_core_vc140_x64.dll',
   'BINARY'),
  ('wx\\VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\wx\\VCRUNTIME140.dll',
   'BINARY'),
  ('wx\\wxmsw32u_html_vc140_x64.dll',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\wx\\wxmsw32u_html_vc140_x64.dll',
   'BINARY'),
  ('wx\\wxbase32u_net_vc140_x64.dll',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\wx\\wxbase32u_net_vc140_x64.dll',
   'BINARY'),
  ('wx\\MSVCP140.dll',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\wx\\MSVCP140.dll',
   'BINARY'),
  ('tk86t.dll', 'C:\\Python310\\DLLs\\tk86t.dll', 'BINARY'),
  ('tcl86t.dll', 'C:\\Python310\\DLLs\\tcl86t.dll', 'BINARY'),
  ('wx\\wxmsw32u_stc_vc140_x64.dll',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\wx\\wxmsw32u_stc_vc140_x64.dll',
   'BINARY')],
 [],
 [],
 [('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\remote\\isDisplayed.js',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\remote\\isDisplayed.js',
   'DATA'),
  ('selenium\\webdriver\\remote\\findElements.js',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\remote\\findElements.js',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v136\\py.typed',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v136\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\macos\\selenium-manager',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\macos\\selenium-manager',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v137\\py.typed',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v137\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\common\\mutation-listener.js',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\mutation-listener.js',
   'DATA'),
  ('selenium\\webdriver\\common\\devtools\\v138\\py.typed',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\devtools\\v138\\py.typed',
   'DATA'),
  ('selenium\\py.typed',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\py.typed',
   'DATA'),
  ('selenium\\webdriver\\remote\\getAttribute.js',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\remote\\getAttribute.js',
   'DATA'),
  ('selenium\\webdriver\\common\\linux\\selenium-manager',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\common\\linux\\selenium-manager',
   'DATA'),
  ('selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\selenium\\webdriver\\firefox\\webdriver_prefs.json',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'C:\\Python310\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\CET', 'C:\\Python310\\tcl\\tcl8.6\\tzdata\\CET', 'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tk_data\\msgs\\de.msg', 'C:\\Python310\\tcl\\tk8.6\\msgs\\de.msg', 'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT', 'C:\\Python310\\tcl\\tcl8.6\\tzdata\\GMT', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg', 'C:\\Python310\\tcl\\tk8.6\\msgs\\ru.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg', 'C:\\Python310\\tcl\\tk8.6\\msgs\\sv.msg', 'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'C:\\Python310\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl', 'C:\\Python310\\tcl\\tk8.6\\mkpsenc.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tk_data\\text.tcl', 'C:\\Python310\\tcl\\tk8.6\\text.tcl', 'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'C:\\Python310\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tk_data\\comdlg.tcl', 'C:\\Python310\\tcl\\tk8.6\\comdlg.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tk_data\\palette.tcl', 'C:\\Python310\\tcl\\tk8.6\\palette.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'C:\\Python310\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC', 'C:\\Python310\\tcl\\tcl8.6\\tzdata\\UTC', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'C:\\Python310\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg', 'C:\\Python310\\tcl\\tk8.6\\msgs\\fr.msg', 'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tk_data\\scrlbar.tcl', 'C:\\Python310\\tcl\\tk8.6\\scrlbar.tcl', 'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'C:\\Python310\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\history.tcl', 'C:\\Python310\\tcl\\tcl8.6\\history.tcl', 'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'C:\\Python310\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'C:\\Python310\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tk_data\\msgs\\it.msg', 'C:\\Python310\\tcl\\tk8.6\\msgs\\it.msg', 'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'C:\\Python310\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'C:\\Python310\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\safe.tcl', 'C:\\Python310\\tcl\\tcl8.6\\safe.tcl', 'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'C:\\Python310\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.5.tm',
   'C:\\Python310\\tcl\\tcl8\\8.6\\http-2.9.5.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg', 'C:\\Python310\\tcl\\tk8.6\\msgs\\pl.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'C:\\Python310\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\parray.tcl', 'C:\\Python310\\tcl\\tcl8.6\\parray.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'C:\\Python310\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tk_data\\bgerror.tcl', 'C:\\Python310\\tcl\\tk8.6\\bgerror.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tk_data\\xmfbox.tcl', 'C:\\Python310\\tcl\\tk8.6\\xmfbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT', 'C:\\Python310\\tcl\\tcl8.6\\tzdata\\UCT', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg', 'C:\\Python310\\tcl\\tk8.6\\msgs\\eo.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'C:\\Python310\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tk_data\\tk.tcl', 'C:\\Python310\\tcl\\tk8.6\\tk.tcl', 'DATA'),
  ('_tk_data\\msgs\\cs.msg', 'C:\\Python310\\tcl\\tk8.6\\msgs\\cs.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tk_data\\license.terms',
   'C:\\Python310\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tk_data\\listbox.tcl', 'C:\\Python310\\tcl\\tk8.6\\listbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tk_data\\tearoff.tcl', 'C:\\Python310\\tcl\\tk8.6\\tearoff.tcl', 'DATA'),
  ('_tk_data\\tkfbox.tcl', 'C:\\Python310\\tcl\\tk8.6\\tkfbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'C:\\Python310\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'C:\\Python310\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\WET', 'C:\\Python310\\tcl\\tcl8.6\\tzdata\\WET', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl', 'C:\\Python310\\tcl\\tk8.6\\pkgIndex.tcl', 'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tk_data\\safetk.tcl', 'C:\\Python310\\tcl\\tk8.6\\safetk.tcl', 'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'C:\\Python310\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg', 'C:\\Python310\\tcl\\tk8.6\\msgs\\pt.msg', 'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'C:\\Python310\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC', 'C:\\Python310\\tcl\\tcl8.6\\tzdata\\ROC', 'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'C:\\Python310\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tk_data\\msgs\\el.msg', 'C:\\Python310\\tcl\\tk8.6\\msgs\\el.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tk_data\\msgs\\da.msg', 'C:\\Python310\\tcl\\tk8.6\\msgs\\da.msg', 'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg', 'C:\\Python310\\tcl\\tk8.6\\msgs\\nl.msg', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'C:\\Python310\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\init.tcl', 'C:\\Python310\\tcl\\tcl8.6\\init.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\package.tcl', 'C:\\Python310\\tcl\\tcl8.6\\package.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'C:\\Python310\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tk_data\\optMenu.tcl', 'C:\\Python310\\tcl\\tk8.6\\optMenu.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tk_data\\scale.tcl', 'C:\\Python310\\tcl\\tk8.6\\scale.tcl', 'DATA'),
  ('_tk_data\\obsolete.tcl', 'C:\\Python310\\tcl\\tk8.6\\obsolete.tcl', 'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'C:\\Python310\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tk_data\\entry.tcl', 'C:\\Python310\\tcl\\tk8.6\\entry.tcl', 'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tk_data\\button.tcl', 'C:\\Python310\\tcl\\tk8.6\\button.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tk_data\\msgs\\en.msg', 'C:\\Python310\\tcl\\tk8.6\\msgs\\en.msg', 'DATA'),
  ('_tk_data\\iconlist.tcl', 'C:\\Python310\\tcl\\tk8.6\\iconlist.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\MET', 'C:\\Python310\\tcl\\tcl8.6\\tzdata\\MET', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'C:\\Python310\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\clock.tcl', 'C:\\Python310\\tcl\\tcl8.6\\clock.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\EET', 'C:\\Python310\\tcl\\tcl8.6\\tzdata\\EET', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'C:\\Python310\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tk_data\\console.tcl', 'C:\\Python310\\tcl\\tk8.6\\console.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'C:\\Python310\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'C:\\Python310\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'C:\\Python310\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\auto.tcl', 'C:\\Python310\\tcl\\tcl8.6\\auto.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'C:\\Python310\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tm.tcl', 'C:\\Python310\\tcl\\tcl8.6\\tm.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\NZ', 'C:\\Python310\\tcl\\tcl8.6\\tzdata\\NZ', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'C:\\Python310\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\GB', 'C:\\Python310\\tcl\\tcl8.6\\tzdata\\GB', 'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tk_data\\tclIndex', 'C:\\Python310\\tcl\\tk8.6\\tclIndex', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\East-Saskatchewan',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'C:\\Python310\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'C:\\Python310\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tk_data\\clrpick.tcl', 'C:\\Python310\\tcl\\tk8.6\\clrpick.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tk_data\\msgs\\es.msg', 'C:\\Python310\\tcl\\tk8.6\\msgs\\es.msg', 'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK', 'C:\\Python310\\tcl\\tcl8.6\\tzdata\\ROK', 'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'C:\\Python310\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'C:\\Python310\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.18.tm',
   'C:\\Python310\\tcl\\tcl8\\8.4\\platform-1.0.18.tm',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'C:\\Python310\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC', 'C:\\Python310\\tcl\\tcl8.6\\tzdata\\PRC', 'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'C:\\Python310\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tk_data\\images\\README',
   'C:\\Python310\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tk_data\\msgbox.tcl', 'C:\\Python310\\tcl\\tk8.6\\msgbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg', 'C:\\Python310\\tcl\\tk8.6\\msgs\\hu.msg', 'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.3.tm',
   'C:\\Python310\\tcl\\tcl8\\8.5\\tcltest-2.5.3.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'C:\\Python310\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\word.tcl', 'C:\\Python310\\tcl\\tcl8.6\\word.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'C:\\Python310\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'C:\\Python310\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'C:\\Python310\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'C:\\Python310\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tk_data\\icons.tcl', 'C:\\Python310\\tcl\\tk8.6\\icons.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'C:\\Python310\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'C:\\Python310\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tk_data\\spinbox.tcl', 'C:\\Python310\\tcl\\tk8.6\\spinbox.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific-New',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'C:\\Python310\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl', 'C:\\Python310\\tcl\\tk8.6\\ttk\\ttk.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\MST', 'C:\\Python310\\tcl\\tcl8.6\\tzdata\\MST', 'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'C:\\Python310\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tk_data\\focus.tcl', 'C:\\Python310\\tcl\\tk8.6\\focus.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tk_data\\menu.tcl', 'C:\\Python310\\tcl\\tk8.6\\menu.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tclIndex', 'C:\\Python310\\tcl\\tcl8.6\\tclIndex', 'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tk_data\\dialog.tcl', 'C:\\Python310\\tcl\\tk8.6\\dialog.tcl', 'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\HST', 'C:\\Python310\\tcl\\tcl8.6\\tzdata\\HST', 'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'C:\\Python310\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\EST', 'C:\\Python310\\tcl\\tcl8.6\\tzdata\\EST', 'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'C:\\Python310\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'C:\\Python310\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'C:\\Python310\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'C:\\Python310\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'C:\\Python310\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('numpy-2.2.6.dist-info\\METADATA',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy-2.2.6.dist-info\\METADATA',
   'DATA'),
  ('numpy-2.2.6.dist-info\\RECORD',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy-2.2.6.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.6.dist-info\\DELVEWHEEL',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy-2.2.6.dist-info\\DELVEWHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy-2.2.6.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy-2.2.6.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy-2.2.6.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.6.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\run\\lib\\site-packages\\numpy-2.2.6.dist-info\\LICENSE.txt',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\unlock1\\build\\run\\base_library.zip',
   'DATA')],
 [('_weakrefset', 'C:\\Python310\\lib\\_weakrefset.py', 'PYMODULE'),
  ('reprlib', 'C:\\Python310\\lib\\reprlib.py', 'PYMODULE'),
  ('types', 'C:\\Python310\\lib\\types.py', 'PYMODULE'),
  ('functools', 'C:\\Python310\\lib\\functools.py', 'PYMODULE'),
  ('abc', 'C:\\Python310\\lib\\abc.py', 'PYMODULE'),
  ('linecache', 'C:\\Python310\\lib\\linecache.py', 'PYMODULE'),
  ('keyword', 'C:\\Python310\\lib\\keyword.py', 'PYMODULE'),
  ('copyreg', 'C:\\Python310\\lib\\copyreg.py', 'PYMODULE'),
  ('codecs', 'C:\\Python310\\lib\\codecs.py', 'PYMODULE'),
  ('collections.abc', 'C:\\Python310\\lib\\collections\\abc.py', 'PYMODULE'),
  ('collections', 'C:\\Python310\\lib\\collections\\__init__.py', 'PYMODULE'),
  ('_collections_abc', 'C:\\Python310\\lib\\_collections_abc.py', 'PYMODULE'),
  ('operator', 'C:\\Python310\\lib\\operator.py', 'PYMODULE'),
  ('ntpath', 'C:\\Python310\\lib\\ntpath.py', 'PYMODULE'),
  ('io', 'C:\\Python310\\lib\\io.py', 'PYMODULE'),
  ('locale', 'C:\\Python310\\lib\\locale.py', 'PYMODULE'),
  ('sre_parse', 'C:\\Python310\\lib\\sre_parse.py', 'PYMODULE'),
  ('stat', 'C:\\Python310\\lib\\stat.py', 'PYMODULE'),
  ('traceback', 'C:\\Python310\\lib\\traceback.py', 'PYMODULE'),
  ('heapq', 'C:\\Python310\\lib\\heapq.py', 'PYMODULE'),
  ('sre_constants', 'C:\\Python310\\lib\\sre_constants.py', 'PYMODULE'),
  ('enum', 'C:\\Python310\\lib\\enum.py', 'PYMODULE'),
  ('os', 'C:\\Python310\\lib\\os.py', 'PYMODULE'),
  ('posixpath', 'C:\\Python310\\lib\\posixpath.py', 'PYMODULE'),
  ('sre_compile', 'C:\\Python310\\lib\\sre_compile.py', 'PYMODULE'),
  ('weakref', 'C:\\Python310\\lib\\weakref.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'C:\\Python310\\lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'C:\\Python310\\lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'C:\\Python310\\lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8', 'C:\\Python310\\lib\\encodings\\utf_8.py', 'PYMODULE'),
  ('encodings.utf_7', 'C:\\Python310\\lib\\encodings\\utf_7.py', 'PYMODULE'),
  ('encodings.utf_32_le',
   'C:\\Python310\\lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'C:\\Python310\\lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32', 'C:\\Python310\\lib\\encodings\\utf_32.py', 'PYMODULE'),
  ('encodings.utf_16_le',
   'C:\\Python310\\lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'C:\\Python310\\lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16', 'C:\\Python310\\lib\\encodings\\utf_16.py', 'PYMODULE'),
  ('encodings.unicode_escape',
   'C:\\Python310\\lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'C:\\Python310\\lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'C:\\Python310\\lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'C:\\Python310\\lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'C:\\Python310\\lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'C:\\Python310\\lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13', 'C:\\Python310\\lib\\encodings\\rot_13.py', 'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'C:\\Python310\\lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'C:\\Python310\\lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'C:\\Python310\\lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'C:\\Python310\\lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos', 'C:\\Python310\\lib\\encodings\\palmos.py', 'PYMODULE'),
  ('encodings.oem', 'C:\\Python310\\lib\\encodings\\oem.py', 'PYMODULE'),
  ('encodings.mbcs', 'C:\\Python310\\lib\\encodings\\mbcs.py', 'PYMODULE'),
  ('encodings.mac_turkish',
   'C:\\Python310\\lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'C:\\Python310\\lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'C:\\Python310\\lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'C:\\Python310\\lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'C:\\Python310\\lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'C:\\Python310\\lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'C:\\Python310\\lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'C:\\Python310\\lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'C:\\Python310\\lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'C:\\Python310\\lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'C:\\Python310\\lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048', 'C:\\Python310\\lib\\encodings\\kz1048.py', 'PYMODULE'),
  ('encodings.koi8_u', 'C:\\Python310\\lib\\encodings\\koi8_u.py', 'PYMODULE'),
  ('encodings.koi8_t', 'C:\\Python310\\lib\\encodings\\koi8_t.py', 'PYMODULE'),
  ('encodings.koi8_r', 'C:\\Python310\\lib\\encodings\\koi8_r.py', 'PYMODULE'),
  ('encodings.johab', 'C:\\Python310\\lib\\encodings\\johab.py', 'PYMODULE'),
  ('encodings.iso8859_9',
   'C:\\Python310\\lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'C:\\Python310\\lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'C:\\Python310\\lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'C:\\Python310\\lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'C:\\Python310\\lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'C:\\Python310\\lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'C:\\Python310\\lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'C:\\Python310\\lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'C:\\Python310\\lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'C:\\Python310\\lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'C:\\Python310\\lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'C:\\Python310\\lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'C:\\Python310\\lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'C:\\Python310\\lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'C:\\Python310\\lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'C:\\Python310\\lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'C:\\Python310\\lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'C:\\Python310\\lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'C:\\Python310\\lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'C:\\Python310\\lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'C:\\Python310\\lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'C:\\Python310\\lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna', 'C:\\Python310\\lib\\encodings\\idna.py', 'PYMODULE'),
  ('encodings.hz', 'C:\\Python310\\lib\\encodings\\hz.py', 'PYMODULE'),
  ('encodings.hp_roman8',
   'C:\\Python310\\lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'C:\\Python310\\lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk', 'C:\\Python310\\lib\\encodings\\gbk.py', 'PYMODULE'),
  ('encodings.gb2312', 'C:\\Python310\\lib\\encodings\\gb2312.py', 'PYMODULE'),
  ('encodings.gb18030',
   'C:\\Python310\\lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr', 'C:\\Python310\\lib\\encodings\\euc_kr.py', 'PYMODULE'),
  ('encodings.euc_jp', 'C:\\Python310\\lib\\encodings\\euc_jp.py', 'PYMODULE'),
  ('encodings.euc_jisx0213',
   'C:\\Python310\\lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'C:\\Python310\\lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950', 'C:\\Python310\\lib\\encodings\\cp950.py', 'PYMODULE'),
  ('encodings.cp949', 'C:\\Python310\\lib\\encodings\\cp949.py', 'PYMODULE'),
  ('encodings.cp932', 'C:\\Python310\\lib\\encodings\\cp932.py', 'PYMODULE'),
  ('encodings.cp875', 'C:\\Python310\\lib\\encodings\\cp875.py', 'PYMODULE'),
  ('encodings.cp874', 'C:\\Python310\\lib\\encodings\\cp874.py', 'PYMODULE'),
  ('encodings.cp869', 'C:\\Python310\\lib\\encodings\\cp869.py', 'PYMODULE'),
  ('encodings.cp866', 'C:\\Python310\\lib\\encodings\\cp866.py', 'PYMODULE'),
  ('encodings.cp865', 'C:\\Python310\\lib\\encodings\\cp865.py', 'PYMODULE'),
  ('encodings.cp864', 'C:\\Python310\\lib\\encodings\\cp864.py', 'PYMODULE'),
  ('encodings.cp863', 'C:\\Python310\\lib\\encodings\\cp863.py', 'PYMODULE'),
  ('encodings.cp862', 'C:\\Python310\\lib\\encodings\\cp862.py', 'PYMODULE'),
  ('encodings.cp861', 'C:\\Python310\\lib\\encodings\\cp861.py', 'PYMODULE'),
  ('encodings.cp860', 'C:\\Python310\\lib\\encodings\\cp860.py', 'PYMODULE'),
  ('encodings.cp858', 'C:\\Python310\\lib\\encodings\\cp858.py', 'PYMODULE'),
  ('encodings.cp857', 'C:\\Python310\\lib\\encodings\\cp857.py', 'PYMODULE'),
  ('encodings.cp856', 'C:\\Python310\\lib\\encodings\\cp856.py', 'PYMODULE'),
  ('encodings.cp855', 'C:\\Python310\\lib\\encodings\\cp855.py', 'PYMODULE'),
  ('encodings.cp852', 'C:\\Python310\\lib\\encodings\\cp852.py', 'PYMODULE'),
  ('encodings.cp850', 'C:\\Python310\\lib\\encodings\\cp850.py', 'PYMODULE'),
  ('encodings.cp775', 'C:\\Python310\\lib\\encodings\\cp775.py', 'PYMODULE'),
  ('encodings.cp737', 'C:\\Python310\\lib\\encodings\\cp737.py', 'PYMODULE'),
  ('encodings.cp720', 'C:\\Python310\\lib\\encodings\\cp720.py', 'PYMODULE'),
  ('encodings.cp500', 'C:\\Python310\\lib\\encodings\\cp500.py', 'PYMODULE'),
  ('encodings.cp437', 'C:\\Python310\\lib\\encodings\\cp437.py', 'PYMODULE'),
  ('encodings.cp424', 'C:\\Python310\\lib\\encodings\\cp424.py', 'PYMODULE'),
  ('encodings.cp273', 'C:\\Python310\\lib\\encodings\\cp273.py', 'PYMODULE'),
  ('encodings.cp1258', 'C:\\Python310\\lib\\encodings\\cp1258.py', 'PYMODULE'),
  ('encodings.cp1257', 'C:\\Python310\\lib\\encodings\\cp1257.py', 'PYMODULE'),
  ('encodings.cp1256', 'C:\\Python310\\lib\\encodings\\cp1256.py', 'PYMODULE'),
  ('encodings.cp1255', 'C:\\Python310\\lib\\encodings\\cp1255.py', 'PYMODULE'),
  ('encodings.cp1254', 'C:\\Python310\\lib\\encodings\\cp1254.py', 'PYMODULE'),
  ('encodings.cp1253', 'C:\\Python310\\lib\\encodings\\cp1253.py', 'PYMODULE'),
  ('encodings.cp1252', 'C:\\Python310\\lib\\encodings\\cp1252.py', 'PYMODULE'),
  ('encodings.cp1251', 'C:\\Python310\\lib\\encodings\\cp1251.py', 'PYMODULE'),
  ('encodings.cp1250', 'C:\\Python310\\lib\\encodings\\cp1250.py', 'PYMODULE'),
  ('encodings.cp1140', 'C:\\Python310\\lib\\encodings\\cp1140.py', 'PYMODULE'),
  ('encodings.cp1125', 'C:\\Python310\\lib\\encodings\\cp1125.py', 'PYMODULE'),
  ('encodings.cp1026', 'C:\\Python310\\lib\\encodings\\cp1026.py', 'PYMODULE'),
  ('encodings.cp1006', 'C:\\Python310\\lib\\encodings\\cp1006.py', 'PYMODULE'),
  ('encodings.cp037', 'C:\\Python310\\lib\\encodings\\cp037.py', 'PYMODULE'),
  ('encodings.charmap',
   'C:\\Python310\\lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'C:\\Python310\\lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'C:\\Python310\\lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5', 'C:\\Python310\\lib\\encodings\\big5.py', 'PYMODULE'),
  ('encodings.base64_codec',
   'C:\\Python310\\lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii', 'C:\\Python310\\lib\\encodings\\ascii.py', 'PYMODULE'),
  ('encodings.aliases',
   'C:\\Python310\\lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings', 'C:\\Python310\\lib\\encodings\\__init__.py', 'PYMODULE'),
  ('genericpath', 'C:\\Python310\\lib\\genericpath.py', 'PYMODULE'),
  ('warnings', 'C:\\Python310\\lib\\warnings.py', 'PYMODULE'),
  ('re', 'C:\\Python310\\lib\\re.py', 'PYMODULE')])
