# This file is generated by wxPython's SIP generator.  Do not edit by hand.
#
# Copyright: (c) 2020 by Total Control Software
# License:   wxWindows License

"""
The Grid and related classes in this module provide functionality similar to a
spreadsheet, where the application can display rows and columns of data of
various types, which the user can edit and otherwise interact with.
"""

from ._grid import *

import wx

def _GridCellCoords_GetIM(self):
    """
    Returns an immutable representation of the ``wx.GridCellCoords`` object, based on ``namedtuple``.
    
    This new object is hashable and can be used as a dictionary key,
    be added to sets, etc.  It can be converted back into a real ``wx.GridCellCoords``
    with a simple statement like this: ``obj = wx.GridCellCoords(imObj)``.
    """
    return _im_GridCellCoords(*self.Get())
GridCellCoords.GetIM = _GridCellCoords_GetIM
del _GridCellCoords_GetIM
def _GridCellCoords___str__(self):
    return str(self.Get())
GridCellCoords.__str__ = _GridCellCoords___str__
del _GridCellCoords___str__
def _GridCellCoords___repr__(self):
    return "GridCellCoords"+str(self.Get())
GridCellCoords.__repr__ = _GridCellCoords___repr__
del _GridCellCoords___repr__
def _GridCellCoords___len__(self):
    return len(self.Get())
GridCellCoords.__len__ = _GridCellCoords___len__
del _GridCellCoords___len__
def _GridCellCoords___nonzero__(self):
    return self.Get() != (0,0)
GridCellCoords.__nonzero__ = _GridCellCoords___nonzero__
del _GridCellCoords___nonzero__
def _GridCellCoords___bool__(self):
    return self.Get() != (0,0)
GridCellCoords.__bool__ = _GridCellCoords___bool__
del _GridCellCoords___bool__
def _GridCellCoords___reduce__(self):
    return (GridCellCoords, self.Get())
GridCellCoords.__reduce__ = _GridCellCoords___reduce__
del _GridCellCoords___reduce__
def _GridCellCoords___getitem__(self, idx):
    return self.Get()[idx]
GridCellCoords.__getitem__ = _GridCellCoords___getitem__
del _GridCellCoords___getitem__
def _GridCellCoords___setitem__(self, idx, val):
    if idx == 0: self.Row = val
    elif idx == 1: self.Col = val
    else: raise IndexError
GridCellCoords.__setitem__ = _GridCellCoords___setitem__
del _GridCellCoords___setitem__
GridCellCoords.__safe_for_unpickling__ = True

def _GridBlockCoords___str__(self):
    return str(self.Get())
GridBlockCoords.__str__ = _GridBlockCoords___str__
del _GridBlockCoords___str__
def _GridBlockCoords___repr__(self):
    return "GridBlockCoords"+str(self.Get())
GridBlockCoords.__repr__ = _GridBlockCoords___repr__
del _GridBlockCoords___repr__
def _GridBlocks___iter__(self):
    """
    Returns a Python iterator for accessing the collection of grid blocks.
    """
    return PyGridBlocksIterator(self)
GridBlocks.__iter__ = _GridBlocks___iter__
del _GridBlocks___iter__
class PyGridBlocksIterator(object):
    "A Python iterator for GridBlocks objects"
    def __init__(self, blocks):
        self._blocks = blocks
        self._iterator = self._blocks.begin()

    def __next__(self):
        if self._iterator == self._blocks.end():
            raise StopIteration
        obj = self._iterator._get()
        self._iterator = self._iterator._next()
        return obj

def _GridTableBase_GetValueAsLong(self, row, col):
    val = self.GetValue(row, col)
    try:
        return int(val)
    except ValueError:
        return 0
GridTableBase.GetValueAsLong = _GridTableBase_GetValueAsLong
del _GridTableBase_GetValueAsLong
def _GridTableBase_GetValueAsDouble(self, row, col):
    val = self.GetValue(row, col)
    try:
        return float(val)
    except ValueError:
        return 0.0
GridTableBase.GetValueAsDouble = _GridTableBase_GetValueAsDouble
del _GridTableBase_GetValueAsDouble
def _GridTableBase_GetValueAsBool(self, row, col):
    val = self.GetValue(row, col)
    try:
        return bool(val)
    except ValueError:
        return False
GridTableBase.GetValueAsBool = _GridTableBase_GetValueAsBool
del _GridTableBase_GetValueAsBool
def _GridTableBase_SetValueAsLong(self, row, col, value):
    self.SetValue(row, col, int(value))
GridTableBase.SetValueAsLong = _GridTableBase_SetValueAsLong
del _GridTableBase_SetValueAsLong
def _GridTableBase_SetValueAsDouble(self, row, col, value):
    self.SetValue(row, col, float(value))
GridTableBase.SetValueAsDouble = _GridTableBase_SetValueAsDouble
del _GridTableBase_SetValueAsDouble
def _GridTableBase_SetValueAsBool(self, row, col, value):
    self.SetValue(row, col, bool(value))
GridTableBase.SetValueAsBool = _GridTableBase_SetValueAsBool
del _GridTableBase_SetValueAsBool
def _Grid_SetTable(self, table, takeOwnership=False, selmode=Grid.GridSelectCells):
    """
    Set the Grid Table to be used by this grid.
    """
    val = self._SetTable(table, takeOwnership, selmode)
    if takeOwnership:
        import wx.siplib
        wx.siplib.transferto(table, self)
    return val
Grid.SetTable = _Grid_SetTable
del _Grid_SetTable
Grid.SelectCells = Grid.GridSelectCells
Grid.SelectRows = Grid.GridSelectRows
Grid.SelectColumns = Grid.GridSelectColumns
Grid.SelectRowsOrColumns = Grid.GridSelectRowsOrColumns

Grid.Table = property(Grid.GetTable, Grid.SetTable)
def _GridUpdateLocker___enter__(self):
    return self
GridUpdateLocker.__enter__ = _GridUpdateLocker___enter__
del _GridUpdateLocker___enter__
def _GridUpdateLocker___exit__(self, exc_type, exc_val, exc_tb):
    return False
GridUpdateLocker.__exit__ = _GridUpdateLocker___exit__
del _GridUpdateLocker___exit__
GRID_VALUE_STRING =    "string"
GRID_VALUE_BOOL =      "bool"
GRID_VALUE_NUMBER =    "long"
GRID_VALUE_FLOAT =     "double"
GRID_VALUE_CHOICE =    "choice"
GRID_VALUE_DATE =      "date"
GRID_VALUE_TEXT =      "string"
GRID_VALUE_LONG =      "long"
GRID_VALUE_CHOICEINT = "choiceint"
GRID_VALUE_DATETIME =  "datetime"

GRIDTABLE_REQUEST_VIEW_GET_VALUES = 2000
GRIDTABLE_REQUEST_VIEW_SEND_VALUES = 2001

from collections import namedtuple
_im_GridCellCoords = namedtuple('_im_GridCellCoords', ['Row', 'Col'])
del namedtuple

def _GridCellCoordsArray___repr__(self):
    return "GridCellCoordsArray: " + repr(list(self))
GridCellCoordsArray.__repr__ = _GridCellCoordsArray___repr__
del _GridCellCoordsArray___repr__
PyGridCellRenderer = wx.deprecated(GridCellRenderer, 'Use GridCellRenderer instead.')

PyGridCellEditor = wx.deprecated(GridCellEditor, 'Use GridCellEditor instead.')

PyGridCellAttrProvider = wx.deprecated(GridCellAttrProvider, 'Use GridCellAttrProvider instead.')

PyGridTableBase = wx.deprecated(GridTableBase, 'Use GridTableBase instead.')

EVT_GRID_CELL_LEFT_CLICK = wx.PyEventBinder( wxEVT_GRID_CELL_LEFT_CLICK )
EVT_GRID_CELL_RIGHT_CLICK = wx.PyEventBinder( wxEVT_GRID_CELL_RIGHT_CLICK )
EVT_GRID_CELL_LEFT_DCLICK = wx.PyEventBinder( wxEVT_GRID_CELL_LEFT_DCLICK )
EVT_GRID_CELL_RIGHT_DCLICK = wx.PyEventBinder( wxEVT_GRID_CELL_RIGHT_DCLICK )
EVT_GRID_LABEL_LEFT_CLICK = wx.PyEventBinder( wxEVT_GRID_LABEL_LEFT_CLICK )
EVT_GRID_LABEL_RIGHT_CLICK = wx.PyEventBinder( wxEVT_GRID_LABEL_RIGHT_CLICK )
EVT_GRID_LABEL_LEFT_DCLICK = wx.PyEventBinder( wxEVT_GRID_LABEL_LEFT_DCLICK )
EVT_GRID_LABEL_RIGHT_DCLICK = wx.PyEventBinder( wxEVT_GRID_LABEL_RIGHT_DCLICK )
EVT_GRID_ROW_SIZE = wx.PyEventBinder( wxEVT_GRID_ROW_SIZE )
EVT_GRID_COL_SIZE = wx.PyEventBinder( wxEVT_GRID_COL_SIZE )
EVT_GRID_COL_AUTO_SIZE = wx.PyEventBinder( wxEVT_GRID_COL_AUTO_SIZE )
EVT_GRID_RANGE_SELECTING = wx.PyEventBinder( wxEVT_GRID_RANGE_SELECTING )
EVT_GRID_RANGE_SELECTED = wx.PyEventBinder( wxEVT_GRID_RANGE_SELECTED )
EVT_GRID_CELL_CHANGING = wx.PyEventBinder( wxEVT_GRID_CELL_CHANGING )
EVT_GRID_CELL_CHANGED = wx.PyEventBinder( wxEVT_GRID_CELL_CHANGED )
EVT_GRID_SELECT_CELL = wx.PyEventBinder( wxEVT_GRID_SELECT_CELL )
EVT_GRID_EDITOR_SHOWN = wx.PyEventBinder( wxEVT_GRID_EDITOR_SHOWN )
EVT_GRID_EDITOR_HIDDEN = wx.PyEventBinder( wxEVT_GRID_EDITOR_HIDDEN )
EVT_GRID_EDITOR_CREATED = wx.PyEventBinder( wxEVT_GRID_EDITOR_CREATED )
EVT_GRID_CELL_BEGIN_DRAG = wx.PyEventBinder( wxEVT_GRID_CELL_BEGIN_DRAG )
EVT_GRID_ROW_MOVE = wx.PyEventBinder( wxEVT_GRID_ROW_MOVE )
EVT_GRID_COL_MOVE = wx.PyEventBinder( wxEVT_GRID_COL_MOVE )
EVT_GRID_COL_SORT = wx.PyEventBinder( wxEVT_GRID_COL_SORT )
EVT_GRID_TABBING = wx.PyEventBinder( wxEVT_GRID_TABBING )

# The same as above but with the ability to specify an identifier
EVT_GRID_CMD_CELL_LEFT_CLICK =     wx.PyEventBinder( wxEVT_GRID_CELL_LEFT_CLICK,    1 )
EVT_GRID_CMD_CELL_RIGHT_CLICK =    wx.PyEventBinder( wxEVT_GRID_CELL_RIGHT_CLICK,   1 )
EVT_GRID_CMD_CELL_LEFT_DCLICK =    wx.PyEventBinder( wxEVT_GRID_CELL_LEFT_DCLICK,   1 )
EVT_GRID_CMD_CELL_RIGHT_DCLICK =   wx.PyEventBinder( wxEVT_GRID_CELL_RIGHT_DCLICK,  1 )
EVT_GRID_CMD_LABEL_LEFT_CLICK =    wx.PyEventBinder( wxEVT_GRID_LABEL_LEFT_CLICK,   1 )
EVT_GRID_CMD_LABEL_RIGHT_CLICK =   wx.PyEventBinder( wxEVT_GRID_LABEL_RIGHT_CLICK,  1 )
EVT_GRID_CMD_LABEL_LEFT_DCLICK =   wx.PyEventBinder( wxEVT_GRID_LABEL_LEFT_DCLICK,  1 )
EVT_GRID_CMD_LABEL_RIGHT_DCLICK =  wx.PyEventBinder( wxEVT_GRID_LABEL_RIGHT_DCLICK, 1 )
EVT_GRID_CMD_ROW_SIZE =            wx.PyEventBinder( wxEVT_GRID_ROW_SIZE,           1 )
EVT_GRID_CMD_COL_SIZE =            wx.PyEventBinder( wxEVT_GRID_COL_SIZE,           1 )
EVT_GRID_CMD_COL_AUTO_SIZE =       wx.PyEventBinder( wxEVT_GRID_COL_AUTO_SIZE,      1 )
EVT_GRID_CMD_RANGE_SELECTING =     wx.PyEventBinder( wxEVT_GRID_RANGE_SELECTING,    1 )
EVT_GRID_CMD_RANGE_SELECTED =      wx.PyEventBinder( wxEVT_GRID_RANGE_SELECTED,     1 )
EVT_GRID_CMD_CELL_CHANGING =       wx.PyEventBinder( wxEVT_GRID_CELL_CHANGING,      1 )
EVT_GRID_CMD_CELL_CHANGED =        wx.PyEventBinder( wxEVT_GRID_CELL_CHANGED,       1 )
EVT_GRID_CMD_SELECT_CELL =         wx.PyEventBinder( wxEVT_GRID_SELECT_CELL,        1 )
EVT_GRID_CMD_EDITOR_SHOWN =        wx.PyEventBinder( wxEVT_GRID_EDITOR_SHOWN,       1 )
EVT_GRID_CMD_EDITOR_HIDDEN =       wx.PyEventBinder( wxEVT_GRID_EDITOR_HIDDEN,      1 )
EVT_GRID_CMD_EDITOR_CREATED =      wx.PyEventBinder( wxEVT_GRID_EDITOR_CREATED,     1 )
EVT_GRID_CMD_CELL_BEGIN_DRAG =     wx.PyEventBinder( wxEVT_GRID_CELL_BEGIN_DRAG,    1 )
EVT_GRID_CMD_ROW_MOVE =            wx.PyEventBinder( wxEVT_GRID_ROW_MOVE,           1 )
EVT_GRID_CMD_COL_MOVE =            wx.PyEventBinder( wxEVT_GRID_COL_MOVE,           1 )
EVT_GRID_CMD_COL_SORT =            wx.PyEventBinder( wxEVT_GRID_COL_SORT,           1 )
EVT_GRID_CMD_TABBING =             wx.PyEventBinder( wxEVT_GRID_TABBING,            1 )

# Just for compatibility, remove them in a future release
EVT_GRID_RANGE_SELECT =            EVT_GRID_RANGE_SELECTED
EVT_GRID_CMD_RANGE_SELECT =        EVT_GRID_CMD_RANGE_SELECTED
wxEVT_GRID_RANGE_SELECT =          wxEVT_GRID_RANGE_SELECTED

