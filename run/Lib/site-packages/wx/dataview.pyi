# -*- coding: utf-8 -*-
#---------------------------------------------------------------------------
# This file is generated by wxPython's PI generator.  Do not edit by hand.
#
# The *.pyi files are used by PyCharm and other development tools to provide
# more information, such as PEP 484 type hints, than it is able to glean from
# introspection of extension types and methods.  They are not intended to be
# imported, executed or used for any other purpose other than providing info
# to the tools. If you don't use use a tool that makes use of .pyi files then
# you can safely ignore this file.
#
# See: https://www.python.org/dev/peps/pep-0484/
#      https://www.jetbrains.com/help/pycharm/2016.1/type-hinting-in-pycharm.html
#
# Copyright: (c) 2020 by Total Control Software
# License:   wxWindows License
#---------------------------------------------------------------------------

#-- begin-typing-imports --#
from __future__ import annotations
from datetime import datetime, date
from enum import IntEnum, IntFlag, auto
from typing import (Any, overload, TypeAlias, Generic,
    Union, Optional, List, Tuple, Callable
)
try:
    from typing import ParamSpec
except ImportError:
    from typing_extensions import ParamSpec

_TwoInts: TypeAlias = Tuple[int, int]
_ThreeInts: TypeAlias = Tuple[int, int, int]
_FourInts: TypeAlias = Tuple[int, int, int, int]
_TwoFloats: TypeAlias = Tuple[float, float]
_FourFloats: TypeAlias = Tuple[float, float, float, float]

#-- end-typing-imports --#

"""
The classes in this module provide views and data models for viewing tabular
or hierarchical data in a more advanced way than what is provided by classes
such as :ref:`wx.ListCtrl`, :ref:`wx.TreeCtrl`, etc.
"""
#-- begin-_dataview --#

import wx
#-- end-_dataview --#
#-- begin-dataview --#
DVC_DEFAULT_RENDERER_SIZE: int
DVC_DEFAULT_WIDTH: int
DVC_TOGGLE_DEFAULT_WIDTH: int
DVC_DEFAULT_MINWIDTH: int
DVR_DEFAULT_ALIGNMENT: int
DV_SINGLE: int
DV_MULTIPLE: int
DV_NO_HEADER: int
DV_HORIZ_RULES: int
DV_VERT_RULES: int
DV_ROW_LINES: int
DV_VARIABLE_LINE_HEIGHT: int

class _DataViewCellMode(IntEnum):
    DATAVIEW_CELL_INERT = auto()
    DATAVIEW_CELL_ACTIVATABLE = auto()
    DATAVIEW_CELL_EDITABLE = auto()
DataViewCellMode: TypeAlias = Union[_DataViewCellMode, int]
DATAVIEW_CELL_INERT = _DataViewCellMode.DATAVIEW_CELL_INERT
DATAVIEW_CELL_ACTIVATABLE = _DataViewCellMode.DATAVIEW_CELL_ACTIVATABLE
DATAVIEW_CELL_EDITABLE = _DataViewCellMode.DATAVIEW_CELL_EDITABLE

class _DataViewCellRenderState(IntEnum):
    DATAVIEW_CELL_SELECTED = auto()
    DATAVIEW_CELL_PRELIT = auto()
    DATAVIEW_CELL_INSENSITIVE = auto()
    DATAVIEW_CELL_FOCUSED = auto()
DataViewCellRenderState: TypeAlias = Union[_DataViewCellRenderState, int]
DATAVIEW_CELL_SELECTED = _DataViewCellRenderState.DATAVIEW_CELL_SELECTED
DATAVIEW_CELL_PRELIT = _DataViewCellRenderState.DATAVIEW_CELL_PRELIT
DATAVIEW_CELL_INSENSITIVE = _DataViewCellRenderState.DATAVIEW_CELL_INSENSITIVE
DATAVIEW_CELL_FOCUSED = _DataViewCellRenderState.DATAVIEW_CELL_FOCUSED

class _DataViewColumnFlags(IntFlag):
    DATAVIEW_COL_RESIZABLE = auto()
    DATAVIEW_COL_SORTABLE = auto()
    DATAVIEW_COL_REORDERABLE = auto()
    DATAVIEW_COL_HIDDEN = auto()
DataViewColumnFlags: TypeAlias = Union[_DataViewColumnFlags, int]
DATAVIEW_COL_RESIZABLE = _DataViewColumnFlags.DATAVIEW_COL_RESIZABLE
DATAVIEW_COL_SORTABLE = _DataViewColumnFlags.DATAVIEW_COL_SORTABLE
DATAVIEW_COL_REORDERABLE = _DataViewColumnFlags.DATAVIEW_COL_REORDERABLE
DATAVIEW_COL_HIDDEN = _DataViewColumnFlags.DATAVIEW_COL_HIDDEN
wxEVT_DATAVIEW_SELECTION_CHANGED: int
wxEVT_DATAVIEW_ITEM_ACTIVATED: int
wxEVT_DATAVIEW_ITEM_COLLAPSING: int
wxEVT_DATAVIEW_ITEM_COLLAPSED: int
wxEVT_DATAVIEW_ITEM_EXPANDING: int
wxEVT_DATAVIEW_ITEM_EXPANDED: int
wxEVT_DATAVIEW_ITEM_START_EDITING: int
wxEVT_DATAVIEW_ITEM_EDITING_STARTED: int
wxEVT_DATAVIEW_ITEM_EDITING_DONE: int
wxEVT_DATAVIEW_ITEM_VALUE_CHANGED: int
wxEVT_DATAVIEW_ITEM_CONTEXT_MENU: int
wxEVT_DATAVIEW_COLUMN_HEADER_CLICK: int
wxEVT_DATAVIEW_COLUMN_HEADER_RIGHT_CLICK: int
wxEVT_DATAVIEW_COLUMN_SORTED: int
wxEVT_DATAVIEW_COLUMN_REORDERED: int
wxEVT_DATAVIEW_CACHE_HINT: int
wxEVT_DATAVIEW_ITEM_BEGIN_DRAG: int
wxEVT_DATAVIEW_ITEM_DROP_POSSIBLE: int
wxEVT_DATAVIEW_ITEM_DROP: int

class DataViewItem:
    """
    DataViewItem() -> None
    DataViewItem(item) -> None
    DataViewItem(id) -> None
    
    wxDataViewItem is a small opaque class that represents an item in a
    wxDataViewCtrl in a persistent way, i.e.
    """

    @overload
    def __init__(self, item: DataViewItem) -> None:
        ...

    @overload
    def __init__(self, id: Any) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        DataViewItem() -> None
        DataViewItem(item) -> None
        DataViewItem(id) -> None
        
        wxDataViewItem is a small opaque class that represents an item in a
        wxDataViewCtrl in a persistent way, i.e.
        """

    def GetID(self) -> Any:
        """
        GetID() -> Any
        
        Returns the ID.
        """

    def IsOk(self) -> bool:
        """
        IsOk() -> bool
        
        Returns true if the ID is not NULL.
        """

    def __nonzero__(self) -> bool:
        """
        __nonzero__() -> bool
        """

    def __bool__(self) -> bool:
        """
        __bool__() -> bool
        """

    def __hash__(self) -> int:
        """
        __hash__() -> int
        """

    def __eq__(self, other: DataViewItem) -> bool:
        """
        __eq__(other) -> bool
        """

    def __ne__(self, other: DataViewItem) -> bool:
        """
        __ne__(other) -> bool
        """
    @property
    def ID(self) -> Any: ...
# end of class DataViewItem


class DataViewItemAttr:
    """
    DataViewItemAttr() -> None
    
    This class is used to indicate to a wxDataViewCtrl that a certain item
    (see wxDataViewItem) has extra font attributes for its renderer.
    """

    def __init__(self) -> None:
        """
        DataViewItemAttr() -> None
        
        This class is used to indicate to a wxDataViewCtrl that a certain item
        (see wxDataViewItem) has extra font attributes for its renderer.
        """

    def SetBold(self, set: bool) -> None:
        """
        SetBold(set) -> None
        
        Call this to indicate that the item shall be displayed in bold text.
        """

    def SetColour(self, colour: wx.Colour) -> None:
        """
        SetColour(colour) -> None
        
        Call this to indicate that the item shall be displayed with that
        colour.
        """

    def SetBackgroundColour(self, colour: wx.Colour) -> None:
        """
        SetBackgroundColour(colour) -> None
        
        Call this to set the background colour to use.
        """

    def SetItalic(self, set: bool) -> None:
        """
        SetItalic(set) -> None
        
        Call this to indicate that the item shall be displayed in italic text.
        """

    def SetStrikethrough(self, set: bool) -> None:
        """
        SetStrikethrough(set) -> None
        
        Call this to indicate that the item shall be displayed in
        strikethrough text.
        """

    def HasColour(self) -> bool:
        """
        HasColour() -> bool
        
        Returns true if the colour property has been set.
        """

    def GetColour(self) -> wx.Colour:
        """
        GetColour() -> wx.Colour
        
        Returns this attribute's colour.
        """

    def HasFont(self) -> bool:
        """
        HasFont() -> bool
        
        Returns true if any property affecting the font has been set.
        """

    def GetBold(self) -> bool:
        """
        GetBold() -> bool
        
        Returns value of the bold property.
        """

    def GetItalic(self) -> bool:
        """
        GetItalic() -> bool
        
        Returns value of the italics property.
        """

    def HasBackgroundColour(self) -> bool:
        """
        HasBackgroundColour() -> bool
        
        Returns true if the background colour property has been set.
        """

    def GetBackgroundColour(self) -> wx.Colour:
        """
        GetBackgroundColour() -> wx.Colour
        
        Returns the colour to be used for the background.
        """

    def IsDefault(self) -> bool:
        """
        IsDefault() -> bool
        
        Returns true if none of the properties have been set.
        """

    def GetEffectiveFont(self, font: wx.Font) -> wx.Font:
        """
        GetEffectiveFont(font) -> wx.Font
        
        Return the font based on the given one with this attribute applied to
        it.
        """
    @property
    def BackgroundColour(self) -> wx.Colour: ...
    @BackgroundColour.setter
    def BackgroundColour(self, value: wx.Colour, /) -> None: ...
    @property
    def Bold(self) -> bool: ...
    @Bold.setter
    def Bold(self, value: bool, /) -> None: ...
    @property
    def Colour(self) -> wx.Colour: ...
    @Colour.setter
    def Colour(self, value: wx.Colour, /) -> None: ...
    @property
    def Italic(self) -> bool: ...
    @Italic.setter
    def Italic(self, value: bool, /) -> None: ...
# end of class DataViewItemAttr


class DataViewIconText(wx.Object):
    """
    DataViewIconText(text='', bitmap=wx.BitmapBundle()) -> None
    DataViewIconText(other) -> None
    
    wxDataViewIconText is used by wxDataViewIconTextRenderer for data
    transfer.
    """

    @overload
    def __init__(self, other: DataViewIconText) -> None:
        ...

    @overload
    def __init__(self, text: str='', bitmap: wx.BitmapBundle=wx.BitmapBundle()) -> None:
        """
        DataViewIconText(text='', bitmap=wx.BitmapBundle()) -> None
        DataViewIconText(other) -> None
        
        wxDataViewIconText is used by wxDataViewIconTextRenderer for data
        transfer.
        """

    def GetBitmapBundle(self) -> wx.BitmapBundle:
        """
        GetBitmapBundle() -> wx.BitmapBundle
        
        Gets the associated image.
        """

    def GetIcon(self) -> wx.Icon:
        """
        GetIcon() -> wx.Icon
        
        Gets the icon.
        """

    def GetText(self) -> str:
        """
        GetText() -> str
        
        Gets the text.
        """

    def SetBitmapBundle(self, bitmap: wx.BitmapBundle) -> None:
        """
        SetBitmapBundle(bitmap) -> None
        
        Sets the associated image.
        """

    def SetIcon(self, icon: wx.Icon) -> None:
        """
        SetIcon(icon) -> None
        
        Set the icon.
        """

    def SetText(self, text: str) -> None:
        """
        SetText(text) -> None
        
        Set the text.
        """
    @property
    def BitmapBundle(self) -> wx.BitmapBundle: ...
    @BitmapBundle.setter
    def BitmapBundle(self, value: wx.BitmapBundle, /) -> None: ...
    @property
    def Icon(self) -> wx.Icon: ...
    @Icon.setter
    def Icon(self, value: wx.Icon, /) -> None: ...
    @property
    def Text(self) -> str: ...
    @Text.setter
    def Text(self, value: str, /) -> None: ...
# end of class DataViewIconText


class DataViewCheckIconText(DataViewIconText):
    """
    DataViewCheckIconText(text='', icon=wx.BitmapBundle(), checkedState=wx.CHK_UNDETERMINED) -> None
    DataViewCheckIconText(other) -> None
    
    wxDataViewCheckIconText is used by wxDataViewCheckIconTextRenderer for
    data transfer.
    """

    @overload
    def __init__(self, other: DataViewCheckIconText) -> None:
        ...

    @overload
    def __init__(self, text: str='', icon: wx.BitmapBundle=wx.BitmapBundle(), checkedState: wx.CheckBoxState=wx.CHK_UNDETERMINED) -> None:
        """
        DataViewCheckIconText(text='', icon=wx.BitmapBundle(), checkedState=wx.CHK_UNDETERMINED) -> None
        DataViewCheckIconText(other) -> None
        
        wxDataViewCheckIconText is used by wxDataViewCheckIconTextRenderer for
        data transfer.
        """

    def SetCheckedState(self, state: wx.CheckBoxState) -> None:
        """
        SetCheckedState(state) -> None
        
        Sets the checked state.
        """

    def GetCheckedState(self) -> wx.CheckBoxState:
        """
        GetCheckedState() -> wx.CheckBoxState
        
        Gets the checked state.
        """
    @property
    def CheckedState(self) -> wx.CheckBoxState: ...
    @CheckedState.setter
    def CheckedState(self, value: wx.CheckBoxState, /) -> None: ...
# end of class DataViewCheckIconText


class DataViewModelNotifier:
    """
    DataViewModelNotifier() -> None
    
    A wxDataViewModelNotifier instance is owned by a wxDataViewModel and
    mirrors its notification interface.
    """

    def __init__(self) -> None:
        """
        DataViewModelNotifier() -> None
        
        A wxDataViewModelNotifier instance is owned by a wxDataViewModel and
        mirrors its notification interface.
        """

    def Cleared(self) -> bool:
        """
        Cleared() -> bool
        
        Called by owning model.
        """

    def GetOwner(self) -> DataViewModel:
        """
        GetOwner() -> DataViewModel
        
        Get owning wxDataViewModel.
        """

    def ItemAdded(self, parent: DataViewItem, item: DataViewItem) -> bool:
        """
        ItemAdded(parent, item) -> bool
        
        Called by owning model.
        """

    def ItemChanged(self, item: DataViewItem) -> bool:
        """
        ItemChanged(item) -> bool
        
        Called by owning model.
        """

    def ItemDeleted(self, parent: DataViewItem, item: DataViewItem) -> bool:
        """
        ItemDeleted(parent, item) -> bool
        
        Called by owning model.
        """

    def ItemsAdded(self, parent: DataViewItem, items: DataViewItemArray) -> bool:
        """
        ItemsAdded(parent, items) -> bool
        
        Called by owning model.
        """

    def ItemsChanged(self, items: DataViewItemArray) -> bool:
        """
        ItemsChanged(items) -> bool
        
        Called by owning model.
        """

    def ItemsDeleted(self, parent: DataViewItem, items: DataViewItemArray) -> bool:
        """
        ItemsDeleted(parent, items) -> bool
        
        Called by owning model.
        """

    def Resort(self) -> None:
        """
        Resort() -> None
        
        Called by owning model.
        """

    def SetOwner(self, owner: DataViewModel) -> None:
        """
        SetOwner(owner) -> None
        
        Set owner of this notifier.
        """

    def ValueChanged(self, item: DataViewItem, col: int) -> bool:
        """
        ValueChanged(item, col) -> bool
        
        Called by owning model.
        """
    @property
    def Owner(self) -> DataViewModel: ...
    @Owner.setter
    def Owner(self, value: DataViewModel, /) -> None: ...
# end of class DataViewModelNotifier


class DataViewModel(wx.RefCounter):
    """
    DataViewModel() -> None
    
    wxDataViewModel is the base class for all data model to be displayed
    by a wxDataViewCtrl.
    """

    def __init__(self) -> None:
        """
        DataViewModel() -> None
        
        wxDataViewModel is the base class for all data model to be displayed
        by a wxDataViewCtrl.
        """

    def AddNotifier(self, notifier: DataViewModelNotifier) -> None:
        """
        AddNotifier(notifier) -> None
        
        Adds a wxDataViewModelNotifier to the model.
        """

    def ChangeValue(self, variant: DVCVariant, item: DataViewItem, col: int) -> bool:
        """
        ChangeValue(variant, item, col) -> bool
        
        Change the value of the given item and update the control to reflect
        it.
        """

    def Cleared(self) -> bool:
        """
        Cleared() -> bool
        
        Called to inform the model that all of its data has been changed.
        """

    def Compare(self, item1: DataViewItem, item2: DataViewItem, column: int, ascending: bool) -> int:
        """
        Compare(item1, item2, column, ascending) -> int
        
        The compare function to be used by the control.
        """

    def GetAttr(self, item: DataViewItem, col: int, attr: DataViewItemAttr) -> bool:
        """
        GetAttr(item, col, attr) -> bool
        
        Override this to indicate that the item has special font attributes.
        """

    def IsEnabled(self, item: DataViewItem, col: int) -> bool:
        """
        IsEnabled(item, col) -> bool
        
        Override this to indicate that the item should be disabled.
        """

    def GetChildren(self, item: DataViewItem, children: DataViewItemArray) -> int:
        """
        GetChildren(item, children) -> int
        
        Override this so the control can query the child items of an item.
        """

    def GetParent(self, item: DataViewItem) -> DataViewItem:
        """
        GetParent(item) -> DataViewItem
        
        Override this to indicate which wxDataViewItem representing the parent
        of item or an invalid wxDataViewItem if the root item is the parent
        item.
        """

    def GetValue(self, item: DataViewItem, col: int) -> DVCVariant:
        """
        GetValue(item, col) -> DVCVariant
        
        Override this to indicate the value of item.
        """

    def HasContainerColumns(self, item: DataViewItem) -> bool:
        """
        HasContainerColumns(item) -> bool
        
        Override this method to indicate if a container item merely acts as a
        headline (or for categorisation) or if it also acts a normal item with
        entries for further columns.
        """

    def HasDefaultCompare(self) -> bool:
        """
        HasDefaultCompare() -> bool
        
        Override this to indicate that the model provides a default compare
        function that the control should use if no wxDataViewColumn has been
        chosen for sorting.
        """

    def HasValue(self, item: DataViewItem, col: int) -> bool:
        """
        HasValue(item, col) -> bool
        
        Return true if there is a value in the given column of this item.
        """

    def IsContainer(self, item: DataViewItem) -> bool:
        """
        IsContainer(item) -> bool
        
        Override this to indicate if item is a container, i.e. if it can have
        child items.
        """

    def ItemAdded(self, parent: DataViewItem, item: DataViewItem) -> bool:
        """
        ItemAdded(parent, item) -> bool
        
        Call this to inform the model that an item has been added to the data.
        """

    def ItemChanged(self, item: DataViewItem) -> bool:
        """
        ItemChanged(item) -> bool
        
        Call this to inform the model that an item has changed.
        """

    def ItemDeleted(self, parent: DataViewItem, item: DataViewItem) -> bool:
        """
        ItemDeleted(parent, item) -> bool
        
        Call this to inform the model that an item has been deleted from the
        data.
        """

    def ItemsAdded(self, parent: DataViewItem, items: DataViewItemArray) -> bool:
        """
        ItemsAdded(parent, items) -> bool
        
        Call this to inform the model that several items have been added to
        the data.
        """

    def ItemsChanged(self, items: DataViewItemArray) -> bool:
        """
        ItemsChanged(items) -> bool
        
        Call this to inform the model that several items have changed.
        """

    def ItemsDeleted(self, parent: DataViewItem, items: DataViewItemArray) -> bool:
        """
        ItemsDeleted(parent, items) -> bool
        
        Call this to inform the model that several items have been deleted.
        """

    def RemoveNotifier(self, notifier: DataViewModelNotifier) -> None:
        """
        RemoveNotifier(notifier) -> None
        
        Remove the notifier from the list of notifiers.
        """

    def Resort(self) -> None:
        """
        Resort() -> None
        
        Call this to initiate a resort after the sort function has been
        changed.
        """

    def SetValue(self, variant: DVCVariant, item: DataViewItem, col: int) -> bool:
        """
        SetValue(variant, item, col) -> bool
        
        This gets called in order to set a value in the data model.
        """

    def ValueChanged(self, item: DataViewItem, col: int) -> bool:
        """
        ValueChanged(item, col) -> bool
        
        Call this to inform this model that a value in the model has been
        changed.
        """

    def IsListModel(self) -> bool:
        """
        IsListModel() -> bool
        """

    def IsVirtualListModel(self) -> bool:
        """
        IsVirtualListModel() -> bool
        """
# end of class DataViewModel


class DataViewListModel(DataViewModel):
    """
    Base class with abstract API for wxDataViewIndexListModel and
    wxDataViewVirtualListModel.
    """

    def Compare(self, item1: DataViewItem, item2: DataViewItem, column: int, ascending: bool) -> int:
        """
        Compare(item1, item2, column, ascending) -> int
        
        Compare method that sorts the items by their index.
        """

    def GetAttrByRow(self, row: int, col: int, attr: DataViewItemAttr) -> bool:
        """
        GetAttrByRow(row, col, attr) -> bool
        
        Override this to indicate that the row has special font attributes.
        """

    def IsEnabledByRow(self, row: int, col: int) -> bool:
        """
        IsEnabledByRow(row, col) -> bool
        
        Override this if you want to disable specific items.
        """

    def GetCount(self) -> int:
        """
        GetCount() -> int
        
        Returns the number of items (or rows) in the list.
        """

    def GetRow(self, item: DataViewItem) -> int:
        """
        GetRow(item) -> int
        
        Returns the position of given item.
        """

    def GetValueByRow(self, row: int, col: int) -> DVCVariant:
        """
        GetValueByRow(row, col) -> DVCVariant
        
        Override this to allow getting values from the model.
        """

    def SetValueByRow(self, variant: DVCVariant, row: int, col: int) -> bool:
        """
        SetValueByRow(variant, row, col) -> bool
        
        Called in order to set a value in the model.
        """
    @property
    def Count(self) -> int: ...
# end of class DataViewListModel


class DataViewIndexListModel(DataViewListModel):
    """
    DataViewIndexListModel(initial_size=0) -> None
    
    wxDataViewIndexListModel is a specialized data model which lets you
    address an item by its position (row) rather than its wxDataViewItem
    (which you can obtain from this class).
    """

    def __init__(self, initial_size: int=0) -> None:
        """
        DataViewIndexListModel(initial_size=0) -> None
        
        wxDataViewIndexListModel is a specialized data model which lets you
        address an item by its position (row) rather than its wxDataViewItem
        (which you can obtain from this class).
        """

    def GetItem(self, row: int) -> DataViewItem:
        """
        GetItem(row) -> DataViewItem
        
        Returns the wxDataViewItem at the given row.
        """

    def Reset(self, new_size: int) -> None:
        """
        Reset(new_size) -> None
        
        Call this after if the data has to be read again from the model.
        """

    def RowAppended(self) -> None:
        """
        RowAppended() -> None
        
        Call this after a row has been appended to the model.
        """

    def RowChanged(self, row: int) -> None:
        """
        RowChanged(row) -> None
        
        Call this after a row has been changed.
        """

    def RowDeleted(self, row: int) -> None:
        """
        RowDeleted(row) -> None
        
        Call this after a row has been deleted.
        """

    def RowInserted(self, before: int) -> None:
        """
        RowInserted(before) -> None
        
        Call this after a row has been inserted at the given position.
        """

    def RowPrepended(self) -> None:
        """
        RowPrepended() -> None
        
        Call this after a row has been prepended to the model.
        """

    def RowValueChanged(self, row: int, col: int) -> None:
        """
        RowValueChanged(row, col) -> None
        
        Call this after a value has been changed.
        """

    def RowsDeleted(self, rows: List[int]) -> None:
        """
        RowsDeleted(rows) -> None
        
        Call this after rows have been deleted.
        """
# end of class DataViewIndexListModel


class DataViewVirtualListModel(DataViewListModel):
    """
    DataViewVirtualListModel(initial_size=0) -> None
    
    wxDataViewVirtualListModel is a specialized data model which lets you
    address an item by its position (row) rather than its wxDataViewItem
    and as such offers the exact same interface as
    wxDataViewIndexListModel.
    """

    def __init__(self, initial_size: int=0) -> None:
        """
        DataViewVirtualListModel(initial_size=0) -> None
        
        wxDataViewVirtualListModel is a specialized data model which lets you
        address an item by its position (row) rather than its wxDataViewItem
        and as such offers the exact same interface as
        wxDataViewIndexListModel.
        """

    def GetItem(self, row: int) -> DataViewItem:
        """
        GetItem(row) -> DataViewItem
        
        Returns the wxDataViewItem at the given row.
        """

    def Reset(self, new_size: int) -> None:
        """
        Reset(new_size) -> None
        
        Call this after if the data has to be read again from the model.
        """

    def RowAppended(self) -> None:
        """
        RowAppended() -> None
        
        Call this after a row has been appended to the model.
        """

    def RowChanged(self, row: int) -> None:
        """
        RowChanged(row) -> None
        
        Call this after a row has been changed.
        """

    def RowDeleted(self, row: int) -> None:
        """
        RowDeleted(row) -> None
        
        Call this after a row has been deleted.
        """

    def RowInserted(self, before: int) -> None:
        """
        RowInserted(before) -> None
        
        Call this after a row has been inserted at the given position.
        """

    def RowPrepended(self) -> None:
        """
        RowPrepended() -> None
        
        Call this after a row has been prepended to the model.
        """

    def RowValueChanged(self, row: int, col: int) -> None:
        """
        RowValueChanged(row, col) -> None
        
        Call this after a value has been changed.
        """

    def RowsDeleted(self, rows: List[int]) -> None:
        """
        RowsDeleted(rows) -> None
        
        Call this after rows have been deleted.
        """
# end of class DataViewVirtualListModel


class DataViewRenderer(wx.Object):
    """
    DataViewRenderer(varianttype, mode=DATAVIEW_CELL_INERT, align=DVR_DEFAULT_ALIGNMENT) -> None
    
    This class is used by wxDataViewCtrl to render the individual cells.
    """

    def __init__(self, varianttype: str, mode: DataViewCellMode=DATAVIEW_CELL_INERT, align: int=DVR_DEFAULT_ALIGNMENT) -> None:
        """
        DataViewRenderer(varianttype, mode=DATAVIEW_CELL_INERT, align=DVR_DEFAULT_ALIGNMENT) -> None
        
        This class is used by wxDataViewCtrl to render the individual cells.
        """

    def EnableEllipsize(self, mode: wx.EllipsizeMode=wx.ELLIPSIZE_MIDDLE) -> None:
        """
        EnableEllipsize(mode=wx.ELLIPSIZE_MIDDLE) -> None
        
        Enable or disable replacing parts of the item text with ellipsis to
        make it fit the column width.
        """

    def DisableEllipsize(self) -> None:
        """
        DisableEllipsize() -> None
        
        Disable replacing parts of the item text with ellipsis.
        """

    def GetAlignment(self) -> int:
        """
        GetAlignment() -> int
        
        Returns the alignment.
        """

    def GetEllipsizeMode(self) -> wx.EllipsizeMode:
        """
        GetEllipsizeMode() -> wx.EllipsizeMode
        
        Returns the ellipsize mode used by the renderer.
        """

    def GetMode(self) -> DataViewCellMode:
        """
        GetMode() -> DataViewCellMode
        
        Returns the cell mode.
        """

    def GetOwner(self) -> DataViewColumn:
        """
        GetOwner() -> DataViewColumn
        
        Returns pointer to the owning wxDataViewColumn.
        """

    def GetValue(self) -> DVCVariant:
        """
        GetValue() -> DVCVariant
        
        This methods retrieves the value from the renderer in order to
        transfer the value back to the data model.
        """

    def GetVariantType(self) -> str:
        """
        GetVariantType() -> str
        
        Returns a string with the type of the wxVariant supported by this
        renderer.
        """

    def IsCompatibleVariantType(self, variantType: str) -> bool:
        """
        IsCompatibleVariantType(variantType) -> bool
        
        Check if the given variant type is compatible with the type expected
        by this renderer.
        """

    def SetAlignment(self, align: int) -> None:
        """
        SetAlignment(align) -> None
        
        Sets the alignment of the renderer's content.
        """

    def SetOwner(self, owner: DataViewColumn) -> None:
        """
        SetOwner(owner) -> None
        
        Sets the owning wxDataViewColumn.
        """

    def SetValue(self, value: DVCVariant) -> bool:
        """
        SetValue(value) -> bool
        
        Set the value of the renderer (and thus its cell) to value.
        """

    def SetValueAdjuster(self, transformer: DataViewValueAdjuster) -> None:
        """
        SetValueAdjuster(transformer) -> None
        
        Set the transformer object to be used to customize values before they
        are rendered.
        """

    def Validate(self, value: DVCVariant) -> bool:
        """
        Validate(value) -> bool
        
        Before data is committed to the data model, it is passed to this
        method where it can be checked for validity.
        """

    def HasEditorCtrl(self) -> bool:
        """
        HasEditorCtrl() -> bool
        """

    def CreateEditorCtrl(self, parent: wx.Window, labelRect: wx.Rect, value: DVCVariant) -> wx.Window:
        """
        CreateEditorCtrl(parent, labelRect, value) -> wx.Window
        """

    def GetValueFromEditorCtrl(self, editor: wx.Window) -> DVCVariant:
        """
        GetValueFromEditorCtrl(editor) -> DVCVariant
        """

    def StartEditing(self, item: DataViewItem, labelRect: wx.Rect) -> bool:
        """
        StartEditing(item, labelRect) -> bool
        """

    def CancelEditing(self) -> None:
        """
        CancelEditing() -> None
        """

    def FinishEditing(self) -> bool:
        """
        FinishEditing() -> bool
        """

    def GetEditorCtrl(self) -> wx.Window:
        """
        GetEditorCtrl() -> wx.Window
        """
    @property
    def Alignment(self) -> int: ...
    @Alignment.setter
    def Alignment(self, value: int, /) -> None: ...
    @property
    def EditorCtrl(self) -> wx.Window: ...
    @property
    def EllipsizeMode(self) -> wx.EllipsizeMode: ...
    @property
    def Mode(self) -> DataViewCellMode: ...
    @property
    def Owner(self) -> DataViewColumn: ...
    @Owner.setter
    def Owner(self, value: DataViewColumn, /) -> None: ...
    @property
    def VariantType(self) -> str: ...
    @property
    def View(self) -> DataViewCtrl: ...

    def GetView(self) -> DataViewCtrl:
        """
        GetView() -> DataViewCtrl
        """
# end of class DataViewRenderer


class DataViewCustomRenderer(DataViewRenderer):
    """
    DataViewCustomRenderer(varianttype=DataViewCustomRenderer.GetDefaultType(), mode=DATAVIEW_CELL_INERT, align=DVR_DEFAULT_ALIGNMENT) -> None
    
    You need to derive a new class from wxDataViewCustomRenderer in order
    to write a new renderer.
    """

    def __init__(self, varianttype: str=DataViewCustomRenderer.GetDefaultType(), mode: DataViewCellMode=DATAVIEW_CELL_INERT, align: int=DVR_DEFAULT_ALIGNMENT) -> None:
        """
        DataViewCustomRenderer(varianttype=DataViewCustomRenderer.GetDefaultType(), mode=DATAVIEW_CELL_INERT, align=DVR_DEFAULT_ALIGNMENT) -> None
        
        You need to derive a new class from wxDataViewCustomRenderer in order
        to write a new renderer.
        """

    @staticmethod
    def GetDefaultType() -> str:
        """
        GetDefaultType() -> str
        
        Returns the wxVariant type used with this renderer.
        """

    def ActivateCell(self, cell: wx.Rect, model: DataViewModel, item: DataViewItem, col: int, mouseEvent: wx.MouseEvent) -> bool:
        """
        ActivateCell(cell, model, item, col, mouseEvent) -> bool
        
        Override this to react to cell activation.
        """

    def CreateEditorCtrl(self, parent: wx.Window, labelRect: wx.Rect, value: DVCVariant) -> wx.Window:
        """
        CreateEditorCtrl(parent, labelRect, value) -> wx.Window
        
        Override this to create the actual editor control once editing is
        about to start.
        """

    def GetAttr(self) -> DataViewItemAttr:
        """
        GetAttr() -> DataViewItemAttr
        
        Return the attribute to be used for rendering.
        """

    def GetSize(self) -> wx.Size:
        """
        GetSize() -> wx.Size
        
        Return size required to show content.
        """

    def GetValueFromEditorCtrl(self, editor: wx.Window) -> DVCVariant:
        """
        GetValueFromEditorCtrl(editor) -> DVCVariant
        
        Override this so that the renderer can get the value from the editor
        control (pointed to by editor):
        """

    def HasEditorCtrl(self) -> bool:
        """
        HasEditorCtrl() -> bool
        
        Override this and make it return true in order to indicate that this
        renderer supports in-place editing.
        """

    def LeftClick(self, cursor: wx.Point, cell: wx.Rect, model: DataViewModel, item: DataViewItem, col: int) -> bool:
        """
        LeftClick(cursor, cell, model, item, col) -> bool
        
        Override this to react to a left click.
        """

    def Activate(self, cell: wx.Rect, model: DataViewModel, item: DataViewItem, col: int) -> bool:
        """
        Activate(cell, model, item, col) -> bool
        
        Override this to react to the activation of a cell.
        """

    def Render(self, cell: wx.Rect, dc: wx.DC, state: int) -> bool:
        """
        Render(cell, dc, state) -> bool
        
        Override this to render the cell.
        """

    def RenderText(self, text: str, xoffset: int, cell: wx.Rect, dc: wx.DC, state: int) -> None:
        """
        RenderText(text, xoffset, cell, dc, state) -> None
        
        This method should be called from within Render() whenever you need to
        render simple text.
        """

    def StartDrag(self, cursor: wx.Point, cell: wx.Rect, model: DataViewModel, item: DataViewItem, col: int) -> bool:
        """
        StartDrag(cursor, cell, model, item, col) -> bool
        
        Override this to start a drag operation.
        """
    @property
    def Attr(self) -> DataViewItemAttr: ...
    @property
    def Size(self) -> wx.Size: ...

    def GetTextExtent(self, str: str) -> wx.Size:
        """
        GetTextExtent(str) -> wx.Size
        
        Helper for GetSize() implementations, respects attributes.
        """
# end of class DataViewCustomRenderer


class DataViewTextRenderer(DataViewRenderer):
    """
    DataViewTextRenderer(varianttype=DataViewTextRenderer.GetDefaultType(), mode=DATAVIEW_CELL_INERT, align=DVR_DEFAULT_ALIGNMENT) -> None
    
    wxDataViewTextRenderer is used for rendering text.
    """

    def __init__(self, varianttype: str=DataViewTextRenderer.GetDefaultType(), mode: DataViewCellMode=DATAVIEW_CELL_INERT, align: int=DVR_DEFAULT_ALIGNMENT) -> None:
        """
        DataViewTextRenderer(varianttype=DataViewTextRenderer.GetDefaultType(), mode=DATAVIEW_CELL_INERT, align=DVR_DEFAULT_ALIGNMENT) -> None
        
        wxDataViewTextRenderer is used for rendering text.
        """

    @staticmethod
    def GetDefaultType() -> str:
        """
        GetDefaultType() -> str
        
        Returns the wxVariant type used with this renderer.
        """

    def EnableMarkup(self, enable: bool=True) -> None:
        """
        EnableMarkup(enable=True) -> None
        
        Enable interpretation of markup in the item data.
        """
# end of class DataViewTextRenderer


class DataViewIconTextRenderer(DataViewRenderer):
    """
    DataViewIconTextRenderer(varianttype=DataViewIconTextRenderer.GetDefaultType(), mode=DATAVIEW_CELL_INERT, align=DVR_DEFAULT_ALIGNMENT) -> None
    
    The wxDataViewIconTextRenderer class is used to display text with a
    small icon next to it as it is typically done in a file manager.
    """

    def __init__(self, varianttype: str=DataViewIconTextRenderer.GetDefaultType(), mode: DataViewCellMode=DATAVIEW_CELL_INERT, align: int=DVR_DEFAULT_ALIGNMENT) -> None:
        """
        DataViewIconTextRenderer(varianttype=DataViewIconTextRenderer.GetDefaultType(), mode=DATAVIEW_CELL_INERT, align=DVR_DEFAULT_ALIGNMENT) -> None
        
        The wxDataViewIconTextRenderer class is used to display text with a
        small icon next to it as it is typically done in a file manager.
        """

    @staticmethod
    def GetDefaultType() -> str:
        """
        GetDefaultType() -> str
        
        Returns the wxVariant type used with this renderer.
        """
# end of class DataViewIconTextRenderer


class DataViewCheckIconTextRenderer(DataViewRenderer):
    """
    DataViewCheckIconTextRenderer(mode=DATAVIEW_CELL_ACTIVATABLE, align=DVR_DEFAULT_ALIGNMENT) -> None
    
    This renderer class shows a checkbox in addition to the icon and text
    shown by the base class and also allows the user to toggle this
    checkbox.
    """

    def __init__(self, mode: DataViewCellMode=DATAVIEW_CELL_ACTIVATABLE, align: int=DVR_DEFAULT_ALIGNMENT) -> None:
        """
        DataViewCheckIconTextRenderer(mode=DATAVIEW_CELL_ACTIVATABLE, align=DVR_DEFAULT_ALIGNMENT) -> None
        
        This renderer class shows a checkbox in addition to the icon and text
        shown by the base class and also allows the user to toggle this
        checkbox.
        """

    @staticmethod
    def GetDefaultType() -> str:
        """
        GetDefaultType() -> str
        """

    def Allow3rdStateForUser(self, allow: bool=True) -> None:
        """
        Allow3rdStateForUser(allow=True) -> None
        
        Allow the user to interactively select the 3rd state for the items
        rendered by this object.
        """
# end of class DataViewCheckIconTextRenderer


class DataViewProgressRenderer(DataViewRenderer):
    """
    DataViewProgressRenderer(label='', varianttype=DataViewProgressRenderer.GetDefaultType(), mode=DATAVIEW_CELL_INERT, align=DVR_DEFAULT_ALIGNMENT) -> None
    
    This class is used by wxDataViewCtrl to render progress bars.
    """

    def __init__(self, label: str='', varianttype: str=DataViewProgressRenderer.GetDefaultType(), mode: DataViewCellMode=DATAVIEW_CELL_INERT, align: int=DVR_DEFAULT_ALIGNMENT) -> None:
        """
        DataViewProgressRenderer(label='', varianttype=DataViewProgressRenderer.GetDefaultType(), mode=DATAVIEW_CELL_INERT, align=DVR_DEFAULT_ALIGNMENT) -> None
        
        This class is used by wxDataViewCtrl to render progress bars.
        """

    @staticmethod
    def GetDefaultType() -> str:
        """
        GetDefaultType() -> str
        
        Returns the wxVariant type used with this renderer.
        """
# end of class DataViewProgressRenderer


class DataViewSpinRenderer(DataViewCustomRenderer):
    """
    DataViewSpinRenderer(min, max, mode=DATAVIEW_CELL_EDITABLE, align=DVR_DEFAULT_ALIGNMENT) -> None
    
    This is a specialized renderer for rendering integer values.
    """

    def __init__(self, min: int, max: int, mode: DataViewCellMode=DATAVIEW_CELL_EDITABLE, align: int=DVR_DEFAULT_ALIGNMENT) -> None:
        """
        DataViewSpinRenderer(min, max, mode=DATAVIEW_CELL_EDITABLE, align=DVR_DEFAULT_ALIGNMENT) -> None
        
        This is a specialized renderer for rendering integer values.
        """
# end of class DataViewSpinRenderer


class DataViewToggleRenderer(DataViewRenderer):
    """
    DataViewToggleRenderer(varianttype=DataViewToggleRenderer.GetDefaultType(), mode=DATAVIEW_CELL_INERT, align=DVR_DEFAULT_ALIGNMENT) -> None
    
    This class is used by wxDataViewCtrl to render toggle controls.
    """

    def __init__(self, varianttype: str=DataViewToggleRenderer.GetDefaultType(), mode: DataViewCellMode=DATAVIEW_CELL_INERT, align: int=DVR_DEFAULT_ALIGNMENT) -> None:
        """
        DataViewToggleRenderer(varianttype=DataViewToggleRenderer.GetDefaultType(), mode=DATAVIEW_CELL_INERT, align=DVR_DEFAULT_ALIGNMENT) -> None
        
        This class is used by wxDataViewCtrl to render toggle controls.
        """

    @staticmethod
    def GetDefaultType() -> str:
        """
        GetDefaultType() -> str
        
        Returns the wxVariant type used with this renderer.
        """

    def ShowAsRadio(self) -> None:
        """
        ShowAsRadio() -> None
        
        Switch to using radiobutton-like appearance instead of the default
        checkbox-like one.
        """
# end of class DataViewToggleRenderer


class DataViewChoiceRenderer(DataViewRenderer):
    """
    DataViewChoiceRenderer(choices, mode=DATAVIEW_CELL_EDITABLE, alignment=DVR_DEFAULT_ALIGNMENT) -> None
    
    A wxDataViewCtrl renderer using wxChoice control and values of strings
    in it.
    """

    def __init__(self, choices: List[str], mode: DataViewCellMode=DATAVIEW_CELL_EDITABLE, alignment: int=DVR_DEFAULT_ALIGNMENT) -> None:
        """
        DataViewChoiceRenderer(choices, mode=DATAVIEW_CELL_EDITABLE, alignment=DVR_DEFAULT_ALIGNMENT) -> None
        
        A wxDataViewCtrl renderer using wxChoice control and values of strings
        in it.
        """

    def GetChoice(self, index: int) -> str:
        """
        GetChoice(index) -> str
        
        Returns the choice referred to by index.
        """

    def GetChoices(self) -> List[str]:
        """
        GetChoices() -> List[str]
        
        Returns all choices.
        """
    @property
    def Choices(self) -> List[str]: ...
# end of class DataViewChoiceRenderer


class DataViewDateRenderer(DataViewRenderer):
    """
    DataViewDateRenderer(varianttype=DataViewDateRenderer.GetDefaultType(), mode=DATAVIEW_CELL_ACTIVATABLE, align=DVR_DEFAULT_ALIGNMENT) -> None
    
    This class is used by wxDataViewCtrl to render calendar controls.
    """

    def __init__(self, varianttype: str=DataViewDateRenderer.GetDefaultType(), mode: DataViewCellMode=DATAVIEW_CELL_ACTIVATABLE, align: int=DVR_DEFAULT_ALIGNMENT) -> None:
        """
        DataViewDateRenderer(varianttype=DataViewDateRenderer.GetDefaultType(), mode=DATAVIEW_CELL_ACTIVATABLE, align=DVR_DEFAULT_ALIGNMENT) -> None
        
        This class is used by wxDataViewCtrl to render calendar controls.
        """

    @staticmethod
    def GetDefaultType() -> str:
        """
        GetDefaultType() -> str
        
        Returns the wxVariant type used with this renderer.
        """
# end of class DataViewDateRenderer


class DataViewBitmapRenderer(DataViewRenderer):
    """
    DataViewBitmapRenderer(varianttype=DataViewBitmapRenderer.GetDefaultType(), mode=DATAVIEW_CELL_INERT, align=DVR_DEFAULT_ALIGNMENT) -> None
    
    This class is used by wxDataViewCtrl to render bitmaps.
    """

    def __init__(self, varianttype: str=DataViewBitmapRenderer.GetDefaultType(), mode: DataViewCellMode=DATAVIEW_CELL_INERT, align: int=DVR_DEFAULT_ALIGNMENT) -> None:
        """
        DataViewBitmapRenderer(varianttype=DataViewBitmapRenderer.GetDefaultType(), mode=DATAVIEW_CELL_INERT, align=DVR_DEFAULT_ALIGNMENT) -> None
        
        This class is used by wxDataViewCtrl to render bitmaps.
        """

    @staticmethod
    def GetDefaultType() -> str:
        """
        GetDefaultType() -> str
        
        Returns the wxVariant type used with this renderer.
        """
# end of class DataViewBitmapRenderer


class DataViewColumn(wx.SettableHeaderColumn):
    """
    DataViewColumn(title, renderer, model_column, width=DVC_DEFAULT_WIDTH, align=wx.ALIGN_CENTER, flags=DATAVIEW_COL_RESIZABLE) -> None
    DataViewColumn(bitmap, renderer, model_column, width=DVC_DEFAULT_WIDTH, align=wx.ALIGN_CENTER, flags=DATAVIEW_COL_RESIZABLE) -> None
    
    This class represents a column in a wxDataViewCtrl.
    """

    @overload
    def __init__(self, bitmap: wx.BitmapBundle, renderer: DataViewRenderer, model_column: int, width: int=DVC_DEFAULT_WIDTH, align: wx.Alignment=wx.ALIGN_CENTER, flags: int=DATAVIEW_COL_RESIZABLE) -> None:
        ...

    @overload
    def __init__(self, title: str, renderer: DataViewRenderer, model_column: int, width: int=DVC_DEFAULT_WIDTH, align: wx.Alignment=wx.ALIGN_CENTER, flags: int=DATAVIEW_COL_RESIZABLE) -> None:
        """
        DataViewColumn(title, renderer, model_column, width=DVC_DEFAULT_WIDTH, align=wx.ALIGN_CENTER, flags=DATAVIEW_COL_RESIZABLE) -> None
        DataViewColumn(bitmap, renderer, model_column, width=DVC_DEFAULT_WIDTH, align=wx.ALIGN_CENTER, flags=DATAVIEW_COL_RESIZABLE) -> None
        
        This class represents a column in a wxDataViewCtrl.
        """

    def GetModelColumn(self) -> int:
        """
        GetModelColumn() -> int
        
        Returns the index of the column of the model, which this
        wxDataViewColumn is displaying.
        """

    def GetOwner(self) -> DataViewCtrl:
        """
        GetOwner() -> DataViewCtrl
        
        Returns the owning wxDataViewCtrl.
        """

    def GetRenderer(self) -> DataViewRenderer:
        """
        GetRenderer() -> DataViewRenderer
        
        Returns the renderer of this wxDataViewColumn.
        """
    @property
    def ModelColumn(self) -> int: ...
    @property
    def Owner(self) -> DataViewCtrl: ...
    @property
    def Renderer(self) -> DataViewRenderer: ...
    Title = property(GetTitle, SetTitle)
    Bitmap = property(GetBitmap, SetBitmap)
    Width = property(GetWidth, SetWidth)
    MinWidth = property(GetMinWidth, SetMinWidth)
    Alignment = property(GetAlignment, SetAlignment)
    Flags = property(GetFlags, SetFlags)
    SortOrder = property(IsSortOrderAscending, SetSortOrder)
# end of class DataViewColumn

DataViewCtrlNameStr: str

class DataViewCtrl(wx.Control):
    """
    DataViewCtrl() -> None
    DataViewCtrl(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0, validator=wx.DefaultValidator, name=DataViewCtrlNameStr) -> None
    
    wxDataViewCtrl is a control to display data either in a tree like
    fashion or in a tabular form or both.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0, validator: wx.Validator=wx.DefaultValidator, name: str=DataViewCtrlNameStr) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        DataViewCtrl() -> None
        DataViewCtrl(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0, validator=wx.DefaultValidator, name=DataViewCtrlNameStr) -> None
        
        wxDataViewCtrl is a control to display data either in a tree like
        fashion or in a tabular form or both.
        """

    @overload
    def AppendBitmapColumn(self, label: wx.Bitmap, model_column: int, mode: DataViewCellMode=DATAVIEW_CELL_INERT, width: int=wx.COL_WIDTH_DEFAULT, align: wx.Alignment=wx.ALIGN_CENTER, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        ...

    @overload
    def AppendBitmapColumn(self, label: str, model_column: int, mode: DataViewCellMode=DATAVIEW_CELL_INERT, width: int=wx.COL_WIDTH_DEFAULT, align: wx.Alignment=wx.ALIGN_CENTER, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        """
        AppendBitmapColumn(label, model_column, mode=DATAVIEW_CELL_INERT, width=wx.COL_WIDTH_DEFAULT, align=wx.ALIGN_CENTER, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        AppendBitmapColumn(label, model_column, mode=DATAVIEW_CELL_INERT, width=wx.COL_WIDTH_DEFAULT, align=wx.ALIGN_CENTER, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        
        Appends a column for rendering a bitmap.
        """

    @overload
    def PrependBitmapColumn(self, label: wx.Bitmap, model_column: int, mode: DataViewCellMode=DATAVIEW_CELL_INERT, width: int=wx.COL_WIDTH_DEFAULT, align: wx.Alignment=wx.ALIGN_CENTER, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        ...

    @overload
    def PrependBitmapColumn(self, label: str, model_column: int, mode: DataViewCellMode=DATAVIEW_CELL_INERT, width: int=wx.COL_WIDTH_DEFAULT, align: wx.Alignment=wx.ALIGN_CENTER, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        """
        PrependBitmapColumn(label, model_column, mode=DATAVIEW_CELL_INERT, width=wx.COL_WIDTH_DEFAULT, align=wx.ALIGN_CENTER, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        PrependBitmapColumn(label, model_column, mode=DATAVIEW_CELL_INERT, width=wx.COL_WIDTH_DEFAULT, align=wx.ALIGN_CENTER, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        
        Prepends a column for rendering a bitmap.
        """

    @overload
    def AppendDateColumn(self, label: wx.Bitmap, model_column: int, mode: DataViewCellMode=DATAVIEW_CELL_ACTIVATABLE, width: int=wx.COL_WIDTH_DEFAULT, align: wx.Alignment=wx.ALIGN_NOT, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        ...

    @overload
    def AppendDateColumn(self, label: str, model_column: int, mode: DataViewCellMode=DATAVIEW_CELL_ACTIVATABLE, width: int=wx.COL_WIDTH_DEFAULT, align: wx.Alignment=wx.ALIGN_NOT, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        """
        AppendDateColumn(label, model_column, mode=DATAVIEW_CELL_ACTIVATABLE, width=wx.COL_WIDTH_DEFAULT, align=wx.ALIGN_NOT, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        AppendDateColumn(label, model_column, mode=DATAVIEW_CELL_ACTIVATABLE, width=wx.COL_WIDTH_DEFAULT, align=wx.ALIGN_NOT, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        
        Appends a column for rendering a date.
        """

    @overload
    def PrependDateColumn(self, label: wx.Bitmap, model_column: int, mode: DataViewCellMode=DATAVIEW_CELL_ACTIVATABLE, width: int=wx.COL_WIDTH_DEFAULT, align: wx.Alignment=wx.ALIGN_NOT, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        ...

    @overload
    def PrependDateColumn(self, label: str, model_column: int, mode: DataViewCellMode=DATAVIEW_CELL_ACTIVATABLE, width: int=wx.COL_WIDTH_DEFAULT, align: wx.Alignment=wx.ALIGN_NOT, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        """
        PrependDateColumn(label, model_column, mode=DATAVIEW_CELL_ACTIVATABLE, width=wx.COL_WIDTH_DEFAULT, align=wx.ALIGN_NOT, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        PrependDateColumn(label, model_column, mode=DATAVIEW_CELL_ACTIVATABLE, width=wx.COL_WIDTH_DEFAULT, align=wx.ALIGN_NOT, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        
        Prepends a column for rendering a date.
        """

    @overload
    def AppendIconTextColumn(self, label: wx.Bitmap, model_column: int, mode: DataViewCellMode=DATAVIEW_CELL_INERT, width: int=wx.COL_WIDTH_DEFAULT, align: wx.Alignment=wx.ALIGN_NOT, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        ...

    @overload
    def AppendIconTextColumn(self, label: str, model_column: int, mode: DataViewCellMode=DATAVIEW_CELL_INERT, width: int=wx.COL_WIDTH_DEFAULT, align: wx.Alignment=wx.ALIGN_NOT, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        """
        AppendIconTextColumn(label, model_column, mode=DATAVIEW_CELL_INERT, width=wx.COL_WIDTH_DEFAULT, align=wx.ALIGN_NOT, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        AppendIconTextColumn(label, model_column, mode=DATAVIEW_CELL_INERT, width=wx.COL_WIDTH_DEFAULT, align=wx.ALIGN_NOT, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        
        Appends a column for rendering text with an icon.
        """

    @overload
    def PrependIconTextColumn(self, label: wx.Bitmap, model_column: int, mode: DataViewCellMode=DATAVIEW_CELL_INERT, width: int=wx.COL_WIDTH_DEFAULT, align: wx.Alignment=wx.ALIGN_NOT, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        ...

    @overload
    def PrependIconTextColumn(self, label: str, model_column: int, mode: DataViewCellMode=DATAVIEW_CELL_INERT, width: int=wx.COL_WIDTH_DEFAULT, align: wx.Alignment=wx.ALIGN_NOT, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        """
        PrependIconTextColumn(label, model_column, mode=DATAVIEW_CELL_INERT, width=wx.COL_WIDTH_DEFAULT, align=wx.ALIGN_NOT, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        PrependIconTextColumn(label, model_column, mode=DATAVIEW_CELL_INERT, width=wx.COL_WIDTH_DEFAULT, align=wx.ALIGN_NOT, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        
        Prepends a column for rendering text with an icon.
        """

    @overload
    def AppendProgressColumn(self, label: wx.Bitmap, model_column: int, mode: DataViewCellMode=DATAVIEW_CELL_INERT, width: int=80, align: wx.Alignment=wx.ALIGN_CENTER, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        ...

    @overload
    def AppendProgressColumn(self, label: str, model_column: int, mode: DataViewCellMode=DATAVIEW_CELL_INERT, width: int=80, align: wx.Alignment=wx.ALIGN_CENTER, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        """
        AppendProgressColumn(label, model_column, mode=DATAVIEW_CELL_INERT, width=80, align=wx.ALIGN_CENTER, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        AppendProgressColumn(label, model_column, mode=DATAVIEW_CELL_INERT, width=80, align=wx.ALIGN_CENTER, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        
        Appends a column for rendering a progress indicator.
        """

    @overload
    def PrependProgressColumn(self, label: wx.Bitmap, model_column: int, mode: DataViewCellMode=DATAVIEW_CELL_INERT, width: int=80, align: wx.Alignment=wx.ALIGN_CENTER, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        ...

    @overload
    def PrependProgressColumn(self, label: str, model_column: int, mode: DataViewCellMode=DATAVIEW_CELL_INERT, width: int=80, align: wx.Alignment=wx.ALIGN_CENTER, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        """
        PrependProgressColumn(label, model_column, mode=DATAVIEW_CELL_INERT, width=80, align=wx.ALIGN_CENTER, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        PrependProgressColumn(label, model_column, mode=DATAVIEW_CELL_INERT, width=80, align=wx.ALIGN_CENTER, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        
        Prepends a column for rendering a progress indicator.
        """

    @overload
    def AppendTextColumn(self, label: wx.Bitmap, model_column: int, mode: DataViewCellMode=DATAVIEW_CELL_INERT, width: int=wx.COL_WIDTH_DEFAULT, align: wx.Alignment=wx.ALIGN_NOT, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        ...

    @overload
    def AppendTextColumn(self, label: str, model_column: int, mode: DataViewCellMode=DATAVIEW_CELL_INERT, width: int=wx.COL_WIDTH_DEFAULT, align: wx.Alignment=wx.ALIGN_NOT, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        """
        AppendTextColumn(label, model_column, mode=DATAVIEW_CELL_INERT, width=wx.COL_WIDTH_DEFAULT, align=wx.ALIGN_NOT, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        AppendTextColumn(label, model_column, mode=DATAVIEW_CELL_INERT, width=wx.COL_WIDTH_DEFAULT, align=wx.ALIGN_NOT, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        
        Appends a column for rendering text.
        """

    @overload
    def PrependTextColumn(self, label: wx.Bitmap, model_column: int, mode: DataViewCellMode=DATAVIEW_CELL_INERT, width: int=wx.COL_WIDTH_DEFAULT, align: wx.Alignment=wx.ALIGN_NOT, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        ...

    @overload
    def PrependTextColumn(self, label: str, model_column: int, mode: DataViewCellMode=DATAVIEW_CELL_INERT, width: int=wx.COL_WIDTH_DEFAULT, align: wx.Alignment=wx.ALIGN_NOT, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        """
        PrependTextColumn(label, model_column, mode=DATAVIEW_CELL_INERT, width=wx.COL_WIDTH_DEFAULT, align=wx.ALIGN_NOT, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        PrependTextColumn(label, model_column, mode=DATAVIEW_CELL_INERT, width=wx.COL_WIDTH_DEFAULT, align=wx.ALIGN_NOT, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        
        Prepends a column for rendering text.
        """

    @overload
    def AppendToggleColumn(self, label: wx.Bitmap, model_column: int, mode: DataViewCellMode=DATAVIEW_CELL_INERT, width: int=30, align: wx.Alignment=wx.ALIGN_CENTER, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        ...

    @overload
    def AppendToggleColumn(self, label: str, model_column: int, mode: DataViewCellMode=DATAVIEW_CELL_INERT, width: int=30, align: wx.Alignment=wx.ALIGN_CENTER, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        """
        AppendToggleColumn(label, model_column, mode=DATAVIEW_CELL_INERT, width=30, align=wx.ALIGN_CENTER, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        AppendToggleColumn(label, model_column, mode=DATAVIEW_CELL_INERT, width=30, align=wx.ALIGN_CENTER, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        
        Appends a column for rendering a toggle.
        """

    @overload
    def PrependToggleColumn(self, label: wx.Bitmap, model_column: int, mode: DataViewCellMode=DATAVIEW_CELL_INERT, width: int=30, align: wx.Alignment=wx.ALIGN_CENTER, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        ...

    @overload
    def PrependToggleColumn(self, label: str, model_column: int, mode: DataViewCellMode=DATAVIEW_CELL_INERT, width: int=30, align: wx.Alignment=wx.ALIGN_CENTER, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        """
        PrependToggleColumn(label, model_column, mode=DATAVIEW_CELL_INERT, width=30, align=wx.ALIGN_CENTER, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        PrependToggleColumn(label, model_column, mode=DATAVIEW_CELL_INERT, width=30, align=wx.ALIGN_CENTER, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        
        Prepends a column for rendering a toggle.
        """

    def AllowMultiColumnSort(self, allow: bool) -> bool:
        """
        AllowMultiColumnSort(allow) -> bool
        
        Call to allow using multiple columns for sorting.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=0, validator: wx.Validator=wx.DefaultValidator, name: str=DataViewCtrlNameStr) -> bool:
        """
        Create(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=0, validator=wx.DefaultValidator, name=DataViewCtrlNameStr) -> bool
        
        Create the control.
        """

    def AppendColumn(self, col: DataViewColumn) -> bool:
        """
        AppendColumn(col) -> bool
        
        Appends a wxDataViewColumn to the control.
        """

    def PrependColumn(self, col: DataViewColumn) -> bool:
        """
        PrependColumn(col) -> bool
        
        Prepends a wxDataViewColumn to the control.
        """

    def InsertColumn(self, pos: int, col: DataViewColumn) -> bool:
        """
        InsertColumn(pos, col) -> bool
        
        Inserts a wxDataViewColumn to the control.
        """

    def _AssociateModel(self, model: DataViewModel) -> bool:
        """
        _AssociateModel(model) -> bool
        
        Associates a wxDataViewModel with the control.
        """

    def ClearColumns(self) -> bool:
        """
        ClearColumns() -> bool
        
        Removes all columns.
        """

    def Collapse(self, item: DataViewItem) -> None:
        """
        Collapse(item) -> None
        
        Collapses the item.
        """

    def DeleteColumn(self, column: DataViewColumn) -> bool:
        """
        DeleteColumn(column) -> bool
        
        Deletes given column.
        """

    def EditItem(self, item: DataViewItem, column: DataViewColumn) -> None:
        """
        EditItem(item, column) -> None
        
        Programmatically starts editing given cell of item.
        """

    def EnableDragSource(self, format: wx.DataFormat) -> bool:
        """
        EnableDragSource(format) -> bool
        
        Enable drag operations using the given format.
        """

    def EnableDropTargets(self, formats: VectorwxDataFormat) -> bool:
        """
        EnableDropTargets(formats) -> bool
        
        Enable drop operations using any of the specified formats.
        """

    def EnableDropTarget(self, format: wx.DataFormat) -> bool:
        """
        EnableDropTarget(format) -> bool
        
        Enable drop operations using the given format.
        """

    def EnsureVisible(self, item: DataViewItem, column: Optional[DataViewColumn]=None) -> None:
        """
        EnsureVisible(item, column=None) -> None
        
        Call this to ensure that the given item is visible.
        """

    def Expand(self, item: DataViewItem) -> None:
        """
        Expand(item) -> None
        
        Expands the item.
        """

    def ExpandAncestors(self, item: DataViewItem) -> None:
        """
        ExpandAncestors(item) -> None
        
        Expands all ancestors of the item.
        """

    def ExpandChildren(self, item: DataViewItem) -> None:
        """
        ExpandChildren(item) -> None
        
        Expand all children of the given item recursively.
        """

    def GetColumn(self, pos: int) -> DataViewColumn:
        """
        GetColumn(pos) -> DataViewColumn
        
        Returns pointer to the column.
        """

    def GetColumnCount(self) -> int:
        """
        GetColumnCount() -> int
        
        Returns the number of columns.
        """

    def GetColumnPosition(self, column: DataViewColumn) -> int:
        """
        GetColumnPosition(column) -> int
        
        Returns the position of the column or -1 if not found in the control.
        """

    def GetExpanderColumn(self) -> DataViewColumn:
        """
        GetExpanderColumn() -> DataViewColumn
        
        Returns column containing the expanders.
        """

    def GetCurrentItem(self) -> DataViewItem:
        """
        GetCurrentItem() -> DataViewItem
        
        Returns the currently focused item.
        """

    def GetCurrentColumn(self) -> DataViewColumn:
        """
        GetCurrentColumn() -> DataViewColumn
        
        Returns the column that currently has focus.
        """

    def GetIndent(self) -> int:
        """
        GetIndent() -> int
        
        Returns indentation.
        """

    def GetItemRect(self, item: DataViewItem, col: Optional[DataViewColumn]=None) -> wx.Rect:
        """
        GetItemRect(item, col=None) -> wx.Rect
        
        Returns item rectangle.
        """

    def GetMainWindow(self) -> wx.Window:
        """
        GetMainWindow() -> wx.Window
        
        Returns the window corresponding to the main area of the control.
        """

    def GetModel(self) -> DataViewModel:
        """
        GetModel() -> DataViewModel
        
        Returns pointer to the data model associated with the control (if
        any).
        """

    def GetSelectedItemsCount(self) -> int:
        """
        GetSelectedItemsCount() -> int
        
        Returns the number of currently selected items.
        """

    def GetSelection(self) -> DataViewItem:
        """
        GetSelection() -> DataViewItem
        
        Returns first selected item or an invalid item if none is selected.
        """

    def GetSelections(self) -> DataViewItemArray:
        """
        GetSelections() -> DataViewItemArray
        
        Returns a list of the currently selected items.
        """

    def GetSortingColumn(self) -> DataViewColumn:
        """
        GetSortingColumn() -> DataViewColumn
        
        Returns the wxDataViewColumn currently responsible for sorting or NULL
        if none has been selected.
        """

    def HasSelection(self) -> bool:
        """
        HasSelection() -> bool
        
        Returns true if any items are currently selected.
        """

    def HitTest(self, point: wx.Point) -> Any:
        """
        HitTest(point) -> Any
        
        HitTest(point) -> (item, col)
        
        Returns the item and column located at point, as a 2 element tuple.
        """

    def IsExpanded(self, item: DataViewItem) -> bool:
        """
        IsExpanded(item) -> bool
        
        Return true if the item is expanded.
        """

    def IsMultiColumnSortAllowed(self) -> bool:
        """
        IsMultiColumnSortAllowed() -> bool
        
        Return true if using more than one column for sorting is allowed.
        """

    def IsSelected(self, item: DataViewItem) -> bool:
        """
        IsSelected(item) -> bool
        
        Return true if the item is selected.
        """

    def Select(self, item: DataViewItem) -> None:
        """
        Select(item) -> None
        
        Select the given item.
        """

    def SelectAll(self) -> None:
        """
        SelectAll() -> None
        
        Select all items.
        """

    def SetAlternateRowColour(self, colour: wx.Colour) -> bool:
        """
        SetAlternateRowColour(colour) -> bool
        
        Set custom colour for the alternate rows used with wxDV_ROW_LINES
        style.
        """

    def SetExpanderColumn(self, col: DataViewColumn) -> None:
        """
        SetExpanderColumn(col) -> None
        
        Set which column shall contain the tree-like expanders.
        """

    def SetCurrentItem(self, item: DataViewItem) -> None:
        """
        SetCurrentItem(item) -> None
        
        Changes the currently focused item.
        """

    def SetHeaderAttr(self, attr: wx.ItemAttr) -> bool:
        """
        SetHeaderAttr(attr) -> bool
        
        Set custom colours and/or font to use for the header.
        """

    def SetIndent(self, indent: int) -> None:
        """
        SetIndent(indent) -> None
        
        Sets the indentation.
        """

    def SetSelections(self, sel: DataViewItemArray) -> None:
        """
        SetSelections(sel) -> None
        
        Sets the selection to the array of wxDataViewItems.
        """

    def Unselect(self, item: DataViewItem) -> None:
        """
        Unselect(item) -> None
        
        Unselect the given item.
        """

    def UnselectAll(self) -> None:
        """
        UnselectAll() -> None
        
        Unselect all item.
        """

    def SetRowHeight(self, rowHeight: int) -> bool:
        """
        SetRowHeight(rowHeight) -> bool
        
        Sets the row height.
        """

    def ToggleSortByColumn(self, column: int) -> None:
        """
        ToggleSortByColumn(column) -> None
        
        Toggle sorting by the given column.
        """

    def GetCountPerPage(self) -> int:
        """
        GetCountPerPage() -> int
        
        Return the number of items that can fit vertically in the visible area
        of the control.
        """

    def GetTopItem(self) -> DataViewItem:
        """
        GetTopItem() -> DataViewItem
        
        Return the topmost visible item.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """

    def EnableSystemTheme(self, enable: bool=True) -> None:
        """
        EnableSystemTheme(enable=True) -> None
        
        Can be used to disable the system theme of controls using it by
        default.
        """

    def AssociateModel(self, model):
        """
        Associates a :class:`DataViewModel` with the control.
        Ownership of the model object is passed to C++, however it
        is reference counted so it can be shared with other views.
        """

    def GetColumns(self):
        """
        Returns a list of column objects.
        """
    @property
    def ColumnCount(self) -> int: ...
    Columns = property(GetColumns)
    @property
    def CountPerPage(self) -> int: ...
    @property
    def CurrentColumn(self) -> DataViewColumn: ...
    @property
    def CurrentItem(self) -> DataViewItem: ...
    @CurrentItem.setter
    def CurrentItem(self, value: DataViewItem, /) -> None: ...
    @property
    def ExpanderColumn(self) -> DataViewColumn: ...
    @ExpanderColumn.setter
    def ExpanderColumn(self, value: DataViewColumn, /) -> None: ...
    @property
    def Indent(self) -> int: ...
    @Indent.setter
    def Indent(self, value: int, /) -> None: ...
    @property
    def MainWindow(self) -> wx.Window: ...
    @property
    def Model(self) -> DataViewModel: ...
    @property
    def SelectedItemsCount(self) -> int: ...
    @property
    def Selection(self) -> DataViewItem: ...
    @property
    def Selections(self) -> DataViewItemArray: ...
    @Selections.setter
    def Selections(self, value: DataViewItemArray, /) -> None: ...
    @property
    def SortingColumn(self) -> DataViewColumn: ...
    @property
    def TopItem(self) -> DataViewItem: ...
# end of class DataViewCtrl


class DataViewEvent(wx.NotifyEvent):
    """
    DataViewEvent() -> None
    DataViewEvent(evtType, dvc, column, item=DataViewItem()) -> None
    DataViewEvent(evtType, dvc, item) -> None
    DataViewEvent(event) -> None
    
    This is the event class for the wxDataViewCtrl notifications.
    """

    @overload
    def __init__(self, evtType: EventType, dvc: DataViewCtrl, column: DataViewColumn, item: DataViewItem=DataViewItem()) -> None:
        ...

    @overload
    def __init__(self, evtType: EventType, dvc: DataViewCtrl, item: DataViewItem) -> None:
        ...

    @overload
    def __init__(self, event: DataViewEvent) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        DataViewEvent() -> None
        DataViewEvent(evtType, dvc, column, item=DataViewItem()) -> None
        DataViewEvent(evtType, dvc, item) -> None
        DataViewEvent(event) -> None
        
        This is the event class for the wxDataViewCtrl notifications.
        """

    def GetColumn(self) -> int:
        """
        GetColumn() -> int
        
        Returns the position of the column in the control or -1 if column
        field is unavailable for this event.
        """

    def GetDataViewColumn(self) -> DataViewColumn:
        """
        GetDataViewColumn() -> DataViewColumn
        
        Returns a pointer to the wxDataViewColumn from which the event was
        emitted or NULL.
        """

    def GetModel(self) -> DataViewModel:
        """
        GetModel() -> DataViewModel
        
        Returns the wxDataViewModel associated with the event.
        """

    def GetPosition(self) -> wx.Point:
        """
        GetPosition() -> wx.Point
        
        Returns the position of a context menu event in client coordinates.
        """

    def GetValue(self) -> DVCVariant:
        """
        GetValue() -> DVCVariant
        
        Returns a reference to a value.
        """

    def IsEditCancelled(self) -> bool:
        """
        IsEditCancelled() -> bool
        
        Can be used to determine whether the new value is going to be accepted
        in wxEVT_DATAVIEW_ITEM_EDITING_DONE handler.
        """

    def SetColumn(self, col: int) -> None:
        """
        SetColumn(col) -> None
        
        Sets the column index associated with this event.
        """

    def SetDataViewColumn(self, col: DataViewColumn) -> None:
        """
        SetDataViewColumn(col) -> None
        
        For wxEVT_DATAVIEW_COLUMN_HEADER_CLICK only.
        """

    def SetModel(self, model: DataViewModel) -> None:
        """
        SetModel(model) -> None
        
        Sets the dataview model associated with this event.
        """

    def SetValue(self, value: DVCVariant) -> None:
        """
        SetValue(value) -> None
        
        Sets the value associated with this event.
        """

    def SetDataObject(self, obj: wx.DataObject) -> None:
        """
        SetDataObject(obj) -> None
        
        Set wxDataObject for data transfer within a drag operation.
        """

    def GetDataFormat(self) -> wx.DataFormat:
        """
        GetDataFormat() -> wx.DataFormat
        
        Gets the wxDataFormat during a drop operation.
        """

    def GetDataSize(self) -> int:
        """
        GetDataSize() -> int
        
        Gets the data size for a drop data transfer.
        """

    def GetDataBuffer(self) -> Any:
        """
        GetDataBuffer() -> Any
        
        Gets the data buffer for a drop data transfer
        """

    def SetDragFlags(self, flags: int) -> None:
        """
        SetDragFlags(flags) -> None
        
        Specify the kind of the drag operation to perform.
        """

    def GetDropEffect(self) -> wx.DragResult:
        """
        GetDropEffect() -> wx.DragResult
        
        Returns the effect the user requested to happen to the dropped data.
        """

    def GetCacheFrom(self) -> int:
        """
        GetCacheFrom() -> int
        
        Return the first row that will be displayed.
        """

    def GetCacheTo(self) -> int:
        """
        GetCacheTo() -> int
        
        Return the last row that will be displayed.
        """

    def GetProposedDropIndex(self) -> int:
        """
        GetProposedDropIndex() -> int
        
        Returns the index of the child item at which an item currently being
        dragged would be dropped.
        """

    def GetItem(self) -> DataViewItem:
        """
        GetItem() -> DataViewItem
        
        Returns the item affected by the event.
        """

    def SetItem(self, item: DataViewItem) -> None:
        """
        SetItem(item) -> None
        """

    def SetPosition(self, x: int, y: int) -> None:
        """
        SetPosition(x, y) -> None
        """

    def SetCache(self, from_: int, to_: int) -> None:
        """
        SetCache(from_, to_) -> None
        """

    def GetDataObject(self) -> wx.DataObject:
        """
        GetDataObject() -> wx.DataObject
        """

    def SetDataFormat(self, format: wx.DataFormat) -> None:
        """
        SetDataFormat(format) -> None
        """

    def SetDataSize(self, size: int) -> None:
        """
        SetDataSize(size) -> None
        """

    def SetDataBuffer(self, buf: Any) -> None:
        """
        SetDataBuffer(buf) -> None
        """

    def GetDragFlags(self) -> int:
        """
        GetDragFlags() -> int
        """

    def SetDropEffect(self, effect: wx.DragResult) -> None:
        """
        SetDropEffect(effect) -> None
        """
    @property
    def CacheFrom(self) -> int: ...
    @property
    def CacheTo(self) -> int: ...
    @property
    def Column(self) -> int: ...
    @Column.setter
    def Column(self, value: int, /) -> None: ...
    @property
    def DataBuffer(self) -> Any: ...
    @DataBuffer.setter
    def DataBuffer(self, value: Any, /) -> None: ...
    @property
    def DataFormat(self) -> wx.DataFormat: ...
    @DataFormat.setter
    def DataFormat(self, value: wx.DataFormat, /) -> None: ...
    @property
    def DataObject(self) -> wx.DataObject: ...
    @DataObject.setter
    def DataObject(self, value: wx.DataObject, /) -> None: ...
    @property
    def DataSize(self) -> int: ...
    @DataSize.setter
    def DataSize(self, value: int, /) -> None: ...
    @property
    def DataViewColumn(self) -> DataViewColumn: ...
    @DataViewColumn.setter
    def DataViewColumn(self, value: DataViewColumn, /) -> None: ...
    @property
    def DragFlags(self) -> int: ...
    @DragFlags.setter
    def DragFlags(self, value: int, /) -> None: ...
    @property
    def DropEffect(self) -> wx.DragResult: ...
    @DropEffect.setter
    def DropEffect(self, value: wx.DragResult, /) -> None: ...
    @property
    def Item(self) -> DataViewItem: ...
    @Item.setter
    def Item(self, value: DataViewItem, /) -> None: ...
    @property
    def Model(self) -> DataViewModel: ...
    @Model.setter
    def Model(self, value: DataViewModel, /) -> None: ...
    @property
    def Position(self) -> int: ...
    @Position.setter
    def Position(self, value: int, /) -> None: ...
    @property
    def ProposedDropIndex(self) -> int: ...
    @property
    def Value(self) -> DVCVariant: ...
    @Value.setter
    def Value(self, value: DVCVariant, /) -> None: ...
# end of class DataViewEvent


class DataViewValueAdjuster:
    """
    This class can be used with wxDataViewRenderer::SetValueAdjuster() to
    customize rendering of model values with standard renderers.
    """

    def MakeHighlighted(self, value: DVCVariant) -> DVCVariant:
        """
        MakeHighlighted(value) -> DVCVariant
        
        Change value for rendering when highlighted.
        """
# end of class DataViewValueAdjuster


class DataViewListCtrl(DataViewCtrl):
    """
    DataViewListCtrl() -> None
    DataViewListCtrl(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=DV_ROW_LINES, validator=wx.DefaultValidator) -> None
    
    This class is a wxDataViewCtrl which internally uses a
    wxDataViewListStore and forwards most of its API to that class.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=DV_ROW_LINES, validator: wx.Validator=wx.DefaultValidator) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        DataViewListCtrl() -> None
        DataViewListCtrl(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=DV_ROW_LINES, validator=wx.DefaultValidator) -> None
        
        This class is a wxDataViewCtrl which internally uses a
        wxDataViewListStore and forwards most of its API to that class.
        """

    def GetStore(self) -> DataViewListStore:
        """
        GetStore() -> DataViewListStore
        
        Returns the store.
        """

    def GetSelectedRow(self) -> int:
        """
        GetSelectedRow() -> int
        
        Returns index of the selected row or wxNOT_FOUND.
        """

    def SelectRow(self, row: int) -> None:
        """
        SelectRow(row) -> None
        
        Selects given row.
        """

    def UnselectRow(self, row: int) -> None:
        """
        UnselectRow(row) -> None
        
        Unselects given row.
        """

    def IsRowSelected(self, row: int) -> bool:
        """
        IsRowSelected(row) -> bool
        
        Returns true if row is selected.
        """

    @overload
    def AppendColumn(self, column: DataViewColumn, varianttype: str) -> None:
        ...

    @overload
    def AppendColumn(self, column: DataViewColumn) -> bool:
        """
        AppendColumn(column) -> bool
        AppendColumn(column, varianttype) -> None
        
        Appends a column to the control and additionally appends a column to
        the store with the type string.
        """

    def AppendTextColumn(self, label: str, mode: DataViewCellMode=DATAVIEW_CELL_INERT, width: int=wx.COL_WIDTH_DEFAULT, align: wx.Alignment=wx.ALIGN_LEFT, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        """
        AppendTextColumn(label, mode=DATAVIEW_CELL_INERT, width=wx.COL_WIDTH_DEFAULT, align=wx.ALIGN_LEFT, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        
        Appends a text column to the control and the store.
        """

    def AppendToggleColumn(self, label: str, mode: DataViewCellMode=DATAVIEW_CELL_ACTIVATABLE, width: int=wx.COL_WIDTH_DEFAULT, align: wx.Alignment=wx.ALIGN_LEFT, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        """
        AppendToggleColumn(label, mode=DATAVIEW_CELL_ACTIVATABLE, width=wx.COL_WIDTH_DEFAULT, align=wx.ALIGN_LEFT, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        
        Appends a toggle column to the control and the store.
        """

    def AppendProgressColumn(self, label: str, mode: DataViewCellMode=DATAVIEW_CELL_INERT, width: int=wx.COL_WIDTH_DEFAULT, align: wx.Alignment=wx.ALIGN_LEFT, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        """
        AppendProgressColumn(label, mode=DATAVIEW_CELL_INERT, width=wx.COL_WIDTH_DEFAULT, align=wx.ALIGN_LEFT, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        
        Appends a progress column to the control and the store.
        """

    def AppendIconTextColumn(self, label: str, mode: DataViewCellMode=DATAVIEW_CELL_INERT, width: int=wx.COL_WIDTH_DEFAULT, align: wx.Alignment=wx.ALIGN_LEFT, flags: int=DATAVIEW_COL_RESIZABLE) -> DataViewColumn:
        """
        AppendIconTextColumn(label, mode=DATAVIEW_CELL_INERT, width=wx.COL_WIDTH_DEFAULT, align=wx.ALIGN_LEFT, flags=DATAVIEW_COL_RESIZABLE) -> DataViewColumn
        
        Appends an icon-and-text column to the control and the store.
        """

    @overload
    def InsertColumn(self, pos: int, column: DataViewColumn, varianttype: str) -> None:
        ...

    @overload
    def InsertColumn(self, pos: int, column: DataViewColumn) -> bool:
        """
        InsertColumn(pos, column) -> bool
        InsertColumn(pos, column, varianttype) -> None
        
        Inserts a column to the control and additionally inserts a column to
        the store with the type string.
        """

    @overload
    def PrependColumn(self, column: DataViewColumn, varianttype: str) -> None:
        ...

    @overload
    def PrependColumn(self, column: DataViewColumn) -> bool:
        """
        PrependColumn(column) -> bool
        PrependColumn(column, varianttype) -> None
        
        Prepends a column to the control and additionally prepends a column to
        the store with the type string.
        """

    def AppendItem(self, values: VariantVector, data: Optional[UIntPtr]=None) -> None:
        """
        AppendItem(values, data=None) -> None
        
        Appends an item (i.e. a row) to the control.
        """

    def PrependItem(self, values: VariantVector, data: Optional[UIntPtr]=None) -> None:
        """
        PrependItem(values, data=None) -> None
        
        Prepends an item (i.e. a row) to the control.
        """

    def InsertItem(self, row: int, values: VariantVector, data: Optional[UIntPtr]=None) -> None:
        """
        InsertItem(row, values, data=None) -> None
        
        Inserts an item (i.e. a row) to the control.
        """

    def DeleteItem(self, row: int) -> None:
        """
        DeleteItem(row) -> None
        
        Delete the row at position row.
        """

    def DeleteAllItems(self) -> None:
        """
        DeleteAllItems() -> None
        
        Delete all items (= all rows).
        """

    def GetItemCount(self) -> int:
        """
        GetItemCount() -> int
        
        Returns the number of items (=rows) in the control.
        """

    def GetItemData(self, item: DataViewItem) -> UIntPtr:
        """
        GetItemData(item) -> UIntPtr
        
        Returns the client data associated with the item.
        """

    def SetValue(self, value: DVCVariant, row: int, col: int) -> None:
        """
        SetValue(value, row, col) -> None
        
        Sets the value in the store and update the control.
        """

    def GetValue(self, row: int, col: int) -> DVCVariant:
        """
        GetValue(row, col) -> DVCVariant
        
        Returns the value from the store.
        """

    def SetTextValue(self, value: str, row: int, col: int) -> None:
        """
        SetTextValue(value, row, col) -> None
        
        Sets the value in the store and update the control.
        """

    def GetTextValue(self, row: int, col: int) -> str:
        """
        GetTextValue(row, col) -> str
        
        Returns the value from the store.
        """

    def SetToggleValue(self, value: bool, row: int, col: int) -> None:
        """
        SetToggleValue(value, row, col) -> None
        
        Sets the value in the store and update the control.
        """

    def GetToggleValue(self, row: int, col: int) -> bool:
        """
        GetToggleValue(row, col) -> bool
        
        Returns the value from the store.
        """

    def SetItemData(self, item: DataViewItem, data: UIntPtr) -> None:
        """
        SetItemData(item, data) -> None
        
        Associates a client data pointer with the given item.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=DV_ROW_LINES, validator: wx.Validator=wx.DefaultValidator) -> bool:
        """
        Create(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=DV_ROW_LINES, validator=wx.DefaultValidator) -> bool
        
        Creates the control and a wxDataViewListStore as its internal model.
        """

    def ItemToRow(self, item: DataViewItem) -> int:
        """
        ItemToRow(item) -> int
        
        Returns the position of given item or wxNOT_FOUND if it's not a valid
        item.
        """

    def RowToItem(self, row: int) -> DataViewItem:
        """
        RowToItem(row) -> DataViewItem
        
        Returns the wxDataViewItem at the given row.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def ItemCount(self) -> int: ...
    @property
    def SelectedRow(self) -> int: ...
    @property
    def Store(self) -> DataViewListStore: ...
# end of class DataViewListCtrl


class DataViewListStore(DataViewIndexListModel):
    """
    DataViewListStore() -> None
    
    wxDataViewListStore is a specialised wxDataViewModel for storing a
    simple table of data.
    """

    def __init__(self) -> None:
        """
        DataViewListStore() -> None
        
        wxDataViewListStore is a specialised wxDataViewModel for storing a
        simple table of data.
        """

    def PrependColumn(self, varianttype: str) -> None:
        """
        PrependColumn(varianttype) -> None
        
        Prepends a data column.
        """

    def InsertColumn(self, pos: int, varianttype: str) -> None:
        """
        InsertColumn(pos, varianttype) -> None
        
        Inserts a data column before pos.
        """

    def AppendColumn(self, varianttype: str) -> None:
        """
        AppendColumn(varianttype) -> None
        
        Appends a data column.
        """

    def AppendItem(self, values: VariantVector, data: Optional[UIntPtr]=None) -> None:
        """
        AppendItem(values, data=None) -> None
        
        Appends an item (=row) and fills it with values.
        """

    def PrependItem(self, values: VariantVector, data: Optional[UIntPtr]=None) -> None:
        """
        PrependItem(values, data=None) -> None
        
        Prepends an item (=row) and fills it with values.
        """

    def InsertItem(self, row: int, values: VariantVector, data: Optional[UIntPtr]=None) -> None:
        """
        InsertItem(row, values, data=None) -> None
        
        Inserts an item (=row) and fills it with values.
        """

    def DeleteItem(self, pos: int) -> None:
        """
        DeleteItem(pos) -> None
        
        Delete the item (=row) at position pos.
        """

    def DeleteAllItems(self) -> None:
        """
        DeleteAllItems() -> None
        
        Delete all item (=all rows) in the store.
        """

    def GetItemCount(self) -> int:
        """
        GetItemCount() -> int
        
        Returns the number of items (=rows) in the control.
        """

    def GetItemData(self, item: DataViewItem) -> UIntPtr:
        """
        GetItemData(item) -> UIntPtr
        
        Returns the client data associated with the item.
        """

    def SetItemData(self, item: DataViewItem, data: UIntPtr) -> None:
        """
        SetItemData(item, data) -> None
        
        Sets the client data associated with the item.
        """

    def GetValueByRow(self, row: int, col: int) -> DVCVariant:
        """
        GetValueByRow(row, col) -> DVCVariant
        
        Overridden from wxDataViewIndexListModel.
        """

    def SetValueByRow(self, value: DVCVariant, row: int, col: int) -> bool:
        """
        SetValueByRow(value, row, col) -> bool
        
        Overridden from wxDataViewIndexListModel.
        """
    @property
    def ItemCount(self) -> int: ...
# end of class DataViewListStore


class DataViewTreeCtrl(DataViewCtrl):
    """
    DataViewTreeCtrl() -> None
    DataViewTreeCtrl(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=DV_NO_HEADER|DV_ROW_LINES, validator=wx.DefaultValidator) -> None
    
    This class is a wxDataViewCtrl which internally uses a
    wxDataViewTreeStore and forwards most of its API to that class.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=DV_NO_HEADER|DV_ROW_LINES, validator: wx.Validator=wx.DefaultValidator) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        DataViewTreeCtrl() -> None
        DataViewTreeCtrl(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=DV_NO_HEADER|DV_ROW_LINES, validator=wx.DefaultValidator) -> None
        
        This class is a wxDataViewCtrl which internally uses a
        wxDataViewTreeStore and forwards most of its API to that class.
        """

    def GetStore(self) -> DataViewTreeStore:
        """
        GetStore() -> DataViewTreeStore
        
        Returns the store.
        """

    def AppendContainer(self, parent: DataViewItem, text: str, icon: int=-1, expanded: int=-1, data: Optional[ClientData]=None) -> DataViewItem:
        """
        AppendContainer(parent, text, icon=-1, expanded=-1, data=None) -> DataViewItem
        
        Appends a container to the given parent.
        """

    def AppendItem(self, parent: DataViewItem, text: str, icon: int=-1, data: Optional[ClientData]=None) -> DataViewItem:
        """
        AppendItem(parent, text, icon=-1, data=None) -> DataViewItem
        
        Appends an item to the given parent.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=DV_NO_HEADER|DV_ROW_LINES, validator: wx.Validator=wx.DefaultValidator) -> bool:
        """
        Create(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=DV_NO_HEADER|DV_ROW_LINES, validator=wx.DefaultValidator) -> bool
        
        Creates the control and a wxDataViewTreeStore as its internal model.
        """

    def DeleteAllItems(self) -> None:
        """
        DeleteAllItems() -> None
        
        Calls the identical method from wxDataViewTreeStore.
        """

    def DeleteChildren(self, item: DataViewItem) -> None:
        """
        DeleteChildren(item) -> None
        
        Calls the identical method from wxDataViewTreeStore.
        """

    def DeleteItem(self, item: DataViewItem) -> None:
        """
        DeleteItem(item) -> None
        
        Calls the identical method from wxDataViewTreeStore.
        """

    def GetChildCount(self, parent: DataViewItem) -> int:
        """
        GetChildCount(parent) -> int
        
        Calls the identical method from wxDataViewTreeStore.
        """

    def GetImageList(self) -> wx.ImageList:
        """
        GetImageList() -> wx.ImageList
        
        Returns the image list.
        """

    def GetItemData(self, item: DataViewItem) -> ClientData:
        """
        GetItemData(item) -> ClientData
        
        Calls the identical method from wxDataViewTreeStore.
        """

    def GetItemExpandedIcon(self, item: DataViewItem) -> wx.Icon:
        """
        GetItemExpandedIcon(item) -> wx.Icon
        
        Calls the identical method from wxDataViewTreeStore.
        """

    def GetItemIcon(self, item: DataViewItem) -> wx.Icon:
        """
        GetItemIcon(item) -> wx.Icon
        
        Calls the identical method from wxDataViewTreeStore.
        """

    def GetItemParent(self, item: DataViewItem) -> DataViewItem:
        """
        GetItemParent(item) -> DataViewItem
        
        Returns the item's parent.
        """

    def GetItemText(self, item: DataViewItem) -> str:
        """
        GetItemText(item) -> str
        
        Calls the identical method from wxDataViewTreeStore.
        """

    def GetNthChild(self, parent: DataViewItem, pos: int) -> DataViewItem:
        """
        GetNthChild(parent, pos) -> DataViewItem
        
        Calls the identical method from wxDataViewTreeStore.
        """

    def InsertContainer(self, parent: DataViewItem, previous: DataViewItem, text: str, icon: int=-1, expanded: int=-1, data: Optional[ClientData]=None) -> DataViewItem:
        """
        InsertContainer(parent, previous, text, icon=-1, expanded=-1, data=None) -> DataViewItem
        
        Calls the same method from wxDataViewTreeStore but uses an index
        position in the image list instead of a wxIcon.
        """

    def InsertItem(self, parent: DataViewItem, previous: DataViewItem, text: str, icon: int=-1, data: Optional[ClientData]=None) -> DataViewItem:
        """
        InsertItem(parent, previous, text, icon=-1, data=None) -> DataViewItem
        
        Calls the same method from wxDataViewTreeStore but uses an index
        position in the image list instead of a wxIcon.
        """

    def IsContainer(self, item: DataViewItem) -> bool:
        """
        IsContainer(item) -> bool
        
        Returns true if item is a container.
        """

    def PrependContainer(self, parent: DataViewItem, text: str, icon: int=-1, expanded: int=-1, data: Optional[ClientData]=None) -> DataViewItem:
        """
        PrependContainer(parent, text, icon=-1, expanded=-1, data=None) -> DataViewItem
        
        Calls the same method from wxDataViewTreeStore but uses an index
        position in the image list instead of a wxIcon.
        """

    def PrependItem(self, parent: DataViewItem, text: str, icon: int=-1, data: Optional[ClientData]=None) -> DataViewItem:
        """
        PrependItem(parent, text, icon=-1, data=None) -> DataViewItem
        
        Calls the same method from wxDataViewTreeStore but uses an index
        position in the image list instead of a wxIcon.
        """

    def SetImageList(self, imagelist: wx.ImageList) -> None:
        """
        SetImageList(imagelist) -> None
        
        Sets the image list.
        """

    def SetItemData(self, item: DataViewItem, data: ClientData) -> None:
        """
        SetItemData(item, data) -> None
        
        Calls the identical method from wxDataViewTreeStore.
        """

    def SetItemExpandedIcon(self, item: DataViewItem, icon: wx.BitmapBundle) -> None:
        """
        SetItemExpandedIcon(item, icon) -> None
        
        Calls the identical method from wxDataViewTreeStore.
        """

    def SetItemIcon(self, item: DataViewItem, icon: wx.BitmapBundle) -> None:
        """
        SetItemIcon(item, icon) -> None
        
        Calls the identical method from wxDataViewTreeStore.
        """

    def SetItemText(self, item: DataViewItem, text: str) -> None:
        """
        SetItemText(item, text) -> None
        
        Calls the identical method from wxDataViewTreeStore.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def ImageList(self) -> wx.ImageList: ...
    @ImageList.setter
    def ImageList(self, value: wx.ImageList, /) -> None: ...
    @property
    def Store(self) -> DataViewTreeStore: ...
# end of class DataViewTreeCtrl


class DataViewTreeStore(DataViewModel):
    """
    DataViewTreeStore() -> None
    
    wxDataViewTreeStore is a specialised wxDataViewModel for storing
    simple trees very much like wxTreeCtrl does and it offers a similar
    API.
    """

    def __init__(self) -> None:
        """
        DataViewTreeStore() -> None
        
        wxDataViewTreeStore is a specialised wxDataViewModel for storing
        simple trees very much like wxTreeCtrl does and it offers a similar
        API.
        """

    def AppendContainer(self, parent: DataViewItem, text: str, icon: wx.BitmapBundle=wx.BitmapBundle(), expanded: wx.BitmapBundle=wx.BitmapBundle(), data: Optional[ClientData]=None) -> DataViewItem:
        """
        AppendContainer(parent, text, icon=wx.BitmapBundle(), expanded=wx.BitmapBundle(), data=None) -> DataViewItem
        
        Append a container.
        """

    def AppendItem(self, parent: DataViewItem, text: str, icon: wx.BitmapBundle=wx.BitmapBundle(), data: Optional[ClientData]=None) -> DataViewItem:
        """
        AppendItem(parent, text, icon=wx.BitmapBundle(), data=None) -> DataViewItem
        
        Append an item.
        """

    def DeleteAllItems(self) -> None:
        """
        DeleteAllItems() -> None
        
        Delete all item in the model.
        """

    def DeleteChildren(self, item: DataViewItem) -> None:
        """
        DeleteChildren(item) -> None
        
        Delete all children of the item, but not the item itself.
        """

    def DeleteItem(self, item: DataViewItem) -> None:
        """
        DeleteItem(item) -> None
        
        Delete this item.
        """

    def GetChildCount(self, parent: DataViewItem) -> int:
        """
        GetChildCount(parent) -> int
        
        Return the number of children of item.
        """

    def GetItemData(self, item: DataViewItem) -> ClientData:
        """
        GetItemData(item) -> ClientData
        
        Returns the client data associated with the item.
        """

    def GetItemExpandedIcon(self, item: DataViewItem) -> wx.Icon:
        """
        GetItemExpandedIcon(item) -> wx.Icon
        
        Returns the icon to display in expanded containers.
        """

    def GetItemIcon(self, item: DataViewItem) -> wx.Icon:
        """
        GetItemIcon(item) -> wx.Icon
        
        Returns the icon of the item.
        """

    def GetItemText(self, item: DataViewItem) -> str:
        """
        GetItemText(item) -> str
        
        Returns the text of the item.
        """

    def GetNthChild(self, parent: DataViewItem, pos: int) -> DataViewItem:
        """
        GetNthChild(parent, pos) -> DataViewItem
        
        Returns the nth child item of item.
        """

    def InsertContainer(self, parent: DataViewItem, previous: DataViewItem, text: str, icon: wx.BitmapBundle=wx.BitmapBundle(), expanded: wx.BitmapBundle=wx.BitmapBundle(), data: Optional[ClientData]=None) -> DataViewItem:
        """
        InsertContainer(parent, previous, text, icon=wx.BitmapBundle(), expanded=wx.BitmapBundle(), data=None) -> DataViewItem
        
        Inserts a container after previous.
        """

    def InsertItem(self, parent: DataViewItem, previous: DataViewItem, text: str, icon: wx.BitmapBundle=wx.BitmapBundle(), data: Optional[ClientData]=None) -> DataViewItem:
        """
        InsertItem(parent, previous, text, icon=wx.BitmapBundle(), data=None) -> DataViewItem
        
        Inserts an item after previous.
        """

    def PrependContainer(self, parent: DataViewItem, text: str, icon: wx.BitmapBundle=wx.BitmapBundle(), expanded: wx.BitmapBundle=wx.BitmapBundle(), data: Optional[ClientData]=None) -> DataViewItem:
        """
        PrependContainer(parent, text, icon=wx.BitmapBundle(), expanded=wx.BitmapBundle(), data=None) -> DataViewItem
        
        Inserts a container before the first child item or parent.
        """

    def PrependItem(self, parent: DataViewItem, text: str, icon: wx.BitmapBundle=wx.BitmapBundle(), data: Optional[ClientData]=None) -> DataViewItem:
        """
        PrependItem(parent, text, icon=wx.BitmapBundle(), data=None) -> DataViewItem
        
        Inserts an item before the first child item or parent.
        """

    def SetItemData(self, item: DataViewItem, data: ClientData) -> None:
        """
        SetItemData(item, data) -> None
        
        Sets the client data associated with the item.
        """

    def SetItemExpandedIcon(self, item: DataViewItem, icon: wx.BitmapBundle) -> None:
        """
        SetItemExpandedIcon(item, icon) -> None
        
        Sets the expanded icon for the item.
        """

    def SetItemIcon(self, item: DataViewItem, icon: wx.BitmapBundle) -> None:
        """
        SetItemIcon(item, icon) -> None
        
        Sets the icon for the item.
        """
# end of class DataViewTreeStore


NullDataViewItem = DataViewItem()
class DataViewItemObjectMapper(object):
    """
    This class provides a mechanism for mapping between Python objects and the
    :class:`DataViewItem` objects used by the :class:`DataViewModel` for tracking the items in
    the view. The ID used for the item is the id() of the Python object. Use
    :meth:`ObjectToItem` to create a :class:`DataViewItem` using a Python object as its ID,
    and use :meth:`ItemToObject` to fetch that Python object again later for a given
    :class:`DataViewItem`.
    
    By default a regular dictionary is used to implement the ID to object
    mapping. Optionally a WeakValueDictionary can be useful when there will be
    a high turnover of objects and maintaining an extra reference to the
    objects would be unwise.  If weak references are used then the objects
    associated with data items must be weak-referenceable.  (Things like
    stock lists and dictionaries are not.)  See :meth:`UseWeakRefs`.
    
    This class is used in :class:`PyDataViewModel` as a mixin for convenience.
    """

    def __init__(self):
        pass

    def ObjectToItem(self, obj):
        """
        Create a :class:`DataViewItem` for the object, and remember the ID-->obj mapping.
        """
        pass

    def ItemToObject(self, item):
        """
        Retrieve the object that was used to create an item.
        """
        pass

    def UseWeakRefs(self, flag):
        """
        Switch to or from using a weak value dictionary for keeping the ID to
        object map.
        """
        pass
class PyDataViewModel(DataViewModel, DataViewItemObjectMapper):
    """
    A convenience class that is a :class:`DataViewModel` combined with an object mapper.
    """

    def __init__(self):
        pass

PyDataViewIndexListModel = wx.deprecated(DataViewIndexListModel)
PyDataViewVirtualListModel = wx.deprecated(DataViewVirtualListModel)

PyDataViewCustomRenderer = wx.deprecated(DataViewCustomRenderer,
                                         "Use DataViewCustomRenderer instead")

EVT_DATAVIEW_SELECTION_CHANGED         = wx.PyEventBinder( wxEVT_DATAVIEW_SELECTION_CHANGED, 1)
EVT_DATAVIEW_ITEM_ACTIVATED            = wx.PyEventBinder( wxEVT_DATAVIEW_ITEM_ACTIVATED, 1)
EVT_DATAVIEW_ITEM_COLLAPSED            = wx.PyEventBinder( wxEVT_DATAVIEW_ITEM_COLLAPSED, 1)
EVT_DATAVIEW_ITEM_EXPANDED             = wx.PyEventBinder( wxEVT_DATAVIEW_ITEM_EXPANDED, 1)
EVT_DATAVIEW_ITEM_COLLAPSING           = wx.PyEventBinder( wxEVT_DATAVIEW_ITEM_COLLAPSING, 1)
EVT_DATAVIEW_ITEM_EXPANDING            = wx.PyEventBinder( wxEVT_DATAVIEW_ITEM_EXPANDING, 1)
EVT_DATAVIEW_ITEM_START_EDITING        = wx.PyEventBinder( wxEVT_DATAVIEW_ITEM_START_EDITING, 1)
EVT_DATAVIEW_ITEM_EDITING_STARTED      = wx.PyEventBinder( wxEVT_DATAVIEW_ITEM_EDITING_STARTED, 1)
EVT_DATAVIEW_ITEM_EDITING_DONE         = wx.PyEventBinder( wxEVT_DATAVIEW_ITEM_EDITING_DONE, 1)
EVT_DATAVIEW_ITEM_VALUE_CHANGED        = wx.PyEventBinder( wxEVT_DATAVIEW_ITEM_VALUE_CHANGED, 1)
EVT_DATAVIEW_ITEM_CONTEXT_MENU         = wx.PyEventBinder( wxEVT_DATAVIEW_ITEM_CONTEXT_MENU, 1)
EVT_DATAVIEW_COLUMN_HEADER_CLICK       = wx.PyEventBinder( wxEVT_DATAVIEW_COLUMN_HEADER_CLICK, 1)
EVT_DATAVIEW_COLUMN_HEADER_RIGHT_CLICK = wx.PyEventBinder( wxEVT_DATAVIEW_COLUMN_HEADER_RIGHT_CLICK, 1)
EVT_DATAVIEW_COLUMN_SORTED             = wx.PyEventBinder( wxEVT_DATAVIEW_COLUMN_SORTED, 1)
EVT_DATAVIEW_COLUMN_REORDERED          = wx.PyEventBinder( wxEVT_DATAVIEW_COLUMN_REORDERED, 1)
EVT_DATAVIEW_ITEM_BEGIN_DRAG           = wx.PyEventBinder( wxEVT_DATAVIEW_ITEM_BEGIN_DRAG, 1)
EVT_DATAVIEW_ITEM_DROP_POSSIBLE        = wx.PyEventBinder( wxEVT_DATAVIEW_ITEM_DROP_POSSIBLE, 1)
EVT_DATAVIEW_ITEM_DROP                 = wx.PyEventBinder( wxEVT_DATAVIEW_ITEM_DROP, 1)
EVT_DATAVIEW_CACHE_HINT                = wx.PyEventBinder( wxEVT_DATAVIEW_CACHE_HINT, 1 )

# deprecated wxEVT aliases
wxEVT_COMMAND_DATAVIEW_SELECTION_CHANGED          = wxEVT_DATAVIEW_SELECTION_CHANGED
wxEVT_COMMAND_DATAVIEW_ITEM_ACTIVATED             = wxEVT_DATAVIEW_ITEM_ACTIVATED
wxEVT_COMMAND_DATAVIEW_ITEM_COLLAPSED             = wxEVT_DATAVIEW_ITEM_COLLAPSED
wxEVT_COMMAND_DATAVIEW_ITEM_EXPANDED              = wxEVT_DATAVIEW_ITEM_EXPANDED
wxEVT_COMMAND_DATAVIEW_ITEM_COLLAPSING            = wxEVT_DATAVIEW_ITEM_COLLAPSING
wxEVT_COMMAND_DATAVIEW_ITEM_EXPANDING             = wxEVT_DATAVIEW_ITEM_EXPANDING
wxEVT_COMMAND_DATAVIEW_ITEM_START_EDITING         = wxEVT_DATAVIEW_ITEM_START_EDITING
wxEVT_COMMAND_DATAVIEW_ITEM_EDITING_STARTED       = wxEVT_DATAVIEW_ITEM_EDITING_STARTED
wxEVT_COMMAND_DATAVIEW_ITEM_EDITING_DONE          = wxEVT_DATAVIEW_ITEM_EDITING_DONE
wxEVT_COMMAND_DATAVIEW_ITEM_VALUE_CHANGED         = wxEVT_DATAVIEW_ITEM_VALUE_CHANGED
wxEVT_COMMAND_DATAVIEW_ITEM_CONTEXT_MENU          = wxEVT_DATAVIEW_ITEM_CONTEXT_MENU
wxEVT_COMMAND_DATAVIEW_COLUMN_HEADER_CLICK        = wxEVT_DATAVIEW_COLUMN_HEADER_CLICK
wxEVT_COMMAND_DATAVIEW_COLUMN_HEADER_RIGHT_CLICK  = wxEVT_DATAVIEW_COLUMN_HEADER_RIGHT_CLICK
wxEVT_COMMAND_DATAVIEW_COLUMN_SORTED              = wxEVT_DATAVIEW_COLUMN_SORTED
wxEVT_COMMAND_DATAVIEW_COLUMN_REORDERED           = wxEVT_DATAVIEW_COLUMN_REORDERED
wxEVT_COMMAND_DATAVIEW_CACHE_HINT                 = wxEVT_DATAVIEW_CACHE_HINT
wxEVT_COMMAND_DATAVIEW_ITEM_BEGIN_DRAG            = wxEVT_DATAVIEW_ITEM_BEGIN_DRAG
wxEVT_COMMAND_DATAVIEW_ITEM_DROP_POSSIBLE         = wxEVT_DATAVIEW_ITEM_DROP_POSSIBLE
wxEVT_COMMAND_DATAVIEW_ITEM_DROP                  = wxEVT_DATAVIEW_ITEM_DROP
#-- end-dataview --#
#-- begin-treelist --#

class _enum_52(IntEnum):
    TL_SINGLE = auto()
    TL_MULTIPLE = auto()
    TL_CHECKBOX = auto()
    TL_3STATE = auto()
    TL_USER_3STATE = auto()
    TL_NO_HEADER = auto()
    TL_DEFAULT_STYLE = auto()
    TL_STYLE_MASK = auto()
TL_SINGLE = _enum_52.TL_SINGLE
TL_MULTIPLE = _enum_52.TL_MULTIPLE
TL_CHECKBOX = _enum_52.TL_CHECKBOX
TL_3STATE = _enum_52.TL_3STATE
TL_USER_3STATE = _enum_52.TL_USER_3STATE
TL_NO_HEADER = _enum_52.TL_NO_HEADER
TL_DEFAULT_STYLE = _enum_52.TL_DEFAULT_STYLE
TL_STYLE_MASK = _enum_52.TL_STYLE_MASK
wxEVT_TREELIST_SELECTION_CHANGED: int
wxEVT_TREELIST_ITEM_EXPANDING: int
wxEVT_TREELIST_ITEM_EXPANDED: int
wxEVT_TREELIST_ITEM_CHECKED: int
wxEVT_TREELIST_ITEM_ACTIVATED: int
wxEVT_TREELIST_ITEM_CONTEXT_MENU: int
wxEVT_TREELIST_COLUMN_SORTED: int

class TreeListItem:
    """
    TreeListItem() -> None
    
    Unique identifier of an item in wxTreeListCtrl.
    """

    def __init__(self) -> None:
        """
        TreeListItem() -> None
        
        Unique identifier of an item in wxTreeListCtrl.
        """

    def IsOk(self) -> bool:
        """
        IsOk() -> bool
        
        Return true if the item is valid.
        """

    def __nonzero__(self) -> bool:
        """
        __nonzero__() -> bool
        """

    def __bool__(self) -> bool:
        """
        __bool__() -> bool
        """

    def __hash__(self) -> int:
        """
        __hash__() -> int
        """

    def __eq__(self, other: TreeListItem) -> bool:
        """
        __eq__(other) -> bool
        """

    def __ne__(self, other: TreeListItem) -> bool:
        """
        __ne__(other) -> bool
        """
# end of class TreeListItem


class TreeListItemComparator:
    """
    TreeListItemComparator() -> None
    
    Class defining sort order for the items in wxTreeListCtrl.
    """

    def __init__(self) -> None:
        """
        TreeListItemComparator() -> None
        
        Class defining sort order for the items in wxTreeListCtrl.
        """

    def Compare(self, treelist: TreeListCtrl, column: int, first: TreeListItem, second: TreeListItem) -> int:
        """
        Compare(treelist, column, first, second) -> int
        
        Pure virtual function which must be overridden to define sort order.
        """
# end of class TreeListItemComparator

TreeListCtrlNameStr: str

class TreeListCtrl(wx.Window):
    """
    TreeListCtrl() -> None
    TreeListCtrl(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=TL_DEFAULT_STYLE, name=TreeListCtrlNameStr) -> None
    
    A control combining wxTreeCtrl and wxListCtrl features.
    """

    @overload
    def __init__(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=TL_DEFAULT_STYLE, name: str=TreeListCtrlNameStr) -> None:
        ...

    @overload
    def __init__(self) -> None:
        """
        TreeListCtrl() -> None
        TreeListCtrl(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=TL_DEFAULT_STYLE, name=TreeListCtrlNameStr) -> None
        
        A control combining wxTreeCtrl and wxListCtrl features.
        """
    NO_IMAGE: int

    def AssignImageList(self, imageList: wx.ImageList) -> None:
        """
        AssignImageList(imageList) -> None
        
        Sets the image list and gives its ownership to the control.
        """

    def SetImageList(self, imageList: wx.ImageList) -> None:
        """
        SetImageList(imageList) -> None
        
        Sets the image list.
        """

    def AppendColumn(self, title: str, width: int=wx.COL_WIDTH_AUTOSIZE, align: wx.Alignment=wx.ALIGN_LEFT, flags: int=wx.COL_RESIZABLE) -> int:
        """
        AppendColumn(title, width=wx.COL_WIDTH_AUTOSIZE, align=wx.ALIGN_LEFT, flags=wx.COL_RESIZABLE) -> int
        
        Add a column with the given title and attributes.
        """

    def GetColumnCount(self) -> int:
        """
        GetColumnCount() -> int
        
        Return the total number of columns.
        """

    def DeleteColumn(self, col: int) -> bool:
        """
        DeleteColumn(col) -> bool
        
        Delete the column with the given index.
        """

    def ClearColumns(self) -> None:
        """
        ClearColumns() -> None
        
        Delete all columns.
        """

    def SetColumnWidth(self, col: int, width: int) -> None:
        """
        SetColumnWidth(col, width) -> None
        
        Change the width of the given column.
        """

    def GetColumnWidth(self, col: int) -> int:
        """
        GetColumnWidth(col) -> int
        
        Get the current width of the given column in pixels.
        """

    def WidthFor(self, text: str) -> int:
        """
        WidthFor(text) -> int
        
        Get the width appropriate for showing the given text.
        """

    def AppendItem(self, parent: TreeListItem, text: str, imageClosed: int=-1, imageOpened: int=-1, data: Optional[ClientData]=None) -> TreeListItem:
        """
        AppendItem(parent, text, imageClosed=-1, imageOpened=-1, data=None) -> TreeListItem
        
        Same as InsertItem() with wxTLI_LAST.
        """

    def InsertItem(self, parent: TreeListItem, previous: TreeListItem, text: str, imageClosed: int=-1, imageOpened: int=-1, data: Optional[ClientData]=None) -> TreeListItem:
        """
        InsertItem(parent, previous, text, imageClosed=-1, imageOpened=-1, data=None) -> TreeListItem
        
        Insert a new item into the tree.
        """

    def PrependItem(self, parent: TreeListItem, text: str, imageClosed: int=-1, imageOpened: int=-1, data: Optional[ClientData]=None) -> TreeListItem:
        """
        PrependItem(parent, text, imageClosed=-1, imageOpened=-1, data=None) -> TreeListItem
        
        Same as InsertItem() with wxTLI_FIRST.
        """

    def DeleteItem(self, item: TreeListItem) -> None:
        """
        DeleteItem(item) -> None
        
        Delete the specified item.
        """

    def DeleteAllItems(self) -> None:
        """
        DeleteAllItems() -> None
        
        Delete all tree items.
        """

    def GetRootItem(self) -> TreeListItem:
        """
        GetRootItem() -> TreeListItem
        
        Return the (never shown) root item.
        """

    def GetItemParent(self, item: TreeListItem) -> TreeListItem:
        """
        GetItemParent(item) -> TreeListItem
        
        Return the parent of the given item.
        """

    def GetFirstChild(self, item: TreeListItem) -> TreeListItem:
        """
        GetFirstChild(item) -> TreeListItem
        
        Return the first child of the given item.
        """

    def GetNextSibling(self, item: TreeListItem) -> TreeListItem:
        """
        GetNextSibling(item) -> TreeListItem
        
        Return the next sibling of the given item.
        """

    def GetFirstItem(self) -> TreeListItem:
        """
        GetFirstItem() -> TreeListItem
        
        Return the first item in the tree.
        """

    def GetNextItem(self, item: TreeListItem) -> TreeListItem:
        """
        GetNextItem(item) -> TreeListItem
        
        Get item after the given one in the depth-first tree-traversal order.
        """

    def GetItemText(self, item: TreeListItem, col: int=0) -> str:
        """
        GetItemText(item, col=0) -> str
        
        Return the text of the given item.
        """

    @overload
    def SetItemText(self, item: TreeListItem, text: str) -> None:
        ...

    @overload
    def SetItemText(self, item: TreeListItem, col: int, text: str) -> None:
        """
        SetItemText(item, col, text) -> None
        SetItemText(item, text) -> None
        
        Set the text of the specified column of the given item.
        """

    def SetItemImage(self, item: TreeListItem, closed: int, opened: int=-1) -> None:
        """
        SetItemImage(item, closed, opened=-1) -> None
        
        Set the images for the given item.
        """

    def GetItemData(self, item: TreeListItem) -> ClientData:
        """
        GetItemData(item) -> ClientData
        
        Get the data associated with the given item.
        """

    def SetItemData(self, item: TreeListItem, data: ClientData) -> None:
        """
        SetItemData(item, data) -> None
        
        Set the data associated with the given item.
        """

    def Expand(self, item: TreeListItem) -> None:
        """
        Expand(item) -> None
        
        Expand the given tree branch.
        """

    def Collapse(self, item: TreeListItem) -> None:
        """
        Collapse(item) -> None
        
        Collapse the given tree branch.
        """

    def IsExpanded(self, item: TreeListItem) -> bool:
        """
        IsExpanded(item) -> bool
        
        Return whether the given item is expanded.
        """

    def GetSelection(self) -> TreeListItem:
        """
        GetSelection() -> TreeListItem
        
        Return the currently selected item.
        """

    def GetSelections(self) -> Any:
        """
        GetSelections() -> Any
        
        Returns a list of all selected items. This method can be used in
        both single and multi-selection case.
        """

    def Select(self, item: TreeListItem) -> None:
        """
        Select(item) -> None
        
        Select the given item.
        """

    def Unselect(self, item: TreeListItem) -> None:
        """
        Unselect(item) -> None
        
        Deselect the given item.
        """

    def IsSelected(self, item: TreeListItem) -> bool:
        """
        IsSelected(item) -> bool
        
        Return true if the item is selected.
        """

    def SelectAll(self) -> None:
        """
        SelectAll() -> None
        
        Select all the control items.
        """

    def UnselectAll(self) -> None:
        """
        UnselectAll() -> None
        
        Deselect all the control items.
        """

    def EnsureVisible(self, item: TreeListItem) -> None:
        """
        EnsureVisible(item) -> None
        
        Call this to ensure that the given item is visible.
        """

    def CheckItem(self, item: TreeListItem, state: wx.CheckBoxState=wx.CHK_CHECKED) -> None:
        """
        CheckItem(item, state=wx.CHK_CHECKED) -> None
        
        Change the item checked state.
        """

    def CheckItemRecursively(self, item: TreeListItem, state: wx.CheckBoxState=wx.CHK_CHECKED) -> None:
        """
        CheckItemRecursively(item, state=wx.CHK_CHECKED) -> None
        
        Change the checked state of the given item and all its children.
        """

    def UncheckItem(self, item: TreeListItem) -> None:
        """
        UncheckItem(item) -> None
        
        Uncheck the given item.
        """

    def UpdateItemParentStateRecursively(self, item: TreeListItem) -> None:
        """
        UpdateItemParentStateRecursively(item) -> None
        
        Update the state of the parent item to reflect the checked state of
        its children.
        """

    def GetCheckedState(self, item: TreeListItem) -> wx.CheckBoxState:
        """
        GetCheckedState(item) -> wx.CheckBoxState
        
        Return the checked state of the item.
        """

    def AreAllChildrenInState(self, item: TreeListItem, state: wx.CheckBoxState) -> bool:
        """
        AreAllChildrenInState(item, state) -> bool
        
        Return true if all children of the given item are in the specified
        state.
        """

    def SetSortColumn(self, col: int, ascendingOrder: bool=True) -> None:
        """
        SetSortColumn(col, ascendingOrder=True) -> None
        
        Set the column to use for sorting and the order in which to sort.
        """

    def GetSortColumn(self) -> Tuple[bool, int, bool]:
        """
        GetSortColumn() -> Tuple[bool, int, bool]
        
        Return the column currently used for sorting, if any.
        """

    def SetItemComparator(self, comparator: TreeListItemComparator) -> None:
        """
        SetItemComparator(comparator) -> None
        
        Set the object to use for comparing the items.
        """

    def GetView(self) -> wx.Window:
        """
        GetView() -> wx.Window
        
        Return the view part of this control as a wxWindow.
        """

    def GetDataView(self) -> DataViewCtrl:
        """
        GetDataView() -> DataViewCtrl
        
        Return the view part of this control as wxDataViewCtrl.
        """

    def Create(self, parent: wx.Window, id: int=wx.ID_ANY, pos: wx.Point=wx.DefaultPosition, size: wx.Size=wx.DefaultSize, style: int=TL_DEFAULT_STYLE, name: str=TreeListCtrlNameStr) -> bool:
        """
        Create(parent, id=wx.ID_ANY, pos=wx.DefaultPosition, size=wx.DefaultSize, style=TL_DEFAULT_STYLE, name=TreeListCtrlNameStr) -> bool
        
        Create the control window.
        """

    @staticmethod
    def GetClassDefaultAttributes(variant: wx.WindowVariant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes:
        """
        GetClassDefaultAttributes(variant=wx.WINDOW_VARIANT_NORMAL) -> wx.VisualAttributes
        """
    @property
    def ColumnCount(self) -> int: ...
    @property
    def DataView(self) -> DataViewCtrl: ...
    @property
    def FirstItem(self) -> TreeListItem: ...
    @property
    def RootItem(self) -> TreeListItem: ...
    @property
    def Selection(self) -> TreeListItem: ...
    @property
    def Selections(self) -> Any: ...
    @property
    def SortColumn(self) -> int: ...
    @SortColumn.setter
    def SortColumn(self, value: int, /) -> None: ...
    @property
    def View(self) -> wx.Window: ...
# end of class TreeListCtrl


class TreeListEvent(wx.NotifyEvent):
    """
    TreeListEvent() -> None
    
    Event generated by wxTreeListCtrl.
    """

    def __init__(self) -> None:
        """
        TreeListEvent() -> None
        
        Event generated by wxTreeListCtrl.
        """

    def GetItem(self) -> TreeListItem:
        """
        GetItem() -> TreeListItem
        
        Return the item affected by the event.
        """

    def GetOldCheckedState(self) -> wx.CheckBoxState:
        """
        GetOldCheckedState() -> wx.CheckBoxState
        
        Return the previous state of the item checkbox.
        """

    def GetColumn(self) -> int:
        """
        GetColumn() -> int
        
        Return the column affected by the event.
        """
    @property
    def Column(self) -> int: ...
    @property
    def Item(self) -> TreeListItem: ...
    @property
    def OldCheckedState(self) -> wx.CheckBoxState: ...
# end of class TreeListEvent

TLI_FIRST: TreeListItem
TLI_LAST: TreeListItem

EVT_TREELIST_SELECTION_CHANGED = wx.PyEventBinder( wxEVT_TREELIST_SELECTION_CHANGED )
EVT_TREELIST_ITEM_EXPANDING =    wx.PyEventBinder( wxEVT_TREELIST_ITEM_EXPANDING )
EVT_TREELIST_ITEM_EXPANDED =     wx.PyEventBinder( wxEVT_TREELIST_ITEM_EXPANDED )
EVT_TREELIST_ITEM_CHECKED =      wx.PyEventBinder( wxEVT_TREELIST_ITEM_CHECKED )
EVT_TREELIST_ITEM_ACTIVATED =    wx.PyEventBinder( wxEVT_TREELIST_ITEM_ACTIVATED )
EVT_TREELIST_ITEM_CONTEXT_MENU = wx.PyEventBinder( wxEVT_TREELIST_ITEM_CONTEXT_MENU )
EVT_TREELIST_COLUMN_SORTED =     wx.PyEventBinder( wxEVT_TREELIST_COLUMN_SORTED )

# deprecated wxEVT aliases
wxEVT_COMMAND_TREELIST_SELECTION_CHANGED  = wxEVT_TREELIST_SELECTION_CHANGED
wxEVT_COMMAND_TREELIST_ITEM_EXPANDING     = wxEVT_TREELIST_ITEM_EXPANDING
wxEVT_COMMAND_TREELIST_ITEM_EXPANDED      = wxEVT_TREELIST_ITEM_EXPANDED
wxEVT_COMMAND_TREELIST_ITEM_CHECKED       = wxEVT_TREELIST_ITEM_CHECKED
wxEVT_COMMAND_TREELIST_ITEM_ACTIVATED     = wxEVT_TREELIST_ITEM_ACTIVATED
wxEVT_COMMAND_TREELIST_ITEM_CONTEXT_MENU  = wxEVT_TREELIST_ITEM_CONTEXT_MENU
wxEVT_COMMAND_TREELIST_COLUMN_SORTED      = wxEVT_TREELIST_COLUMN_SORTED
#-- end-treelist --#
