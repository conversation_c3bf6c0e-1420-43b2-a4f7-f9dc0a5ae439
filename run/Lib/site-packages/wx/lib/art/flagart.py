#Boa:PyImgResource:

#----------------------------------------------------------------------
# This file was generated by famfamfam_flags.py
#

"""
See: http://www.famfamfam.com/lab/icons/flags

FamFamFam Flag icons are available free for any purpose
Copyright Mark James (<EMAIL>)

This module is (c) 2006 <PERSON><PERSON><PERSON> and licensed under the Python license.

"""

from wx.lib.embeddedimage import PyEmbeddedImage

catalog = {}
index = []

index.append('AE')
catalog['AE'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAAA3UlE"
    "QVQokYWOsU0DUBBD3ye/pKOKBF2YIiUd89BFTMEUabNJNiASTIBozr47igQBRcjTybrClj2a"
    "HwoWz2AwFAQY4s8zATYboKtG1dNjujLLbrvsktIuqazU7mU3Aeh+eyeTzNcPZaZaSqmkVGRE"
    "hVJ313enhq4+ulvaPmz7DGOMw/1hFoxM7LaRz7m7G6iqCeBs6XJgDNsT6HTbrfg/MLqBWTCk"
    "jsDuiH/2dHdVzYKF3RJSS+v1OiIknVRShC07V6vVfr+fBVcyy2VLOG+tI7aPaju/AcYn1K+7"
    "4QJfesZpBoG4DtcAAAAASUVORK5CYII=")

index.append('AF')
catalog['AF'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABbUlE"
    "QVQokVWQvU5VYRRE5+ABAX8KCQIlFeEFKKkt7WngNSzvG1hb8gR0xqixMrHSwoiJMcZOIMHk"
    "JtyzZ2Z/37YgJroy7SpmDfv7+/P5HAAASb+urvrycrcBJADg2SaeHwMBCADG6+vr2WwGoPXW"
    "W4c9ZLtjlb1id+npbnnvRk1sPH1xOo7jCODi8jLtzCy7pKJKLLKTv+/Wt+05HdvrOxDG3ntr"
    "LW1nWiq7gH5yXC3rzbv68N6uyZNMJRFYkpSZskVKKrKOjvT2tc7O+uFhD5qkOWWwCQuMsVhk"
    "piXJZJTU11bz4ABTDKsrFZHRp5zCZBKB8SbCtiSSJLvUfnxf2trp9+7n+ddhsbARGUoxCWFE"
    "le2IuHV6RL18FY83y7ny+RxkqmhGhrshjLf5JUUEyYqoL+drHz91skc00kS0SS3d/gokH21s"
    "3J4GBXGQBnKwl6SHW9h9sOF09sQCA/7nJ9CB/GdPgIs9IIAE1vEH+0VpUzSLjT4AAAAASUVO"
    "RK5CYII=")

index.append('AL')
catalog['AL'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABgElE"
    "QVQokU3RP49NYRSF8XXe83IdF5mMiZhoFBKJRKX0MZCoVNOIZgqKaZSiEaLRjIbaF9AqNDq+"
    "gj8Tk+u6M/fOefdae2/FkGif5il+3ecBNYACAFUQUYEROG4GCBAQQAAjUCuw+eI5gHSPiOq+"
    "+2t5eyjF/fX+4f1p71InCzLIL6/eVAAAtPe9D387W90ZyvXl+Hi2lNndobP9ePZzsT0tMjt5"
    "cdOAGoF0T/eUbvS582Mxtpa009LLg0ZrD4Y+jGEWooBSDOmeZIqfDo8uhYxcNvt6NLaxbbg+"
    "LJbRLM3CWICCRLpCTOoKHOTDaddaa63tnC1rYVeTYc1bSxJADSAlUO681vnlSb6brSpN1t7P"
    "Y2vSTZRhTBpIA4oBoJKm1gbZ073ftyY5H9t8bPdOdY++zQdZ0mAWrtXxIZxBCzOYPTnX9+LN"
    "Pg9KnBF310+4WZLpCglABZBUv3GhkEGCFtT22vkkwxlSkUCPYLoC6D4CAOI/UQH2z/WvEmAA"
    "gBH4A2X6QkAkcTlLAAAAAElFTkSuQmCC")

index.append('AM')
catalog['AM'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABE0lE"
    "QVQokVVPvU6bQRCc+3yV3VKgiJY3oMzTIapILtMktDwaEgqS9WEJEm7nJ8XZkKxWoxntzGq3"
    "1fU11hWzbI9hEmNAMjAbZ2Kg43BY7u4AxEaykTY2yEiZKIEMCem433csC5D8eoYUGT6NQ6bq"
    "FKtCVbu8JNCxrnEwN0mYjukmwwqZIlipMtAvtj9uefPEN8tkSJOq8r+kylX6UrufeO9j0LZo"
    "0qLrP58os1ylKYE/fYzPKz5uqQrpqkhz/fwfANrv7+hfv+X1MWHMqD7RZVUsuGIuu6un+4fu"
    "BTATxhV9ZCouT2nCjOkYHd1HZAibC4AAWyPaydEWtQhSLIN4p1e0lz3wBg+YZyR4JpiSgGEC"
    "W/wFGOaHzQZ1JyAAAAAASUVORK5CYII=")

index.append('AR')
catalog['AR'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABOUlE"
    "QVQokU2RvU5VURCF1z5sDyihwBsMjYWVEUsTShqUxEfwAWxIKK2peR5ibLTz5w1oTLAmuZFo"
    "LnKy78y3LM6B3NXMl8maZGZNeffDi9AoREOEQmpBwBA0aCMEfaf6N/T+2ei2XbCQwOmSrKW6"
    "xOC00z75vqhDaImul8bCSjsRduDAYScTz9aLGvX06fzFk9kSWZKNZRls27LxiNgPinZezuvz"
    "LWa9JNmWZdm3X3JxjqPbfOuNI9+pFO3P1I3nTr2xDp/hl/PS/z55VRJQV90aJ/oDDXOpeeNw"
    "1V9soF78YW/LLW3rbt03fvQaG8zN5Ab11d/mWY9/Pv6wpqtbB7oPJO9TYkop0e5DnX3drh0U"
    "dTvrU6BpJUpKWoGwQOmS2JJEefXx5nebHjkEQyhBgRoCjRxIKKRe/wH2AXDnJMhVrAAAAABJ"
    "RU5ErkJggg==")

index.append('AT')
catalog['AT'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAAA6UlE"
    "QVQokX2RrW5CQRSEv21vatA3NwgcpjUoHgyN54EwvAGeF0C1glBca/b8TMXeFGhIJydfRkw2"
    "Z+cUcVXe+4dTBKzXgDLJJEIehMsdd5nphl/bbQeA9P5BBBEyaxynmqyqVpmV2SyhY7nU6xv9"
    "gFLuRChC7nK/Gvda68sw5G5XTqdT3/eAJITQH9VaGyeTyeVyaSuNqV/zWCCpu4Yevn+blzKz"
    "2GJRViudz9xsLA/F3TeIKMPwudl0eTg87/c6HhlrGTsZ2YwbHsznQJfwZM50KjM8cMOsmOFO"
    "o3trnIiE8n1/yH9u3PQDfNZaTYpscjsAAAAASUVORK5CYII=")

index.append('AU')
catalog['AU'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAACC0lE"
    "QVQokU3HT0hTcRwA8O/b3mhLndO5tmnPtuUi56YdYguzwrKB2SIiKOiUFR2joGSHLkXBQBE6"
    "ZlHQJbBGBDGFKK1cRA0TTALdFhk129JtbHu/9/vbVfhcPtLHkdszxo7o1PpYMS5zfoWHr7f+"
    "PLy3ffB+AapFAApABy8MJB6+BFgHwNJQZOxRtI/YFTSfEkiVdngsNsvrNPqy+JtRrmJyLhKQ"
    "ZSmzupGYXXkyOi6lTCbnnbv6wl9SLnNNM9TXaVyqqThmPFCkEkZYcTaG97tjE3MupWny3rju"
    "nf+4rqvH6OtkVhsorq3BIFHc6e3diIoT/R0A8D2bj03MRS/3Oax1ADVd8fQwWK0vuOtPvT1n"
    "sMSbg3p/oGRuOXLQq9gbrg7vUxE5e8xnkKWjvW4ApPdLrfkGx0wWkRrKVnVTS6Xlit7C1efJ"
    "X15Py2Ti21q+UthQmxqNT18trnx9r/+c6QwMdFeLlQwxZfAWmeJcvjKfI0yIZGp17V9FCHFm"
    "qOtxfMFkNGQW3ugAKFBOKD91yHPzYggTRhkHzgimnHPBhKbR0QfJUrnGGQOgOgCMKRNChPa0"
    "tTvNJ8O7NUwwoZhQTJiKSZvDPHKpVyOMMQFAZQDMOW9uNE1/SPfsss1++qE4zJrGmOCUChXh"
    "a+dDvp3bNMyeTS8BIAkgAkABEADehG6ut//G8ttbABwA/gMK+Buk8wRxpgAAAABJRU5ErkJg"
    "gg==")

index.append('AZ')
catalog['AZ'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABbklE"
    "QVQokU2QvWpUYRiE5zs5uyFrwEYkP0SzaqugtW1uIhewd2AVvIM0ptvgJWjjDSguqNgJWbtV"
    "RBFt1MRE93zvzLwp1oDDw/DAdFPw6DXmxCIGaJggQIL+19UgUYm2aXHKvZ0hACecqUwasplJ"
    "m0bItMMZymfjVy0GAyy1X85ChjLDljKkSIQznFWudshbl/ror5ZJKTcODuq372lBMpmkSZMZ"
    "YdIRjkiyv7b2cjxu0TQgoUgJQc3/Xt3d7W1sHj9/0bu+dTyZ/JlOM2hGRm2AxpITlpMSuTy8"
    "2dseft7fH9y7u3xte+X2HVEiRdlJoAFQFg9UllB39L6bfVwfjX4+eTqfzc7evC0dS2XpiEoD"
    "bQUkSpGiGW7y6+PDrGHx1/TIEV5M5pJ5CrQ7DzG6rw+/FWJYVayqUaNCYVYzTIq0bl32u09o"
    "cYIIrvevhEiTYpjRI82wKDEpSSl1wg8UPABOAAL1Av4nvBADBFZxDq7cZQ8Pjp14AAAAAElF"
    "TkSuQmCC")

index.append('BE')
catalog['BE'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAAA7klE"
    "QVQokW2RQUqDYQxE36dVRFTEjXThxgt4IW/SW/U61V0FV4riKjNJXPT/WxWHIau8zEAGv/Xx"
    "zkUAVFHX1Cd1S3HwAlitVkBVVdXZeXKV4NE+xkeX6ke31HZLX+v1AoDebl8yMzOrBNmtyane"
    "REe0NO7upoSqzkxnWurO7uh2t7qjOzq0A5AmIDO9k3y4/R/ADrBT0g8gDkCpY6qEvU+w7VD8"
    "BuY5J4x9JUkRYVsRfxNa+/Ntb+dK1qyp9377RB3RFk4yPSd4uVxKsnMMgcYQGITMvcncuWD8"
    "+fTmmVjgoszNGfXG6wOA4RSe4BuvWmXvCEAfcgAAAABJRU5ErkJggg==")

index.append('BG')
catalog['BG'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABB0lE"
    "QVQokW1OMU5DUQzz+7wuldhYKhV1YeEenKkjCzfgMsyVmDgCqhg6dWEBFYnYcRh+Cx+EEzm2"
    "nEhph8MBE9geeSq+IbsDmM/nAKpqvKka3WlMsN/v+2mppqL+Q2vNdt++bZfDMit/EpTLo/Sp"
    "XJ4N/fnjueEO65v17n0nSxZNmkxGkhmRQTMUNFfnq839pgNQJYsqqUQzHDRZjIoo0hEVNFWC"
    "0TEgB2lQVKiJjUREYyAIBoIDo4JFDkRHxyNW15y9hlJSKMkMiUpSIZEZMiUtL/S0Qf98wNml"
    "/EKQRVYcHznyKEQo21XeAt3AQGGxKBJKiCAbCQkjS8gc20A7AJ40fts/EYAvPNBlhsfxqY8A"
    "AAAASUVORK5CYII=")

index.append('BH')
catalog['BH'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABIklE"
    "QVQokV2RPW7CQBCFn5FbFzSxlIgbQJPchpvQkZQhSKRBSorkDjTcgaSDC9BQ2AqJTBDOzuy8"
    "FOsfwdNoNSt92jezLyqKArWS5dKGQ3gfrgYYoHUTKiqKIkmSQNA88hxv78xyeKUqVO10oghV"
    "KfK7WMQVSgLAeoPXF37t4RxFQtnxSOcoEvV6BnRaGsBgwPsHpGkg6ITOtSViQGxmzQ5crzF5"
    "4s939bwTSktDBMEhmJBkv8/nGW+ua8idOagaEDc0AH5+4HHC/Z7iqnlEGodIxICOmbEWbu84"
    "mzG94p+7oKFKVW12qPZerTidcrdr6XCqQH3IJ8qyrNvtViN5j+2W4zEPB9R/b2VJ7+E9vM83"
    "m5hkWZbBx+ZzG41wHm2TdNA/d+tUbaz3axgAAAAASUVORK5CYII=")

index.append('BI')
catalog['BI'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAAB/0lE"
    "QVQokTXJTUiTcRwH8O/zbMl8HvfC1tzKaI4sGy1q7JCCdIhePEanLqVEVqNL0SnommKHFkR0"
    "6VSSQW8g2EkQKTIhiqSpGLY2UsKtPXtrm//n9///OkS3D3w0zmQgjJW+HrUlnaGQIlJEEEK1"
    "WsSsAAW0AADx6XnlrGoHJqNfz34H0FhYqM/MOIJBSEm1GojgcrGUdj5vnho0Txxn5vbRdoc+"
    "UNHchr8jHO6Om8n+8qvnVCwGzg274gerb6ZFLhceu7sttjfTWr318ebi9KLT7/F52nwPvqQT"
    "ncnh6FB4fJyZi6W8sCmUTjMzM99bSU9lp/pC/fBBJ6UAhDp2LBUzqfkrZW4ysypYWvE3M9dZ"
    "nJk9/Xb9XbcvyhqjBb2hWpKlYmm4TEHiW2WZayLQEw/2JrjaXKutVpo1r+m1JZEiKOi/XKRY"
    "ElP5j9VUzT3uQz9ePCpll6yN7Nrj+1EzJp2iUC6QsqWS6IAulbQl5axcl7vrycCEh3n3+cu+"
    "yH5vOBJN3TCYZ47N9vr3rRSXbRJQcKKEjfr6SDx1xJtkxT8vjUDT/NevMWuF0dvCKkWevR49"
    "PDYXeZ/+fAeb0PAQ9kWbmRvzH6ynE47AdkhpVyrKtnXDYKKtfM4/dMEcPAnAc9WjzWXndpU7"
    "PyViVaANIEABBAhA/fe/Ojr5cmnn5l99aCZ0THLLKQAAAABJRU5ErkJggg==")

index.append('BLANK')
catalog['BLANK'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAYAAAB24g05AAAABHNCSVQICAgIfAhkiAAAABlJ"
    "REFUKJFjZGRiZqAEMFGke9SAUQOoZQAAMjgAHL4jfPwAAAAASUVORK5CYII=")

index.append('BN')
catalog['BN'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABw0lE"
    "QVQokS3MMWhTYRTF8fN976U+kxgNdojBoZAIDopUyZIOoougm4ug4CIdRASJFNRdaCdx6OAo"
    "KMFZcLKbi2jBKKJRaRMpCVbb5L00eS/fvbnXIT3Tbzl/M2wCDrCYTgSQfQgDApF9s0AERlUB"
    "TAYvwH9FepCJqsAwDAszwBBWJVFW5d2vr/xp2GZvALCATLYlfDre23ZRdDDvhAiGREnFmdRx"
    "YXjpIKguLExvqmpMxgQXNnbO0juXLbVgVGQkTCLO2kPD7g/v7dpaHMf1en22WHTM+VxuEsfu"
    "+RMNd+n0s5nCNT9702YuarIOg367aQDUavfb7RYzM/FK9aTn+dHl6+Wj+ejlqrhxofZYVVXV"
    "WBv2+2bwDebIg18/Nx8uR0xM5K+eL9158z7taOXSmd7V26lM5tTcnKpaa3u9ngm/ICguCbVM"
    "KhkPqPtbw3+Y/VRCsrf0+vv66I9zjojOzc/fWlysVCombODAsXtCLUgCjxQuCYViVuXOJq7c"
    "7ZBzzMQ8KZfLjUbDF4YqG68gmiizgoPDZGcmEDpRpebH3DiSrQ5vdfnR8hCA2fkAYYiA3T6E"
    "YX04howAgU2DA0AgjI3P+A9mXiz0vUkDDQAAAABJRU5ErkJggg==")

index.append('BO')
catalog['BO'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABHElE"
    "QVQokW1RPaoUYRCs2f008pmpCwaC4DG8h7G5eAkRQzHzDiYewnsYi4Esbx/jV38GMysGNkVT"
    "1d10QffSR0dcIzImEGBFiuA/GADw4ROApkuMpA7sJT7aB7Fyrco1b99/HABQ9OcP2EgqYW+7"
    "YqWKJSsuj08Bxt0r338x82RF1Lh2w0awatasFbPm4eHd+RnG7WvcPF3NSyvUjVo2TP8SNmzm"
    "4d7Nr3cYCBq3aoW4ZcLuQwpmw4aJDlGCEaFgK4StcnXYd28mYaPCEsYX4aV50e+EKRN6I6Wz"
    "y0auHhzXzyvGmzPe8vJ9nhkqYjgzWe5kK5aKn+Py7YwBQdFpnFQxcjQxFW3YTuvYtWEIC74C"
    "AfQPNjmvv92K2PMfrSN8t7ZlHI0AAAAASUVORK5CYII=")

index.append('BR')
catalog['BR'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABrUlE"
    "QVQokU2RO2iTYRSG3/75Qq0oIYMS24JDFcFbBkcVlAoi4uAloMRJUSGTQ10KCkWx4KK03kAc"
    "xMvQCirWwUXNpi62OAgOUhW8ECxNm1/id95zjkOU5OFweOHlcIanC8fRxgD+3xGIHaE1TQQA"
    "I+URB9zNzNRVTbet/x3AqXcJjWpKihhFZeLsRADg8G8L37XVGe8cTN173GWw8Gvv3UykUCVq"
    "7M/1//vg7mqkcc+mtNSrw8Nj01YQ+kDzx9OLhx58levVDFWEgogEBho1SW/tT0sDnP5QOlIp"
    "3Bz6UynVZ7pXnrkwdHhNrB6rR4qYoIkERG+ueXufJC4Oufx88PQNv/oky7jk/AnPbyy6R/f4"
    "5tR8sa+BFAkiZueSk8/oiO5xS/6t0Gc++ehkmK95cfnr1sGue/HVe0EOCQiScwvJgfth6nOs"
    "HB1du5jWfrosevVRY/f2c49n44ax7JdaNrs0izoCIugUFVEZf5m51JO+uLLDpc9FvPvj5vEg"
    "FqhCJY0AAiKELCxbRRUxIVeUJ6W8tZEJdu3hutV5qilVaWJqALqws22x02jbtKKTv/JDL0E5"
    "0S0eAAAAAElFTkSuQmCC")

index.append('BT')
catalog['BT'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABnklE"
    "QVQokW3Kv2/McRzH8ef3e5+79tL2gtzFj5RGJcLAH+APYLIYDQYJ6R8gFpMwMHWiDFi7IGmE"
    "ySAGgxCLgaQGOYk0vetdr+e+/Xzen/fLIGLxyHN8FpO3BMP5y3EHIzTwEq/wiIM38IA/IAQj"
    "HL8BCEeOZ5HB1FhUtBA/q7BMKqLpVdp5uhY8AFL8gTKepQRZzRO+b6mqFKtx2bs26sbO6pfa"
    "scNepwSE/t2eFaK2Pw4Go5hy3WPRXt5a+1nsiZpKnihx8IyblJCpjL9G5cbkkA9fD8fx/fOV"
    "75tbncu3tBOVExBwUJaSZBSmKnVnVomTWl2hplPnr0I59+iSmolkDsENyeSmIlKz3t7rPqn2"
    "Nwa5Sv1Jcy5t2MubGkTNpCInh9IdeVItTmYvrIe7/c12M2+nXrd17+LCh2fjufn+2TsaJcyU"
    "zaD0CDXT9Mn+8PT0t+3Fh1eK6ebwzWOxq08r7dtnjtw/p3aUJXkGQjmLStO73sH1Zaayjs63"
    "vr6oN2JxYIEy0TLcUCZnPDsUoyewhO/iHTyC4WOc//THb4UhI+oZPvWXAAAAAElFTkSuQmCC")

index.append('BW')
catalog['BW'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABDklE"
    "QVQokX1RsUqjYRCcjT+KYAicoJ2BA/EN8wRi6YvcA9jYpFMfIU0gWB0XuIBC2J1vxuIzIXeI"
    "U8wu7O6wzMT1s/8SHZRSoJCCJUigQGHfA8OfxN1PGBBsRxME0JaCPmoeNZtyyc2+f3obRoCM"
    "3+VmSKDdDMq0O5dcMuXLkwAVm81mPB73l2x3/hIRsVwuA8BsNlutViRZLFZHZlVl7kpVTafT"
    "+Xw+ACBbVe0P+vif9cy+AGAA0BpJZuVOPzPrUHsvASDW6/VkMvn++z6NiMViMZy/nN3e4HXr"
    "Eg496Uy5ul3C1Sl+Pf4YQMmji+NuJZpBoRS9aUIzmqPJAkAFHt6xPQiV+Iw2/8tYIHCMD2d1"
    "fWGuzZK5AAAAAElFTkSuQmCC")

index.append('BY')
catalog['BY'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABTElE"
    "QVQokU2OsWpUYRCFv717sd1qCVuk8R3yEhbiC4RgqjRhGyux09ikixY+RTBBEFJY2vgQESxE"
    "FpG9d6/unTPzj8W9yWY4HA7MfMMhuy6PjvL2NiEeSNDDP+ighTX8gd8wSaDvOTtLiVKISA/C"
    "0x33lPKBb66va9Zrbm5yuWS5JCKlwUeZUpZmKU329wvUzGa52XB6urs2S/fxVJamAUAqUJXV"
    "iqsrzs9TQj7s0mxUb7ssATXzOV2Xx8cj4OO/EbuHJdwLVKxWeXmZF+/uCtx53+/cLM2GSnV5"
    "8iy/fO5Onh+euIpZSGEKWZGKKWRhHlLxxzP/+paqfPpY3rzefnjfTq2dqp1aM6pvKltX1kyt"
    "qa2t1VXBI+qyN2fTVK9eLJ4uvISHVKQiL66QF/f0iIiMyGDLpPv56+9i7/uPbwcvDzAwcHYB"
    "MNhCYZj/bzd1lgzDLI4AAAAASUVORK5CYII=")

index.append('BZ')
catalog['BZ'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABpElE"
    "QVQokVWRv2uTYRSFz/v1a5Jq2kRNJEJCsaU6KIIUXPxBJ0FxdHBycvZfcHARBPE/cBPH7oJQ"
    "7SgFKzrZgrbW2LRJkybpl/eee69DKNSHMx14pieM8B8ECGQAgNzxaccDEDC78eLZWQAGN8Uo"
    "X8gPhqcLUYyd3mR/Yiq0u1QInbQ3L7+mqNSyU7mdtqs6DQmxdKd1vp5lqlvN+H75SrdbErrQ"
    "69UEuJQAcAPVxZAEffR49e7l6cWkccNqDxqVJ09XcpOjSI1UoQIxAUFzKpQ+d6FZz5eL3bD+"
    "/dPal4/+a1QLxavXNqOEKCZ0wBLQ1FzVqahfbJb9TK5YybbpW83a7FwZ1dr8H1GLdNGxEE0V"
    "oh7VmtvlQz84yo56+epPLOz19kcYtH9PS3QRJw2wFBGiLnRR//ytvnhvlVl6+/p8VNvo/Dic"
    "aa18uBmpkUZLAEuRgQpRRGLA5PWrW/cfrs3kOyQOEN49X9rZh9BoiWoAkGJ3NwzPNaachNDj"
    "RLr+dqFU6qvi716u7F6a6au5qbPvwGZonQg5zjwE+gCBAlA4UXrMPyh0GTDv/QqiAAAAAElF"
    "TkSuQmCC")

index.append('CA')
catalog['CA'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABoUlE"
    "QVQokU2RvW4TQRhF78zOetc/YMsOFqJBQAEPQEidmpI3SJkOCckSVV6AhoKfElHnBRBvYJTK"
    "EkUKIhNSRCTKkoWdmW/muxSWEKe6xSmudEzz9JleXVln/fnpcLnEf5AEEGM8m816QAQS4HR9"
    "2l88B1BcXw4GA5IkY9MYRTG5SdXSlbf291010py/vX7lMKoB6s+LfHm+sZOyffNWfR6+fFEY"
    "QzIeH2t/Yu/cjoBDUmalJKREMqy+Bo3FyRrR/15+cTlXT7bRddnUDDEBTmNEzhRhCCTbd+/d"
    "aGCGY5Z1/vDx+vvJ/PBQuwDTmRgS4OC9SlIRjUISD+7y6IjlyLTeNmfFw0eqmkJnXV+jRMAm"
    "75mEIho8ydHe3p/ZVmwakbadzseLBUnxniHmuLnUthTREDQEVUVdjw8Ous+fTFVPd3ZMXeec"
    "GUIuI0QS4NLFD4owRPVeVUmaXontx7ZXo6pyzqqqvtNesClFwCVAg5jpxObOGLOJVU23DKmA"
    "tdYYU967X0zmKkkBcwwokIBfwI3Vynv/L/BmiMh6d7cHKADgLxaxMt388uCoAAAAAElFTkSu"
    "QmCC")

index.append('CB')
catalog['CB'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABTklE"
    "QVQokVWRL2/TcRyEr82P/UlaycKydCAIOIIhJAjI9AKC9AXMTPEOhsHxKta9BBI0eh5PBXSi"
    "yC0hdJ+77x2ivwYwl0dc7sQzwPExzs6wtYXVCgBsSAAg9Wz3IGE4HAB4P5vh6Mi7uyZbIqDZ"
    "SmQLoC2bCZPPl5cdAMy/Xc3nbTptd+9xxZbQYJ8upxzak+078KgDYKctfuj8/N3L5YunLa5E"
    "MeNKagMc+PB69aUD0FqTpOXy4pMG4PMnTNj3XMkaiBDAEIDUSJL8ec2PM36/qgyrb5tpG26y"
    "sH6QpGKNhjqdcrJfqX7134eBaWEIgGRVaTQ+fVuvnlVUaZv2JmEl0godAEnc2+PJyYfJg/pV"
    "ZdP5m7FsOQ+z/bW77ADw8P7+m9ccj3V7q63Qpq2EGxstaXbLb9Syw8HBxeNHWCxwc9PrXHut"
    "+s/x2v3Ozh9eL3jobZO0pwAAAABJRU5ErkJggg==")

index.append('CD')
catalog['CD'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABoUlE"
    "QVQokU2RPWjTARDFLzEoNhZt0cYU/gqCH4TiR4nB4sfkZGdnHVx1FAQXlw6CheLgIKKDk5tV"
    "R4eiaA1RSiF2KBmSGm3VBkHU5u6993dIKo7H3eO9d79McnGydf3FvtuTK4sVk8xlgDnMZe62"
    "AUvdzM1g5maescO3Hs2MN9YLSFMxpVJIpKAUEKggAQUV5OzNu7nkaPXS8e6dudJCe5RSSAQD"
    "aUgBBuigQwEmu/Nmnl1ZLD/8MLHQHoUUFEFHGmQx/9X71/SABwIy82yhVL184lVppAUSpENp"
    "6hPJ0tT5J8mO1W7Qoy8Lysyza7Xx+7VT79vFoAIK6Nz++o2zT4e3d6YvPC4MfOsLQgDNPJsf"
    "e31lfG5spPkvwLOPRz79GEjVfb50qLG+09E3CcgMuV/z5XvvKtVWHlT0d3pQK883kwND7V7p"
    "AAFRMvMtdtJnr27Uv2xtdgY3f8L66p7O79zy9yEPBggK1K78trXqy5y9qcy8Pf35z97iMEEB"
    "DCjYIyCQYEqK6jkoY8cO/pxaHrxWsMaZHstNrvhvlBnMZGZ/AR0PdcdtApHFAAAAAElFTkSu"
    "QmCC")

index.append('CF')
catalog['CF'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABj0lE"
    "QVQokU3KP0hVARiG8ffcc+x2K7qeweIqDWnSFEFrNdTqUK3V5ODQP5e7OkSTLS3h5OzgkhBc"
    "XbMlCLpNIRQoaESbcU+D3/e939tgQb/tgafoXN0pu/Wbpd6d5z/Qgmeywa/PPQLjl/YxlgjC"
    "EiQsYVGV3fGdtY2pqaX9a2vrHx4zoSOcevdUES9vTHgpUkx5iMrlZ9+qJLaG8/c6L16/fZSF"
    "0pGUdnfl/v1yBuWhYHrofF2gQXFw8HNycoIEgKIQAECam5O5tjb1T6aKohgOP1WAAyhLSIIg"
    "SJLMZK7/AAIwGo2qhfcLi9cXD48OKTLJVjLjoZncV7+uRjKNFCOjbtf9Qb8abA5mL87uNXuW"
    "FgxPd9oDc5mtfFwxmtMtzenT3elmo6kQcLnRIsNplm40mcv9703zygPBMnACVX8e92fit3vK"
    "M52wlMlcZq9uOdOTlvIUz4xx+QmquydxpQ6pJzlAySXHBUfE7Z5LIYVEgADP3USxvY1WC50O"
    "2m2cPgsQmeAMEsgv4HESmTj2B5KxXKDSA72eAAAAAElFTkSuQmCC")

index.append('CH')
catalog['CH'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAYAAAB24g05AAAABHNCSVQICAgIfAhkiAAAALBJ"
    "REFUKJGFUsEVgzAI/VEPvk5gOoZrZIyM4SwePXp0Jydo/kXtIU1NClYuEODDh2BMVQMA9n07"
    "8EeY2Y+qNsluiqxhUJAESLQfm+NYhBsBWFcgBGCaTp9zUVtbMAGAShQIQZsAJNWYZJB3TsB5"
    "jg/vRbpk8Au+kbJABtDAJMUOviMQQEsCzkXwspxZfR+1tfcMLmlf+MUS266LhvdnsdRZ+QWT"
    "LvG1b4fWQ/M9s0t8Ay6VSOU8nBloAAAAAElFTkSuQmCC")

index.append('CL')
catalog['CL'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABC0lE"
    "QVQokXWMPUpDURCFv3vfExOLIGqT0qxBRFyKhTaCZRZg5U5SuQcbWyE2toJIEOxifiSa5M7c"
    "GYsEyTPmcDgwnO9M4OqBb0UhwszYiblzCti/gpKJXp8dXhzvdx4HWXKjXkAADyEURRFj9BVN"
    "JpOIcXlygHN+tPf2MX8fzWFZr33HzCJw+zTI7nfPI1HXjG+WmZXUdse6dXM/VEe3a5rTJhoA"
    "Sl5f4me9GM8sezRLjbpPpw6rb5eDGK3fDxlot73Xc0mIuohL8iSekkslY6s17HZLgyDiKaFa"
    "gao0qq6qUBoUqi7CL7Q+U0EzObMYRFGaTRdBMyqIBBFUWaQqOS9sEL7AVkz1/FMBP4aNaAa6"
    "+++rAAAAAElFTkSuQmCC")

index.append('CN')
catalog['CN'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABJElE"
    "QVQokV2RMUpDYRCEJ8mLYhGwElOkEAQvoEfwGOYQllp4HQuRmMLGUkG8gJ7AQkUQExOzM7tr"
    "8b8XHsIWw7Iz+7HbeQECCKALANg+xvsd0DQFAFBLVwHsXpwPTjC7jP5ebB759qvPJoIppSRD"
    "SrLo5+m0CmAwzt7O62Dsn2ceC24cev+A8xsmmcakhVmS1WikAjK/ypj5z637LzsDT5m/MY1h"
    "llyFWV2kgApAfPnHqZDaOlK1z+UDl/eMVnaaBZnFEECsvLAun4QtziaWZJIlFU18SLUhXSmB"
    "5gt9XzNphSfW2cVARm0oLSlbAP+nJaw3QCqIQYZZPdFwp1mIKU93AJWAoHrDYZJdeYp1fHP+"
    "kOAe7ukuoPPYemRbqPXsaKoC/gA6rnMwXmwqoAAAAABJRU5ErkJggg==")

index.append('CO')
catalog['CO'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABEUlE"
    "QVQokWWOsa6OYRCEn48vR6MhIhQSci5A7Z60Kjegdiki0WuERknjiEKhUvrtzDuj+M+fHGwm"
    "kye7O5vdfr4h4bJCQnIC/yfYE24/fg6U0NBVFnFr4qLGjVq3unj9ageg1Xe6yGpFV6NWJ59m"
    "Gm03HsTsQOnf29O6UTPtnEBEHnYMLHA3g4u6qVVRt2mnqEwrEGHj1rtnLx5++zFelSNXvoRR"
    "5MyRvR7du/H25fudAzZ2xz15RsdYR50rhyA7B36LX4OFzJgxUuWOLzsy9jYDsH/qk7ufn+ri"
    "azUZVcoMUjSRjm9VyvLZ+flHPuxncN2Ha/fv1O5MfRyvSkfGzkqXuw43YfsCBmAgEPAV/0fA"
    "H7CAa8EDIY7HAAAAAElFTkSuQmCC")

index.append('CR')
catalog['CR'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABJElE"
    "QVQokWVRMUpDURCcl/wiKKg38BCeQCy8gxfwAEJuEFKr2FhYBCzEU3gLWy2sFDSKkt2dGYuf"
    "RMVhGBjYXYadhu0JAktIiIIF1C/GSgNQh43RxeWhARuyRZckqWjSpJJiqcQqn44v2sIeArAB"
    "GIBtwH/R+9ba63zeYTbD3p6/Pi3DslakLNkCadJS29iM25v2AOyMx3x8dJWyXOlMZCrSGYpw"
    "Zq/D3d37u7tOAIrOVJX7hQhnrucU4QhlDqoEdALEUpUynOVMZzjy9+2lZgpob9PJaP9AH+99"
    "0F61ym0SpGSLg62tp/Oz9rVYdMOhpeWLbNv6/yWgtfb88tKAk8nVEUukiiqKVJYoslwSKclF"
    "2b6ejhtwvGpxXefa/hQMFCBg8A0y93t3R/GzpwAAAABJRU5ErkJggg==")

index.append('CZ')
catalog['CZ'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABO0lE"
    "QVQokXWRvWpUcRDFz978CWgUVkgQ/CjcUhtBbFcrC0GwSG9vkRfxRQIR8Va+QNrsRhDFF5C1"
    "EBVSeGfOOWNxs0uQeIqZU/zODMNM8Orkzcvd1892AQCwPdaLZiPa7YbPtq/f/brSw3vXxkwV"
    "gKpNu6DVatV1XbdTPDo9O/7yeyQ22D/jx4XNhlxXNfSfLHt+f1r/0XmAtKtctaPh3UdRfvrg"
    "8gwAki1o21RJdUXD4Ylufjia7W2XZLIkSCWVNJlOcXDQSMqQiqqUX5y+vbN4P0RWhDIrojId"
    "UZltNvs1DE20XClTNV/280V/DmUiApkVgUyQIAE0BDnSi/7Jsq+Icd4mVhFmgiqJQEOYquff"
    "jh//WOLWbTO7TGSarMwJWeTW+gwADX/4ef/RPn5+BwwYAMC15/j+temAvzMZeRNn/GS2AAAA"
    "AElFTkSuQmCC")

index.append('DE')
catalog['DE'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABNUlE"
    "QVQokXWQMaoUARBEa2ZnwUgUEwN/aqjgNzJUzD5o6D1+4C28iLCXMNHUowgy3V3dVQazaOQL"
    "iqbgVdDL3d07zA4Jp61axbYICP9hI/f7+88AJEma6Zkj+wrZ090kebl83dZ1BfD9x88mj7qq"
    "Kisr/xIRGfHh41sA2w3wSLohu7urmmQmM6uKEZWRmdwjK582AWyfmLczt6SrfGSmMxWpDGcq"
    "wnuq4szegM0sz6DKRWe4SlchnKlM7fvhm2VgM4Dpf8NVjlDEVdt3V06kqzwcYOMb4Ll9kkei"
    "xDkOt0W5MbQbov16iW/Y/AJ+Jj8eaaSxRtOWpD7erLElCX6y6D02FNzE+dWKWjRWLWqY1li0"
    "2h677Ub1+hvL5cvLBwMVfELuEOCHWAMa1AlT0OC8QgGc8KvwBzggh2H1+79aAAAAAElFTkSu"
    "QmCC")

index.append('DK')
catalog['DK'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABKElE"
    "QVQokWVSO04DUQyc93YLBIpEhZRiG27AQeAgtNDlVilziFwhAkFBBFKWBKR4bA/FW5AAy4Vl"
    "j39jlxFITJLAeURut7v5PIE/6gCAPoGTxQIAMpFZSqmz2dn9XX58yl0k3EXKPcmX5bIHACif"
    "nhGhCEmKiM1DHvYiZRRNZknWYcjWAakJTQKQlLRsUJqMaSaykDYlRMhd7qBLUq3a72EETWYi"
    "YQZSZAIlIkopkiS18spUM35LqXXc7Xr8xCAILe8/WlKRMrPfdt3s9jY2G7nL7HS10jgerm9y"
    "/97maQvIvb+8fFqvewCNPpEgp2Z2xPE4Oc3khAciJpZEr/O5SHkAQNfVYcDhUBr97jUCERkB"
    "oDwD/n1IBy7e3vzx8fXq6sfZ/qCduQJfBq5Z9b21BQQAAAAASUVORK5CYII=")

index.append('DO')
catalog['DO'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABN0lE"
    "QVQokXVRMWqUYRSc798fixWEEIwsmCKN4gG8jYVN6lR2OYOFJxCCF0iX3gukiN1KakFZu+TN"
    "vBmLfyOxcIrh8Zh5A/PG6s0FHtAyyrB/fnv/TLvvB4cGDOgRzwA+nb8NkMAdO7LXTzDm9fPT"
    "05CRQloKub28nAEk+PHrrhd1uzudzG5utymG5aqQq+NjA3MbCbrTTrfVkewkcarCcjFVJidS"
    "wAxZflBrb5hHMq1S5SqTqQppUsDo7jFGkiQA8n+Mafq9240tcHh2ptvbsEKZDOvg6qqn+d2H"
    "r5Kplkz55YunXz5+ngxkHypXpSr3lfu76ixqypSl7jag2cC+OHJxuiqJk+L+trrVaf81UNNm"
    "M8ioLQ5yTKtpjM3RuttLdbbVATxu/n3kwq9urnfro9cn54/Wy4A/xLJ3307zCyYAAAAASUVO"
    "RK5CYII=")

index.append('DZ')
catalog['DZ'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABmUlE"
    "QVQokU2PPWtUURCG33PO3bu7lxWjZA2bsIUEFMk/0DqI6EJs9AfY2FhYGkFsBP0B1tqITSxD"
    "GrHRRrAQO0VBtsr6lTWud8+Zr2NxF3WKYYrnnZnHnbw1msYZAAAkEok0zkJZfbqzU4YWgJyz"
    "mZmZiJhZ8a2e3t+6kQHLpqaiKiJWhP5g0IZHzpb/1Xg8LgpfZOTJ4Q9RFRUyYab1nySr720w"
    "sFbLbBGpqirnXMDMchZVVmGV4a/68sevXUo17/XOj9DvI4S/F8zMRyFRIRUWpiybn79XlO6e"
    "OREujGa7z5z3jV4TEBEfiUSVhZJyIlr+PX8ntN8p5vdupzevJ9eueO9DCM65Rt1niqyShBNT"
    "Up4Yn9qflgfT9s3tcv306qOdhg4hLF5CrEmFmBNTSvHpSmfewoNXH/jJ46NbVzORmQFwzgFQ"
    "VY9Ys3ASSkyR0tsurm8sPVzrVZuX8nBonc7/xgAKxEMSXu4tkTIxJ2Fqp/FKaefO5uwc0Kxv"
    "aDNzuHgMXw4QgRqLPgNKvHj+8nj3SAOpajMA+AMw503J6/1+rgAAAABJRU5ErkJggg==")

index.append('EC')
catalog['EC'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABKklE"
    "QVQokW2OPUqdYRCFz6evIFcJiCERK7lNmuwgq8kCbMTaStKkdUMuINiqlSI2MYkXo6LvnB+L"
    "zysKDofhcOZhZobHky/wDcay7Q7T6HY3YwOGDRM2ADT43+KnfQCAkCxEiBONQpgQYeyEs+Of"
    "DWhAwKs54YQJYyaEGVRcMYelz+5oMAC/S8eVEKmYScGk0drkZlh8RB5eHpjrmRvXJzW0++UJ"
    "2sfdvR/fv/2+vqUiizJlSkVRLkkU6ZI31lcPti/a9enm5ezDxZ9Wcsmki+50yUUVMw/1XxNg"
    "2vBgahy7+GI0ms5wPEVLBjwcAZs7O/3sLFWueu69u8rkmIQVamk6/XV42LaA5bsZ1lbCSmkk"
    "wEoxrIghQ0HK7d+vwHAOEPBb8ZXBK7MAPAH8bIEjRK/4bgAAAABJRU5ErkJggg==")

index.append('EE')
catalog['EE'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAAA8klE"
    "QVQokYWRMUpDURBFz4MHLsAilVi5BBcgrsfWxl25gSC4gCwhBFPbhpk7dyy++cZCPM0beOde"
    "GGbwuGXFEEZGJkSYMCcRIsTJpCbw8nQH2Li73DJlq1plFVlWOd1Z/fr0Ppfqj88sd7mzXNVZ"
    "zuqUszrkKGf65voKNDE25Zat6qqOsqpz8eRQh5x2lkGT7UPdP+twkKRUKhciMjPi/GRm3t7C"
    "2wSkysw18P19qUcsAjCBKkmKjHN/RORl91oBjOPxuNlsuhvovwHGGLvdbtpe1H8DgO1pe7XX"
    "A44x+MWA/gns93vbGGNJtu3vYVnUZ4Av1C5+7JEmXZcAAAAASUVORK5CYII=")

index.append('EG')
catalog['EG'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABI0lE"
    "QVQokZ2PIU5DYRCE5388UCSoUipwNWCqkByCO5D6OhIsqUByABwXqEFyhl6gCkigwVQQ+s/s"
    "DuIVAki+bCaTyewmW96AxIb8rd+jH2GbwO7lJQBnItMRVjhkCZJJS0laAvk8m7UADMfjEyIc"
    "AdIRJk0m6UqzZq0gm8NDAe3OyUlzdIze/uZ8BCK45tbpe/OwnayWGslS6fV27u9LRJRSANiG"
    "YTgzPpa34OP2wQXc+otSymq1arp3Nxlsu75ca73KWNfnK/8EyMzmT9u2Lcof3EvzV9/OzDIa"
    "jSaTyXK5jAhJkkidn73aurnbk5QZkiKi3+9Pp9MCYDweLxYLkiRrrbXWzpDsnEQphsPhfD5v"
    "AZAaDAYkpZC6RUrqtDvfgX/wCf7XTmN4WqTRAAAAAElFTkSuQmCC")

index.append('ES')
catalog['ES'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABH0lE"
    "QVQokX2RrU5cURSFv3PntghCJQkCBUgQ9RieAVmN6CtMJsFjeBZsVVUtg2dUK5owyYTOlLln"
    "/yzEvRM6CenOypcl9l5i7SLeJrf9uyoCxmNAmWQSIQ/C5Y67zPQPl3d3LQDSz19EECGznoOq"
    "yapqlVk5PExoCVQEAfF3kS8L7e3X0Y5LJlWpDiaNYvlMkwABvlx53MJNu3q6WHd1s70RVRjQ"
    "ABCSje6/+G8vXycfHo+6edmKH248kwaQXPL27OTPSXI9GZ1+bvVR6qS6YZUqskyaTPqYH48P"
    "u+d1fdl9n35bf1ptsgeCS+5O6WB0dZWzGUMtQycDe+OGRzk+nk+nbUJjzsGBzPDADbNihjs9"
    "3fvGiUgoq+1H/ufH/bwCdrRKr4/GDeYAAAAASUVORK5CYII=")

index.append('ESPERANTO')
catalog['ESPERANTO'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAYAAAB24g05AAAABHNCSVQICAgIfAhkiAAAALtJ"
    "REFUKJHNzLEKglAYhuH3yCGEaDu41RhODjk4uUejV+A1eAVdhIObU12DEDjW7BK1BdFmNVRk"
    "GdjqEFhOfePP/7winIUPy7RkVVUIIQCYLqe3dJ8+adqei7RMS7ojV4u3Mf7QRwiBcTZ6dBo5"
    "ABpAtI4IVgHewiMv8u9kPTDuj3EMB1vZKF39FJAASlckk4Rref0JA8goi+6D16BbP2bH7PvA"
    "fDMvKOk2v36e1hb+T0Cy40TJo5U+cHsD6JgyzFPQGaoAAAAASUVORK5CYII=")

index.append('ET')
catalog['ET'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABhElE"
    "QVQokV2LsWuTYRjE7/3yGSsWDbRKGgixnYRujg4d/A8KXerk4iK4OIiguDQ4Cy5uTk7ZRHB2"
    "FXEqKFhqCUrBBFNjkn5573mec6iKevyG+3FcwvYqJmOcJAzZEBmZMMCACshA/l0WUGL87f7N"
    "RwAiFHKPsHC3bOEWMne6mxvD6Xxx+3GJqg6mz6OBh3sEI3uyVLboMZ3usWI2ZCPJ9nITQIm1"
    "wXJjzmJm4RE2J85dvFOr1T1iUlWH/QdSRae5Nc+fxQUkd6R0F+hLBtmTt93hbGmj3Ts4urQ7"
    "uNKoH967ekPKElPqjMevCwCAS5QocJrrG+3ewdGqRVzrPB8eN6T8C1gECgCSSSZkKaeierW/"
    "FcqtxQ8vP26fOTX6c4AYgUQgdW/pyyeYyXKeY2fz6ffTiwwt/Bh1n22VmoiEWWqvDXvvygBq"
    "fdM+QYosc364c/19c93NL++9KThTzjLCHMcOoAygoGFlRSTMYSzIdX4VTJ2WzGAG9xMCSFMg"
    "/gL/6n8TgJ8hCj2miB4d6gAAAABJRU5ErkJggg==")

index.append('FI')
catalog['FI'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABN0lE"
    "QVQokW2RvWqUYRCFz3x+ooQVFAIKCRaSIlYKtpZeR65C0moj8UpSCNYWBsRKOysF0ZA0ESQB"
    "Q7JF8r5nnkmxy+KKD1PNz4EzJ46OzzVnWHv2WQ1Nhq+7j1duykjMMQCnZ9NxkO6tTiRV1avt"
    "TYpMbT64XVWz5t98+9lGpNlA0q+TS6NM6n9ERDMjKJmL9VSCXQlVS/IqFNFM6MnHnecbRyct"
    "k9bLWT0paC67etI6Ni1ZX73x5uX7URdu5rKTWU66y4lNz+qmGXda0ns1I12MwlVVFNBdNs0A"
    "mZUJSQIJFCARPw7+3F+/BVRp6/V+N928fbHBsnNJEfHpy+E4jFwLRUih2XY3VSX9U5IEbRQs"
    "Xn73znUntiJCS8TiJr7v/14E+ejhntSk4d2Hp5MVGWS8SBudTadX03Ro9+qwo6AAAAAASUVO"
    "RK5CYII=")

index.append('FJ')
catalog['FJ'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABzElE"
    "QVQokU2RP2iTYRjE702+tDEl1mgS27TFarFoh9JWHVxcKkKd2kGqg4OTFFGRDoIViujmIg4F"
    "FxUHRUGnWBCqoLSCQ0HBuFnt0qQojSY1Cd/7PM85BP9sd8PB7+7c0vmZhTA3e+fT7I73iZbI"
    "5WL32c7K+PjBscpV5AxiaBjEYIaaQeCGey4+fzApO3ONwkfWaq4j157dNl+or6aHgzhCUIxi"
    "FKUYb89tBnfliSv0b3n7MlouW73ekkrVBYeFiweGkKQqxeiVXtnR5hBakE8Mnenbnyiv1YVu"
    "eybe17vxpViotnqlKqfzl2L1avqklO55dPfOhdORtZFjLh5//DlY8W1fa7GHP7siu3ZvsNUr"
    "KTQvmVNCCbOnxbxAEKQH9uVLyaVIf6Ozy5m+Womu7h0YPCS+TFPi+q3YVpYmJzKPnmIkghM/"
    "HK7pzVGsVZvN0CQWpTfWyHMvrvSUClrdZBD9Nnjk6OJUgNCUTpRiEKVXemNTEGyfvZFpZ3Fi"
    "LPtsvlFxePM9gJhoVJT+b+DPMiE485rH93DU+/sfuLwOhAjQMI9IKgltIhGiUHOiEIMQyyW8"
    "u7Bg61QDzBymfkEMoSFs/op/1v4TYjAA+A0AOEaMXbWFtAAAAABJRU5ErkJggg==")

index.append('FO')
catalog['FO'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABQklE"
    "QVQokW2RvUoDQRDHZ7kVm9wV4S4GycUPCD6FhYW+gYXvICjEj9pOW30CCx/ABxARsbFNIYci"
    "Z9CQi0KKNGZmdsZiY4gmv2JYZnb+O/sfMxgM4Jew3n3uNxrzWb9bEREAkCksAJRKJQBQ1ePT"
    "JC4O9stJFEWq6pOTdDod67V9uV0wf+RvQ9JZGGNGL/jbBEBOhB3yf2GPn9CGte7RWdzuOWZh"
    "p0BITrcOP5GUWJCVSJCEWJeq9u7ixWQAyUmT33N1rMTKpIRKJERKKERC6A9z9ZXW9YNdC572"
    "ypV8yOiUSS/bO9sLV0iCrMQjbR9XK/YRWrb/VQ3D0I+42ewp4hDl/jye/oAx5nV32Xq/fQpJ"
    "hRBnmeTNZGYrImPLF+Mg+K6lSWCMgT8YgFGPKYpivMU0zW9hfQNusqw2uV0QYRHf8AN4MF/m"
    "C35t4wAAAABJRU5ErkJggg==")

index.append('FR')
catalog['FR'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABPElE"
    "QVQokW2RPYpUURSE67x+BiM4iYK0Zi5AcANGxoIbMTExFjE0EWPd02xBzIycbu49t34MegQD"
    "v6iCgiqq6t3XG/zF9NvX15LmnJJIVtWzVy98C04QMLADePPyCYA4do7HB0kAJLGd5N7Hz+Fy"
    "d9b69f7DDgDB7/OSYvt0OiW5uKtK0vXPH+6Znnj4iMAGI44U2VS2bUtSVRdh2z08p9dMt4Gd"
    "tGzJUmgl2bYtvtQJAM+Znu5V3Q3s3bQjedliDoeDbQP7BruqynNmtbuLNLA3TWXJlEnnHyQl"
    "SU91p9d2SRhN2aSXzCX7ro10p9Wdnl4LXAb2MUh60aQW/R/mDJfXKorAfu4mff/qIILcLvtc"
    "fqyqqjo8PposMVoG9vOZn77fjNHnwdvRT6+O3b3GaKm7xxjPv3wz7m4G8AfurXtmcb4hTgAA"
    "AABJRU5ErkJggg==")

index.append('GB')
catalog['GB'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAACAklE"
    "QVQokQXBW0hTYQDA8f93XKtWlIdFmkaidoGkhyiokRDRzRFFPkSZRQkVQhJIIPQQlpYRXZ5S"
    "KUPowSLohrMLuS5iuaAyy7UopqVZmYquXc7Ozrbv6/cTNbXt7oJxvB7l3t0RmCWVWrHMkX+n"
    "TujO4IbKT4GIQm4vSqRbL80oP9zQpVNzqn3kr5RGTPo/yETyx28ZNaR891p+6Y3E5OBPKQ1T"
    "PrgpwyGfX67f1qahNAVvgw6Rmyc62vMc8WhciP4+8S0YNkS+IyKOVwvXxqans+MGRKWGAEDw"
    "Zlhny2a8j7MyogwMYI7lZIQ5WUPDmSvdztA/LAvA5tZ6crXVC+1TKBhRFC3nQj39QfoCvHpP"
    "1TEGv1bNizHXUs4Fdi7alO8lJW4+BpAwOMTYLybGCUUwDUb/8LyMpIlpkUpxZM8k2ETBEvIW"
    "49BB4XKRmUnFASIRTJOZdq63YBiYSRIWRYX6iy6bR9+ZL7OGE9kCigtD1NfR8YQ1K5k7nbvP"
    "KCvlWvPRW9nRuFmSYb/KVk0qhQIoXjTB6VpO1E4m7Bgm8VTPpJPGVvbua9zkV1Y0nU6BpSFB"
    "Ubw0rHyd6uz57pE5tmlKHSxV5TuydXV7KEve91g+741Kk3QSkjZU+vvDpkBbi1ZRfW7/PabS"
    "63bNX9vpBS73rlKjn5uJHXL4Jx55cgBc/wFytP7Lx9suAgAAAABJRU5ErkJggg==")

index.append('GE')
catalog['GE'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABtklE"
    "QVQokUWRT2sTURTF73t5mUCptg0BjUhXuo60OouCf3cu/IPahfgBXPkNBMFVoRuhIPgN3HRR"
    "BQMt1kXQEpVqcRvIIo1aEhOcSTLz5t13j4uJehYXLvdy4Jyf6vV69Fciks/gdJU8TTodERER"
    "Zv53MkRUqVQwHPivX3TtnFooA5StrYHdXLUKAMMhv3urLoRUPdVqtXTu7b4dDD5/cAf7AIjg"
    "v3f94dTe7n862q3bvQYRMYshIgDFi1fmj82a2nK+gh2cAwDAXL5WLgUmXAEgwmr86GHx9l38"
    "jkgrZBm8R5Lw3ns4p5eWEZTgPRW0TKyeP/7zyWOTbrzQ5Yo/7IAZjsk5iMfMDDzz6y2kCRyL"
    "c3CusLg46hwZ82BVnw/pzFlSSqyF90gm/LEJZn3pKgUBhAtK+9TqhXJQ36Zut8vMzrl4p55l"
    "mbU2TdPB/Xu/7twYj8ej0SiO4x8b61EUxXHcaDR0XnD6anO4/Way+TIPCvc/dPz8WX93J1p/"
    "CkBEjIgAKC2Fc/1+KVzJW1YnTlJmlVJENHtr1SRJ8frNHJxqt9tTkEJCU6hBrSZEo2aTmfO/"
    "XET0B9K2TnP0V/LHAAAAAElFTkSuQmCC")

index.append('GH')
catalog['GH'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABF0lE"
    "QVQokW2OPYpUARCE6+kLhA2Ml2VMvIE38AaCiayB4GYewGQjT6ORsOM5PIOJHkBZ0KmfLoO3"
    "g7NgUxRNV33QS/Fv5v7+Xy0FcH0NoDOYQVIHcW3YlXrit/v9CgBov/9AgqTS5neiKpastOx2"
    "A6wAOr3XJmvfVcVSGwBpgHWCZQng1qgL3f7Wp4+idHnJs0ds1bIVFuEXtpfSqjXgVjefub/R"
    "4Y+W8urt1t4Az+ABgNatiy3gq5d8csHdxeHNa7aHI0BUM1gfvse7F/r2kx4rZMShnkvRsy9U"
    "xFCRx08f++sHrDAcK9JoixlqdHQp9MgTT0CsIDQ+PztX5Ik3cuSxRo5dJ0mTCQYLrgACBngi"
    "n1x81ADAX4YyZWXkOpMSAAAAAElFTkSuQmCC")

index.append('GL')
catalog['GL'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABJklE"
    "QVQokWVPu2oCURA9V1cLC8GFBKwC/oKNH2JnbWtvn7QJWiSFjVr4G6IkNta2IhaL3e7i2zt3"
    "TopdxMfhwDAz58zDxHGMG6gCUL2GJ3gAisUiAJIAEIY8neD7zOWS4i2CIPCSwSSx22Ew4HyO"
    "45G+z0aDtdqt2hgjIplUTaLf52SC7ZaHA1crvn9wsXjYoKqZ9PYg4HQKK7xcaC0vF+73HI2e"
    "DcY1m6ZeZxRRBKp0Sid07pEiplSK220PvR4LBary6xO8/3I8ZquVLrTWVCoahhkFaC2XS/z+"
    "3amt5XB4VUOEIgJ4CmRFeD6j06Eqq1Vms9xs2O1yNksNYiEOziExZKygXKa1+P7B6wvyebNe"
    "I4rw9gYRiMC5hAqYPaA3xH360ALwDxiKW0pwzWHQAAAAAElFTkSuQmCC")

index.append('GR')
catalog['GR'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABTklE"
    "QVQokWWRP0sDQRDF311ONGLgCBEb01jpgV1EwUIQyWcR8TtYCxYW4qcQRAWLdEYFEcUUAUnE"
    "qIdICvEPBDXH7nsWe4kBYWZ4swxvfzvr5ZbOAVzshgBKq2+g951YJERCJCatPwY/RELIBACW"
    "S2EURZJW5m4h7W1OSxAACXIpSRQe4xcvjuNisSgJAABJ5zfvFCiRkkSmOjvin1aeAzfUnwZQ"
    "u/u2pLGylsbKGBlLWuXDYGOn7g0vnpbn8wdbM5KW1+qkjrejlAIpirvJ9/1mMw5M0mN0DFR5"
    "vU5HQqUhURrLBrXqvdduvxYK4YDhoGA/Afm+32o9BfvVzsJs5qtL9ZzkvHswFJwYHcmcVTrB"
    "yVV7ajL30UmslaWsZV/QtaSlLJXPDW0eNrzGw+d4flgS3PogSUgf+kcIkcLlddPDxFH6kV0C"
    "5l8QcOdw9RfrG2tQxEeGNAAAAABJRU5ErkJggg==")

index.append('GT')
catalog['GT'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABG0lE"
    "QVQokWWPMWpWURSEv5t3i7+QYCDEwsIyy7DKHizsk8reFbgBV+Ai3IRrsLIIhCBJiI/HvXNm"
    "LF5A8D/NwOE7M3Pacp3aADCs/vX14dBtG7D9tPryw2tkZDZzoNczXz4ClDPV3pyfLSdAkgCn"
    "MzefYkcVOd8+P3c6wO1jVGzCybLjSRI5P+9iZ1benjXkjrEjU0aV5B+9z1DsyFHB5o5cbirK"
    "Uf1PxxmVVOTMgkFHVKjKdKaODpKplx+mGuWOqBeDzKMEO0PJXskBd2SlzcoUU8cB2ZcqqgLq"
    "DEvLVKYYdYSToZDIqAA6q6dPLk6ZlW3QGkDbBZbGu3NIs1MB3Lj6w28zzGpW//h+9+qAJNu2"
    "75909f4CDN4t/gLVAHPNcoxygQAAAABJRU5ErkJggg==")

index.append('HK')
catalog['HK'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABUElE"
    "QVQokVWRsS6EcRDE5/vfR3wcIqJQ0OoUSjqPQucJJDyFwlOI4pQaEi+h4nJESET4jrCzu6O4"
    "I2wmky0mmd38qmcgMZ788b9yAIADCRSgdqB7cAAAmcpUBDwUjgiRIuX+64Nery4AoLy7V4Qi"
    "QCqiu7mlmem3k5M0iiYzkWVlBUABgNQ4HVFKKZ2Odne0tja1uFgVpNlIIh2oE1CE3OFeVVVz"
    "eKiXF93caqrpHB3NPDy87+3ZcDg6L4EagDxEwj0Zur7W9rZ6Z2oaZej8PD8+0gyk3POnweEu"
    "muivx8ezgDY2lKnppj09ZduOfyAdKAmAlJnoaTY5P6/1dZnp8kJNM9HtjtJyz98GuItMUuTX"
    "42O1v//Z71vbzl1dDQcDmcGZHiUiRxySXi0vF1Iecn6SubTUWVgYPj3Vq6twz4gSoQgHqj5g"
    "/7n+Xfw/+0ngG/KLPeZjxNoEAAAAAElFTkSuQmCC")

index.append('HN')
catalog['HN'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABYklE"
    "QVQokW2RMWsUcRTEZ+MSc14fIxhFCNjmCyjIgWI6BbvUfgkLEYuDQFC0SKHYiKBCIJ0g9oel"
    "pY0WRi4KWpjdsPt/82Ysluvy+PGYx/CKYSpcfoN/xDAUCkEhBfA0UONvebRzDYAEWZlIKWmm"
    "STPFFKmgmH63+7pGUQkd/uozlYmF7aAGSlFhRtH6hRHQ1dOnm7dvrJ10aTtlpwVbzoFwQpIl"
    "jUc1m+uYz+eSJGXmcRN7b79HRN+Xru+7rpu++Pr7z3Hbtk3TtG07m82WhrS2bZ9dxp3Jedv7"
    "n47efziyvb21NlquBteApCVJi9t90cv9H7bvTlbv3Vq1/ergZ9fnwjfJ6vHzz1uTq+0JJaVM"
    "CnDKkpRmyoYkyeNz9fTZxwrYvf/w5rfDNkLBLHQpGZGFjlChI8gU6Y1L4y8HT2qAZ2pcuTgK"
    "ihTpQQTFFNOZHh4sA6iAB8DKoshymhhaH/bKf9m9dMO0S+5pAAAAAElFTkSuQmCC")

index.append('HR')
catalog['HR'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABbElE"
    "QVQokW2RMWhTcRjE78VUO3QRRHAIiiCU7u3cDl3EVQQXETrpLAgFHQulgtDZxaHYybmb4JIh"
    "Q0cHt/jImOTl/d9L/t939zmkjQg9juOG301XBP5J//cbXQSAw0MAIQFApxMK0YMqLMMs3OM6"
    "6/PzLgAg4k8JEmSYvXt+4g/Df4c/0un712E5cg6zotcT0AEQihVt5PHXt5/Wp5+/HJyuT8wW"
    "kfOVzQSApCRJJOls29YuL/Pe3mIwmO/sVFXVNE1Kqa7rlNJoNLoakHR3d2+aJu/uLum2359M"
    "p0t6NpvVKZVlCZJL2tzMLKW0GAzm29ttv99sbY3H4yVdVVVd18PhsMCLn0evHpeThTGcMmo2"
    "/HX2/UOYPXv68faDTaNcckbv7p1vb350kd0lo1aDW/efvDw4M2qNMspcHuEMKgB1keWKextr"
    "TpEygZQzSJmCFCOcoQhXACqwfwEX5o7syEJ2zK9LdrgjC1wdjb8S03cdKh6exgAAAABJRU5E"
    "rkJggg==")

index.append('HU')
catalog['HU'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAAA7ElE"
    "QVQokX2OKVJDURBF76v6OBQuIgaWwSLYBCoKn11lH8kWYsAiolJ9h0b8fGa4out01elh7PEl"
    "AgBkAS08VwETgPV2C6CTTmBDjgUpUpMtNRkp5GG3mwAAzeeXtmE3GRtkyCZTDKurQl6t15kv"
    "dPqz3VUtdTGssLqYqnm+gGF7jAGgu9Fo9F8ZY5xOp/mli/UOvwZjJJk+pP/3d4/uJAMP2Dxu"
    "jq9HWXSVWC6aNMvFpZV1d3O7fzpMOEMSTeoilepim2XSJVO22gAmFBitrlcMJSukyVASm7IU"
    "WTZsG8DAPVBAAecfUEDjW94AIuRtBo6TpQMAAAAASUVORK5CYII=")

index.append('ID')
catalog['ID'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAAA9klE"
    "QVQokX2OO0oEURBF7+vuwEBhxsBAtyK4GkPXYeYCDN2PuAADQ82aEZXh1f0Y9AyoMB4qKKh7"
    "qqq9rEe8CwAACwAMGADAfe8f/YRZp3e3AGLDjhQ5IsyULIYMK8WQz/cPE0YA4NsrpEghd6Vy"
    "MVVmpSq9j+cXBiYIkZe0yZBghWUyRVcPy73CaiSBiYC/PvXxHgq7fUx1V4XlqlS5d7Pa8UkH"
    "ptXN9dHllTabSLHi5THFCmUxy3FpXK3Pnh6bJABJgiDIYYZhmOd5st1aS7LTDrBMSQ62/4/+"
    "0SbbwzAsDva01vCLBgSA7cn2drv1AgzDB1jUb/9idu6XLw4+AAAAAElFTkSuQmCC")

index.append('IE')
catalog['IE'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABFElE"
    "QVQokW2PPYpUYRREz/d4gjhM5M8wgyaCGxGMXICJoamZgjCrcCsmsxiTCexAMNIWH9+tW2XQ"
    "jYE9hxvWKeoOPsMGgEHs3u0WFsAHtD16+cLGE098n5U916+uj4n44vHFGAskCRArn94mFStd"
    "+/dfVlYg337v2t3de+3P753nH39+5fvXZKZqPHxmWDF22i23unKKZnomRZdhBZpWJEvWSdyp"
    "Gc+46AJWhNzVpb5TSDSTGRWSD0Jbsqan+kSw08dJ4zhJVGr2VKt6Ov7fqRmKVlo6TmpV1+Hu"
    "+EEzFG7cwMpGWZdnl9Ul9TIWYIzBgWXlyXNounEbBh/hJwg22Lj5cHP14Mq2JNv6cfv09RuD"
    "jwX8BUrDcqkVhANcAAAAAElFTkSuQmCC")

index.append('IL')
catalog['IL'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABDUlE"
    "QVQokW2RsUoDURBF54UEwcZusRDs/AlLBbuQykKsRBBBAiJ2aQQbEQWtLBQbBe3SaOtfhEBK"
    "q/cNO3OPxYbdjTrVfcy53Jl5KedsrZIkqRaS3N3M3L16pqWdfD8szCwALGSShYggZB64CCeE"
    "O9dH05RzLooCqBIqwX+VUppMJt0aatO3LwhO9xYM1ZzdauI5jT1/MP1mpcdqh+EdG2scDxqD"
    "u3dqGsAYbOIlJ7v0+5Ql+1sLEZI6kmoaeP+idMZjbl4pg6fPFg/u3q0NVc5hH+DqkeUeD2dE"
    "0O42O/w6zvlBfZnGMF96fVuXF7gsHJe5m4dFJA9zWYSFLCKFEPY2UprNZu2P/CvaZWY/KQh5"
    "EgUg+FQAAAAASUVORK5CYII=")

index.append('IN')
catalog['IN'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABQElE"
    "QVQokU1NvWpUYRA93/WiGIv4s6YwMYgxIBaKgq8hPoLgI1hapcuTmN4mYJNGsBE7qyx2W1nI"
    "Jm6xd86Pxd1dM5wZZs6ZOdNyhk14TAOGCRseG65HogeAw48AArcYUSCYLbxmdqiYcSVM6u/X"
    "zz0AIKkZIlhJIYorqXUd4iGuduOhid47r9vNZ+h3EidCNP199/t0X9arR+eHk1nCmA5bf98P"
    "TltJXWsJkozfPp3+Wl4sXjy992N6+e7NYydxnHSt/ZlfdB2AAMkGi8XyYHfry9n55WKZJE5G"
    "CYDdja5A0AIkyfODO99+znl96+WT20lW6gpu+IDjt8ez+YwmVSWWi2KpaJaKIkWae9t7J0cn"
    "PYiR2uz9XzU3l7RkYUCPATQntyajzaqaNGVJYihJkSwYDe+BASAwXAGvMFzDAPAP+jxNFrI5"
    "ksMAAAAASUVORK5CYII=")

index.append('IQ')
catalog['IQ'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABXElE"
    "QVQokZ2OsWqTYRSG309/bCnqICYh1IBCFl0CpR30CvQCpO1lZO6eqygddS9CxE3U6Jq6KC0B"
    "xRrBVmyaNMn3nnNeh9+iQicfXh6e5cBJwh/i375wSQC2tgAoAhFwlzncZAYzkfrL493dAgAg"
    "fTmEO9xFlv69TDErZ5Gp0QigwNqa7t5DpQZBluEhN7lklFHuoilCeY5KJbpduHtEzMOefeqa"
    "+czsjPnl4etXX99MZrPj6ejH2cn2+53ReDyZTIbDYfkSXnx+/uHnx/F8VF1ari7eePftbXHp"
    "8kJaPJ2f7h3vHYz2FdpsbkREAUDSo1sPs+cH9fvufu3K1dXqioTb1xuSVm62nuw/Xb/zGFJE"
    "pFar1W63j74fJSWS5kYyPGik0c1pTJGmnNZqtU6nU/T7/V6vNxgMSJLMOeecyyBZlhnNvNls"
    "AigAkFav10mauVl5SDMrbWZ+Dv6DX3iOQn5NOXoqAAAAAElFTkSuQmCC")

index.append('IR')
catalog['IR'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABVUlE"
    "QVQokV1Pu05CURCcczkBlZioBYUIDVQW+gHW/oKFf0BHb/wH7CyNhbG2wVhqQ0FMSOxM8BGs"
    "1BARgvfs2R0LHkEnk9nX7CbrcAL8YAoD4kwXGWbq4THE0f4RADMzmJpGU2WMNqGIxkgRjaJy"
    "dXrl4eHgeqM3o85NkRpUokqwICpBg2gorZaRwDcPmxvZjbXsWm/U21zZfPl+2cqXnr6ftlaK"
    "z6PX0nKxO+iW8+XHr8ft9e3hwdCpqnMOAEkQBDlOeXdLI/f2uJTlDM65wWCQTL6d9ibx8oIf"
    "78x6np9xEYCZOVWdLkzvk6psNCjCep2ZzNyfJEm/388c53KoVtluIw3sdJim7HS4s0vvacZW"
    "i2nKVgvjMR4ehjfXLgUytZp1uwiBIhThPFnIoeoqlc/7e29A8pOiUKAIoiIKRJwIYsREY4Qq"
    "VBGCAW4E2ALxt/w3AvALz3tDHmqDQaIAAAAASUVORK5CYII=")

index.append('IS')
catalog['IS'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABYUlE"
    "QVQokW1RsWrUcRjL7/5/h9ap7VXhKLQ4uvgABXFXN8EX8AlcvB4FEScHQRwENxEHcRMnB/sI"
    "DuImOPQG0bYcHBSO70u+OJzn1AxZQhJCGm68xgr6+uBn112Tur2XCCKIBRGECARAIHoAzw72"
    "AVS5tTacHADt8MlN0SyTRVmlZKXqzfhdDwDwr7OFqgDw+Bjw9Pd50qkiFayUyBptrQHsUVWC"
    "qiTbNtN2sEinlKykgiWJMhBNUmsNgG0Yp/fvbb7/4IvQWpvP5+0HMBw/4nRq0pmViUxnVoYj"
    "K7MinCzGpd29b1+OmqRlvGEYp3dvb378dGHDYDCYzWZ9t/Ni8vTW9M95Um/H+xVp+87kKLkc"
    "UMliiazdq5c/P3/VY0GqlrLtirCdVPwzmNTSQAFgjwiqrmyskQLQj0YARsN1qpIlWRKrJMgF"
    "VEP3+P+RZ7PD7xtb109OtrcfAgHU6uBaMf4CWkFmlGgAUDYAAAAASUVORK5CYII=")

index.append('IT')
catalog['IT'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAAA4klE"
    "QVQokYWRQUoDURBE38jfeYIRAhLIObLyFll5BzfmFF4l98gJsjUXkAjD7+pqFyMjCaKPontT"
    "Dxp64A0mAAzi/Hz2NQ+bjWFJ48L+aQ/Ydnkcxyqgalm7XUWUVBGXw6HRgHr/PKczM396C6dT"
    "9V4Rw2plaBi70imnMm7bVdX7LBBhaECSKsmSddu+FoCGkDMylP8JkmchLVndXfmXMEQY7hBR"
    "0bMrFdl/FyKQStL3SanImPO7oEBJJtCYCGu8HyNDSgAYhoGF9ZrMOYaBF/gAwQQTx9ejJNvz"
    "BB632/nHM18uVneE/JK6zgAAAABJRU5ErkJggg==")

index.append('JM')
catalog['JM'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAAByElE"
    "QVQokVWRT0gTcBzF3/5EtZpmG4NyNYsyvYycEZF5SCIp81DQIUQooVMFHYPoEkg3b0XUIZiC"
    "B7FDl+gfHcpIzCLaIGxtFGg5so05a/u+7/e3DjOoxzu8y+fw3vPU3mGhjFMfMZ0HADjAAfqP"
    "BagAEbyPIxqA53QXkmNX123AdME9LrqSmsLMqdZUnSq53qv9jTwc0l9F7u544J+YQvf92pHE"
    "/P49tt1r977zzbKxRjqSjAd4ISzRoMy+5tjTbUuAD8DezkOPXhTlt+1rYXfIfE4+rKhzHAjJ"
    "5agEybvjcv0OqxbMZrN+AGZWqerEE51N62AfT+5g+xqasS0gqbSMjHImJSQ3kwC8AFSNJMl0"
    "htduMvdVWoPS1iC5L3LpBmdSUpeqrgJmqqpCaY7IlbMS2yKZoswVqrGtMnyxuqt5FSAJwA+A"
    "pKqc6NLB47I2yMl5ufWN4ng+LGcSHN0pI0nenlRTRb300Z6Oob6l/l7+JIdzksxL2Vg2eV5g"
    "qsTORjl2kPGYflpomvu86GlvxNTLoY2b+OoHx/O2KKxvqk7pqNQmn54LW2/ECnmL9aQ8K8+w"
    "UELiLZYz/78rf3MFUKAFDw+gtQF/ALzGPhZXojC7AAAAAElFTkSuQmCC")

index.append('JO')
catalog['JO'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABLUlE"
    "QVQokZWOMUubcRjE741vnYSCuGQIxGq/gFvWTqEFIZMgCG6FLo4dXF261OxuWUqhIIRSCIqQ"
    "IWu3fIBAuxUkU+B/zz3noJEO7+KP47jhDg5XeBmVgX/n5xNgnpmZkiIkxRMk105yPB7XAHbg"
    "4z9/76Rv0pKUxGcKC0sphWSn0wFQA3Aa0jtpl+z2P/j0xE1UVbVYLFoJQMLBgTO7DE9+eTRq"
    "HNjOzBYA7O/77My9nkkvlx6N/PWyoQ5ERA3A87mHQ0+nYLFrHw786WPDJRtAKwGTvr3FarXY"
    "Kn7fb2wDeLxUJ7ARYfLmLYf9Teq6/PxOkWJRYUYkKUZq7/Wb2cWsTuB+M34ctX/vvWorIoMi"
    "k5ERCjmYIUmWUghUX4DPA2AbKGvFfyHWIYEAgAe4hlu5xLXqFgAAAABJRU5ErkJggg==")

index.append('JP')
catalog['JP'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABA0lE"
    "QVQokXWRPW4CMRCFx6utttgG0VBwDEoOQENBS4GUXAwlHeICFDTkGlsgEmmbRUKCImNrvhT+"
    "CSnyZNkz1hu992z3eDzkCWYW9+eiIJjVItI0jYgAcQZil48n9H1fZxK/xfHI6QOvzGYsFoXt"
    "nLOo8Ie937PdEgLqOZ34+uTlNQ5Eh1VxD8gwsNvhPappvb1zuRSREEIVwxWP3G6J6hVV7nfO"
    "Z7KEmdVmlntkPKaq8Ip6VPEeESaTlAGSpcJnNGK55FsTW5XVium0vJiI1MVSmtxsaBoOB1SZ"
    "z1mvS2IRCSG4YRjati23qYg+nctpRQQR6bquNrPr9Zo/Mliw/xBFfgAmk2yfBC2dxAAAAABJ"
    "RU5ErkJggg==")

index.append('KE')
catalog['KE'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABeklE"
    "QVQokZWOP09TYRyFz3t70UJqdLHqUkg3Q5j0U/gRymBgRcNsGmUiYXdnYnGtiwvRgaXuRG8b"
    "Y3JvA9II0qCXe9/z++NCE1efnOFJnuUA/0kA0O/3AdgcVSUpIqLqqlVdC0kRkoPBIAEA+GQy"
    "KYoiz/P89LS3vt6ezR5c/NrY3PxWFOPxOBuNsiwjCSAcD4fdtTU38znldGoHBxCxXm+p3b4J"
    "ZknaOBq8D2fAndc7OjlxERdCZKf6sx3SBdU3s4vd2qyqPUZXTZc7o8MPaQk0y0qvfruIRyYm"
    "57zWhaZTp+W1Rvc6WqRLRFVHIL0FJElACBbgIQQPl0J4I1FtqQWKMYICtaAGIBx/+dhdeaqq"
    "N1fdLn/keLufiNZbz+896ribu5t52kg/Hb0LeIUXz15+vcqopMVoftsWn4wZIg+7PNef0UpR"
    "iunq3cef94YpKohwpdmhUkxopPH7qtD4UOW+tdxBE3UxNVQI2AYqQIA4n/wjMhcDBGjhL1ti"
    "PSucHkU3AAAAAElFTkSuQmCC")

index.append('KG')
catalog['KG'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABKklE"
    "QVQokW1RMWqVYRCc78sTKwkGLAK+RhuDheI5cqPcxMoz5AKWYhHBwlqESIJdIIG8mdkdi/9/"
    "iYXLsmyxuzOzM27wGL2v/zYPaQDABsDTszMA6UY3quJCOXbsSLEhtR3pz/n5BgCQvvyNqlRF"
    "wpMaW2Gov6tvFbHJSHO7NbBpIJ3H6VGbU84PRmmccPeJfaeQkSI1MAGg6gF9vuF4LX+lv3C8"
    "4sEJQ4YrSAOzAbiWA6HGC+WAuWZdsQfzck+JjO0FIYtEMWL9Iu6YQ+Jol1v25W6dJlvCqkFa"
    "DoD0N423nO+VUv2gPis7Rmp72r0uLAJWVrz/yPlOuacv1DcK2RZcqdp/SR7Hx0OKq60h5adi"
    "z+caz9z2XH5YBWBcAd4b2f9zd3F9sXkCfwH251SCACM16gAAAABJRU5ErkJggg==")

index.append('KH')
catalog['KH'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABbUlE"
    "QVQokV2RsWpUYRCFz/29d/eGda9rWMiquGhjKRYGKzsDbmETfAftFUFFsbA14CMIqQy+gcRC"
    "3yI2ErUQqyAmO+fMjMVNIDh8DKcYZjhzKow/wQJ9CfAAenTSTwvUMLzaugYgAx7pAffwSCno"
    "cIXU6yRj5+2XGvjTpv38tfTI8JBnP2EeYkhJBhkmv7TWAr/rz8t7V388tf39cE8yPdyWg9ks"
    "PA6/7kHuy6M0S3Iwn9/GxwIA7ilVJFyjG9cHk3Oz588uvHzRjEdnb63DlWZpBhJAERDyIEVm"
    "VVYfPBzdXaBdQdt2m/enjx5n3chMZpKOgBoAXJCSdmalK13Xbdwpk/Np1i0WZTIpg+Z4fX8h"
    "gCTDDO4X32wBqJomM7Ouq+Ewgfn2dpUeUkgCKuDdk9c3v33/S4ZQSCeDdGNQQYU8eUi5X7k8"
    "2t15XwPdAYfjaUP1XoLHjw8p3VO9iDzwBKYV8AGwkyD/43TSPeUfjDQzrVjF/Z4AAAAASUVO"
    "RK5CYII=")

index.append('KR')
catalog['KR'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAAByklE"
    "QVQokU1RS08TYRS9X1uYdsa2004Z0o4kIMUJRlRSFq410oYfYHDnD/DB1hBt4p4FC/snhLia"
    "hYnB16qJj6hx0UUJAVL6mIlTtSl1Pu9hMWI4yck9q3PPyRH94YDOgJmJiDlU/3gKycwRIkrF"
    "1VRcbe3tf//8ZQwiFdd0VU1rWkZLJqLRbx8+HjSbE+m0qWeZORYa93o9x3H8H37ONGdnZtBs"
    "QkoUi612Z3t7S89kjGz2/NSUDD8A8H0/CILFpdJsIY+NDayt4cE9PFmfNieWV1Zc1z1qtwEw"
    "s+gPB0klQUTdbldNJsWrN/FnmwiC3zIyGv3R767+XL3T77kFqyCE2GsdxsKWAHK5HBG9dFMl"
    "GQ0iiaeLD1sjZf3g01VF0awCABJCMseYCQAAz/POaerAXrhvP4qOj+0rpn88apaW7eEv1+vn"
    "83kBEHOEmAE0Go1areY4TuVaYu568TCapeHxzcvjt25YOzuvq9VqvV4PO8SYiIhUVQXw9t37"
    "+Uvzj29f/LqkyL9YmFZ2dxvPt14YhqHrOoAwEgOwLKtcLnc6HdPME9GVC2qYc3KyUKlUDMOw"
    "bZuIiEkced7ZIf8fkixPhyYpJRExEfEJhTEZN2ZjOv0AAAAASUVORK5CYII=")

index.append('KW')
catalog['KW'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABKElE"
    "QVQokX1RP0odcRic3awi8WHxTMQnSBpPEN4BvISeIZ1t4J3APuoNcoBX5AYewNrWtCEQkeU3"
    "f74U65b6McUwDHwzTAcA18AeMAIjoBmZSZshQBgAbLLBV+Rjwris2JFKihTSUsiI5vZmOwDA"
    "cz39/O1z+9A0HTNkSJNmc2tpNE8XpxgxAEjK/6xfvry6OPh84DiJy6kkdmVS9nf2d7/tDgBs"
    "S9Ko9XJ9sjqpN67ruvViPQCQTFJSkLfcVYWuS9IDmB40Nr9jr0JVkgEAydZaQbq/z3JpT/ld"
    "9tQCSSX9YvF8dzdF0gB+f+GX29uX1qq1Iqu1kK9EhPzh7Ozvw8MAIJ1+fFodk6PcizX5pJ6c"
    "xuht2GX3QAfgETgC/gCZ98VMJgWzCOA/nWhlKT+0ek8AAAAASUVORK5CYII=")

index.append('KZ')
catalog['KZ'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABk0lE"
    "QVQokU3R32vNcRzH8df3e75a7XA2u2CJNY3UslLIzYpyoZW7ccElhRu5FFfu3C21qxUllBsn"
    "d9Kio/wqV3JBmnEzK3ScTZNzvt/35/lyg/wBj6tHVmv5g7Lxwv1SiNWQQkIKBAIFylGgQKWy"
    "WsudnTPLGnmk6WRhJQko3O1R66pIOOGwK/vai7VC0rK23aqmcwsUdthbeb/HrTYbH/voGn0J"
    "B97cl6kklyT5mOcS6tnJmGqf5ye5f5DmAR78whWu7MCCPIlhf7zL2bAzVzndutvjfmb1Glra"
    "64d1VipcJYetUrmQnY57trQH+NJPexevXqf9jrITg29j90R6XuEKR5KCQqHPHrntk+Af1Ie9"
    "OOEn9zi1wPZvDK3QOOw78xwJHFhBrsBOJ5gBtb1hlfqnNDblG5NujvrNeV9oMxQ4ULJFFBJb"
    "WLjIFeFkLbLjHWPr+Trlm4MszaXLT30o+wMkVCjA1bl115s+HSisCgWbXvoM0PHAKE7OEk6W"
    "gkzNn1ejcSn//u9S5d/X+G9aqCvl+g2/aVpzpMUQkwAAAABJRU5ErkJggg==")

index.append('LA')
catalog['LA'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABZklE"
    "QVQokWWRvWqUYRCFz/vtZ4RFTUxcFdQUbpne1k5IoRaprERIY7edhAi5BgnY2go2snfgfZgi"
    "IvnBaDSJZL93zjljsRhWnGJ4DoeBgacI/4wB/4WLOMtFQNncBJA27JRAQQSZZEZcbEScjcft"
    "qPd0bfDg5PA4ZcuWaieGRKUIWaRIBudvL25h3G7fH91bHO6ro1NCV7U03z57NMjMd+P9nb1J"
    "ASoV9J0bl3fwpgEdQnUJlQldmvLq+d2VYX9l2N94sTw315zV7Fg6ls4FqA2qlTAlKjotXGmu"
    "9nuZmZnXr/VuLfTqeTDIICWADWjJ1ajKMA6+x8lvTg+OfsXuQUegY1YmIwG2qKQywlTaOD7n"
    "xtvd9Sc3AWx/2Dv8yaag0qKVBlwe4+H61ssfX78pJIrBOpn+IIWblCiJopeWB6OPr9v3+HTp"
    "yxCMjEgqGXDYkcnMSBIkJEv4rFWgnP4nlTM8rTjj/g9Gz0RszhHEXAAAAABJRU5ErkJggg==")

index.append('LB')
catalog['LB'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABUElE"
    "QVQokVVRvUoeARCcPQ/tRDu5iILPoOYt8gwJMVY+QXwAbUVrIXU6LaIhpLIIEpD4RUyhRJTE"
    "Dwsb8TPf7exOiuNOXZZhFmaW/bHfeIxssSFsebacQAlgan0VgDKRqQhFiFQQpOgiky4S9J8f"
    "dkoAKMu4/oukGAqq1Ymu2tNdrFV7UU0nUI5eXY1VFQBIkgC7ub/ZPtz+N3x48/JtNV4pU5Ik"
    "M4wuHRUoCpjBTGawQob98y/H/d73/tHe2b4rBbRpzCwys+0uQcf93ufTvduH29qHu72dX/0T"
    "dQFkY+jUKW0dbIZCCQ+vw9e+rrPTSyTLztCssfFq48/d9fLHpVC8m3+9MLMY6YaRZgoAdgq8"
    "eL8Slxdine5yz3ooOuuhkSLlFF3Bcnbu26fDkoAGA5uYBL1giG7uohfOpKO5coQYuh8AsB/P"
    "H5lPHtyVaBHAfxBTROajm4qVAAAAAElFTkSuQmCC")

index.append('LI')
catalog['LI'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABVklE"
    "QVQokWWMMWqUYRiE5//20xAli0ZYCLJCIgYsAkLK3MAjCN7CCwh6iTQpxUrSBbXwAIKFGJBY"
    "xEZRgmBgl+Sdmfe12KRQ4WGYgYcZMH2BBBqABgESYkHCCQgQEJdFHcLT59t3Vn/evfXtwfTL"
    "+6/rh9/Xj09WpVKWlHJKSaWcL5/tdzQA9XBzf6mfNcXO9GhjvPLk42My6aRMJZWkb0+WATUA"
    "WTj4tHX96nxpdCbr1YdtcqE6lEEHHUopgejIxhzeft56c7gp57mvUAibhogQ6CEECucG0Pr9"
    "04N78/GN2W+pLFu0JKVo0pJNSZY8uXbzNXb7u9neyq8xfhyXo6gii/F/pjhqG48w63Ng2dTC"
    "ViRZihRLTEWRpSixpJF1AnQAJRWZ5MUZo8i8+GYxSky72Qn0BEoaJmtNhJzkYCbZpBQhlZTp"
    "ZsNOYDgCBORl4u/5DwD+AH37YQGY0d3gAAAAAElFTkSuQmCC")

index.append('LT')
catalog['LT'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABIElE"
    "QVQokVWNMU6cUQyE518eQjShBgpOgOi4QSROxAWi3CNtijSp03ACRKJUVFAgBaVCK4E845kU"
    "b1mCZY0+22N74W9A2IYFAzDgDVuvLHiFAWF19AlA0oh30DsxomzTQpgI5tOvr2OsgAXRw5Ke"
    "PqTnGJlZMWMuu8cAxs81jkosJj1P2kw0TU55gmt3r64fMc5+4PLj8/16rZYsmjTZLJNd1cXm"
    "1JMPB1dfMPACudmUJYvNcrFJs7qq39bUwhoDQEeKyqWeH6q8WSsXJ4cMAYxvn3EaPt+Vqai6"
    "aE0bzdpoM9T+ib7fYJwDB3/VfxjSZKpStYFtR4x67PcFMApoKoeHIaGOGBIkJJCLBCndS7e6"
    "X4DlFhDgV/X7Ev/1DayAfyUid0PrCp4LAAAAAElFTkSuQmCC")

index.append('LU')
catalog['LU'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABGklE"
    "QVQokX2PMUqDURCE5yWvUETjD2IEQUERbKy9hZV4EgttAxaewnPoCSw8hpW/gagRw3s7MxZ/"
    "TBp1WIZvh2Vhkk9P8D4BAEASSiBmKKFSVKoKVCEsJ2PyiusRAEvJQtAWyET1yV5UkyYd1eR0"
    "dJsxK6jF7QtEUI4Aw6SDjuoajuqorjUNdwRknJ374Ahb26BsgjLpuYdJi44wicFA+8M0Ho+b"
    "pgFgG4bhv5RSats2d3W7aAG/CilJysuj///byZaULx83Lo49+TIF2pQph7zgro6EZtVX9/18"
    "96TDzf7zhysRcqUrHXSVYwFyCHvreHtABtRDb3cNYQdBgUJV6iDUJYm2BGSldPPpqRBCEYoQ"
    "QPlZpTmrc2AF3+HEeWs6fQpmAAAAAElFTkSuQmCC")

index.append('LV')
catalog['LV'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABGklE"
    "QVQokXWRMWpCURBF75OHQhRSC2rhXtJkKfYhhQtInTLbcBNi87dgCEkRSKGSgMydO5Pi/y+m"
    "yDBc7oOZN8Oc8gyc0UUA3ut1Wq8DoH4Dd+s1gIzIiJTCFfJ0D/cg5R5kuIt82WxqBYD8ef9I"
    "KaQgWxUZpIyihZnI8XweQD0DHsnrajO5h1E00WSUmcgheQLK5243WSxklpmISCDa3TI7E92j"
    "Doev222RVErJzMwEkP9HGQyOh0P9aprxbCYzdH9mXib0vh1VR6O33a48Aver1XG/D3eZBbuN"
    "dTFm7ZVul8unpqkBhHuS0aZZmAWZZklma5zpSglANSDpN9NpkOGS9509hHSX1B7dgfIAnHqQ"
    "dgXV/mJu8U+AX3wogUmU2HzTAAAAAElFTkSuQmCC")

index.append('LY')
catalog['LY'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAAA3ElE"
    "QVQokVVR223DQAxjAH+kE7RARul0QaboAP3IYpmhEB/qh2zHPgiCdEeKPOiCH5yOgADaYup6"
    "5wXA4/vRQOB03E6iSC21FCliKIvN5+9zAdDo19/LsduDGNBEhXQxvH3cVoV0HM88xwwVlYtN"
    "hjRpVkotFBYEhsfDoFdQyFRtdbloQkOI1WLkaIOyUjQrZGouHaOwQHBrbOzo8XAky2K4EmTT"
    "3D866rsIm/MqCxlC62T9ML5ChTIZuw1hgcDw8/rliJHylpJlSLYit91GcMH9tMg1T5FDOysH"
    "/gF/KJDzB2IFMQAAAABJRU5ErkJggg==")

index.append('MA')
catalog['MA'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAAA7ElE"
    "QVQokX1RMUoEQRCsORe5L5ywqWBiYuDj7lf3jENDQQMzzQ3uELuquwxmj90FsSmKYaaLrqlu"
    "xly1Pv+JZgD7PQBXoQqZViJlCZJJL/h0OAwAAPvjE5nINNl5QtAMR5hs41jAAMDlVXdESSYR"
    "YYaDXQCygKGAlgnJEiiL3+bzPUt8OMb1T5izAMAGALTwQL7fxHkbp228jpfuDuliKWXJDFAm"
    "b9/iy0zF3Qu9mNBmS6QjIPWHDePxiSaLCz+SJXXBVf/iFMuUiSPmGxFKZKILNhR2O5NQQgTZ"
    "SEjoLPUMkVlAO68X+c+Oe/0C+ctawBP+hnQAAAAASUVORK5CYII=")

index.append('MC')
catalog['MC'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAAAyUlE"
    "QVQokYWPS2oDQRBDXw/tC3hpmJP6Vr6BT+ELJGDwNqtutZRFD06GOIkoChUl1aeEL3jPX0YJ"
    "cD4DsbEZIxoMRUJK7/mWPy6XCkDy9s4YjJHeZ96i9fSW1tJ7WVdDBeLs1K1F2qS9pfVpoHdD"
    "NZQxkCLRFb2Y/TQAlcOBdU2tGZ6nbxf/eIDTyddreTwex+MxSQghv2NZltvtVm3PGv5Sz67t"
    "ansW/xoASduGp2eilMIOBbZutX2/3yVJsj0XPrkkY4ykZVmAT1MWYm5ocuzlAAAAAElFTkSu"
    "QmCC")

index.append('MD')
catalog['MD'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABW0lE"
    "QVQokT2Qu2pVYRSEZ++zTzTeQIIQIl664Bv4AsHCdGmigpa2AUtbGysfQHuxsbKLaLCxtROs"
    "BI1gJKJED8k/s2YszjbDx1SzZmB1WNzGsYT6tIYB+AUbPgUbH1ZhQBh9APDoySqAMmx0Sw+T"
    "4Cy71CQHfaZXthQxJVMfnz0fACDY/cGqVCX6gunBq5enXby5Pkt49JkhQw4rFxvQwyhjnlYF"
    "qb3dunpp1g759dvfpIXNIzLQQ3CgisqUE+68Xjz/tF1/M9vZXoibG91atRZRwICyFM1dTljV"
    "nbnRYup7HzdTJkP+P4DKJq2K6LhJ3cbbx6TvXNtKOKZJSwYGwJKpUC46aZu32I4elNrde39i"
    "pimiyajGBRYozytiZvh5e4N2S8f4pDl/a7nGBUleXppKYRmTFfjC5Bx7K/mNyYmFy5UqV6HK"
    "QAe8AHTM/vv77uFD2CPv1mCMAPgHcLpkVfGzF3MAAAAASUVORK5CYII=")

index.append('MG')
catalog['MG'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABAElE"
    "QVQokXWOPU5CARCE58lLiAlGEgoqGhsu5Dk8hVyIQ1h4DUQSMaGQhp3ZWYv3g8a42Uy2+GZm"
    "m9PphGFszxeL/v5nWwDz+RxAVQHAZlPHT6QaaSLdkCXVoOfttu3yehqo/R6HQ5H9BotREUU2"
    "q1XfcKWrIFVEST3KqGBnAGmgtX2lCxX6mz0aMBqqqus5TnmehcUizTBpRjFSnN7rZXyp+nw8"
    "PfL9K6IYk2AyHExGBs2HO75e0Noe46sqFRcGzV90UlZOhI8fL3U65g1KZsiUU05oaBg9y9ul"
    "0yzKoqmUSpmZlelEoNntdrZtS7K9fl7jDTBwAwgQEIAAAwJm+AYksGMfxpLrpwAAAABJRU5E"
    "rkJggg==")

index.append('MK')
catalog['MK'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABiElE"
    "QVQokT3JPUiWYRiG4fP1e9vCpqAiJQpC+gENnGyoraGfKYiWmqwxajGCoFGniIYmCQKpKcqt"
    "oqXBisihJSz74cOhwETy07qv+3muFgnO6Tix8SUMhvK/LuryFzagB79hFVZGWF6hZQJu3+Aq"
    "flyblUopzkJk02RnPPtCzvRW+UJ6SGunn7ZMwg57dInzhZnid6IUL8qt/FkOeTh8ObygZnqg"
    "vqIF/NPcK+wrPia2FT8Lz6eRCZ8IH5Anw2/EoCq0dZRmsPAnvZgspA/KR+RRucrL4V/hKXk9"
    "vEsMiRe0vAaK1+W15Hv6vUx4TLb8IdyTb4b3hvtFf9brtBzFF9Mf0z+CjfSQ3AnPyQ5/k9fD"
    "j2SHt6s5rHqHts7RjMhfg4H02fAX+UlYsuWX4ZPylfADMZ9ezYS2Qqc/PS4OydPy83CEh+Ut"
    "4Yfyffl4eEq8LcwWoK2n6JtIujt9VywVdgup2S/6kj1CyafkVuFa4UypAzS9HvUcdZYKFYAK"
    "dY5aqWObuLk6sMA/749Ehz/eU98AAAAASUVORK5CYII=")

index.append('MM')
catalog['MM'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABF0lE"
    "QVQokW2RsS5EURCGv3Ndi0i0KLZQUPEgHkWv3YfZnsYT6MQ7CMm2IuJirzv/nDOKw41FMplm"
    "/u+ffzIJbsABmIBBgRY6cVrgb7Xgs9nB7t7k5Hjz4yOur7u+Tykr+dmaeyOFe3z3t6urFlhf"
    "b44ON6bTSSn0/fZ8/owsdB9SmEIWZiGl6bRuoJSAiEgRpUSYleTlSyoLUwWQKtD0Aze3vYn3"
    "Zb64fJEaVMJs9B4B6n0xDHcPw+Pitet8+ZyTUaQVdQXcC7RPnG7l89wvSickpJAa68OG0fvr"
    "hhppB5KWYa+4/xz/UuMe7g5tgTX3+PYeA6xgLjyTMxVo5Ozvh4RnXEhJwp3a3cm5VoH0vvpI"
    "/vvuOAI+ATPsVhKSc7rOAAAAAElFTkSuQmCC")

index.append('MN')
catalog['MN'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABHklE"
    "QVQokV2RsWqVQRSEv/3vHxSxiCSFUdKIknfIO1iFQArfJPggvkZewN7WwsJKREEQQbwJyc7s"
    "Tor9b+7FwxR7DjNn5rAlbKvDil9wuOk63FzzrG+aDjPA5SWn5FMvt/19f/pPpbXYxV6hx3t+"
    "Fyl2pPXV1QxAcvSD340/7bvbX0dakNq7vqbWSOX4uMME5G3Yb1y0SDK1RorqoPXUukDqMHco"
    "HxtvnC9msm+X3UOWmgcpEsOB8xaUM+VQ2q570GxG9nJ0ivPamSuy1Ad1sNlxKNtIXXEFp9Za"
    "c3e3TcVwkLBjewhWzfkpHinWf3kWgYUbrTEE0wezPsosaC9eTk+EVGwkiqfiV7Q20KFc7/xi"
    "hwM+w/OdwfobJ+M16h6eGU4S17GA1gAAAABJRU5ErkJggg==")

index.append('MO')
catalog['MO'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABj0lE"
    "QVQokU2RMWtTcRTFz3v+CZQXaeoSGoliJlts1aVLqZP9CN0EQRRBQQSjgt38Burk7GqHdujk"
    "IkjBSRdXoWrqEEig5JH37j33XocY6Bl+nOXAgV+GN0CF/3GAc8q8zPqMCQkT7G7vAvBwD7cw"
    "0rKcACc1GapOuqpTTQ/eHyQkAPGnHFiYmXlokcxC77azd8flhHpai4SoabfZhSOHwz3MjW4e"
    "eq+TlypFuvS5vClORP20C6EIRU0hyAEYjEGaRvDD33Jc8/bF7f76C7GCLm9/T4QiJuqzAUE3"
    "NVXXO22strYeXL2/2lpBxLP1Jw9XXo0rvLyciwmNIM7hFjZ6G6N6JC5fx/L42qPN9mYjb5yU"
    "J9cv3DjfaC41Wq9/fNLQxcbi4MsggdBQMaFTTfpH/YW0ULOecpqyNKpGw+lQXek0J4gEgsbZ"
    "JTWdarVzZadTdNaW1g5/HX4bft/7+ZGhdKMbiIQK6lwultWUbqTuH++7O52VVXT2Wj0zszBz"
    "gyDDc+B0LlLmUqszpuWM/ib+Ab9BPtFV+EpyAAAAAElFTkSuQmCC")

index.append('MT')
catalog['MT'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAAA70lE"
    "QVQokW2RsW7CUAxFb17crfQLYGKhZUAMZeGv+DT+JlLVMjEEBqLQ5dnPt8NLQtL2yrI83HNt"
    "yUXTNHBHCBjJr9fn5dKBvyUi8iRSVdV6/YYiACABER4OZdsGVZqx7/fjUdzs4+uzPp9NdbPd"
    "kgTAlHg68XZjVGpkjFQtFgsHJIisVq/t/fvhJunOmH2RUTMAVQfE3RHC+25HEgTBTqo98wDQ"
    "Aehcw0By4s6AWbehc0zye2C0ocgn/R/vHHxDPM1sfNIEoE/iTWEJKWHYMPSsoiwxn2M2gxnM"
    "kFIuB4rL5WJm3qub6/plv//146wfNaZa7RkAoEoAAAAASUVORK5CYII=")

index.append('MX')
catalog['MX'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABcElE"
    "QVQokUWQQYvNcRiFz/83/7lNSHfuNIRmalaUBRsbuhuz4SNI8gUsfAULCxtla+EL2CsWYqGk"
    "xJQN6VIyZaKkG9f7nvcci/+UZ/HsTk+dDtcPovUYIL/dnTU0SQAkcbHY29oCQECAgB5ot67e"
    "ATqponKyuta6Btg2AGcu37gJWUVnfnhwvwcAY/fXXqn+ZNhG5//IMftsy5lLx48NBckqiSqK"
    "hm3P3j2TtHHqwpKtDEgiG5NAg1gWXazKou0v719+//T24b3bH988NuAIZSpSSQE9IkpiMYus"
    "sj0aH5m3wwc2T49PnLRkUiWRJgk00FRlMYuhtP309YuvK5PVs2eevHouWxHKNNNMAA0ExaEQ"
    "GYYvntvW/Aerts9fsl2RzlSkWQR6BIpDIaNoeTJev3L52v5JEc6wYZVL+4MUjx5ay8rf+bfr"
    "OgCDAXStLW9uALAspoAOU4DAAhDwEzuPdkZtRFKSpJjPd6fTBhAA0IB/+qNU8P1bz0wAAAAA"
    "SUVORK5CYII=")

index.append('MY')
catalog['MY'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABoklE"
    "QVQokU2RP2gUYRDF35dvveyZU3NiGj0METGNBCzsBLX0KquAVqK9GywMWFqlsBElIiJYpIg2"
    "hojYRFJd4Cr/gMihuTtDvIuaxj8k7jez8yx2o07x+M0UjzczDmhWy68X524BqF+89jOdAAyw"
    "Jk4P5wTof+qq5fufOh7AcmP8+PhaqZQ+fX7iQ3v/zM0Dgz4jQDPu1K9eL7p+dRE4v9Lcd3Ji"
    "pbJ7C5bVz/anXk5uXr4SI6UoJZgIQ/C1Qx/nH0cAANv4Ujl2ZKAS69a2JzMRDD+Yjb2SRhbi"
    "vD+aTLkhf+9r35j1Z+7UL00ugzr76NSrtwcflqbLSE3VRBiEKr5We7+w4IDG3dtD5868ILPk"
    "xgVRhmAifPZkpOwDyWIHwHn/vd12TeBwMp121yBiKlRhEAsSyw8XD1LERHLdNTr6bmnJSf+z"
    "27OXWQaiCJuHNiu8zUiCdFG0ubrqWsBIkmi3ayJUpYjlmUQYAkUsH2ZZaWzsTaPhtjudqFot"
    "bHauDrN/HwBoBhLAt1YrChu93+vrZmaqpvoXVNXMkPeagw0AfwC6FlrZi8M1hwAAAABJRU5E"
    "rkJggg==")

index.append('MZ')
catalog['MZ'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABhUlE"
    "QVQokU3GPWuTYRjF8XM3d2JtbenSQoRHLXQUOvsFBMFJ0E27dxMXUayOgopzcXByEHEJiC+f"
    "QDrVLtVJF4tpkqIxiel9rutcDvKA8Od3TnqyjlvXgFPAFBBgtf9XajNSAN+e3u2s48eSVOTh"
    "JneZhZnMRLqZSBmdne1OBnCuHxsvvz+/4DurTtLldFKkSGdRKV7orOYrZGQAoVhMvvner96/"
    "2WyfjYiIUKgeRYSknPP+6f08AT7P+epl+/PC2luP9pifkQc0kiylkDSykGR1pup86MwUoH0x"
    "bb1eWdiYizQ5Px5cGfV7o15v1Dv81/CwO+x2h93BeAAgT4FXb/zh9cnRu9nZI//ozW1DIQpB"
    "Q2GioViiNWgtAOkYaNzZjN2vmDc9uPdracEkRIQUkkKQFB5So3nyy97bDAB9iwZxiWjdXvxd"
    "QiXEWoZKBBGeTqytHXzKAmaWDTfaURFTRxDBFAQMIJIhGeAIx7GrjzRehh5DK9BPyACDBBlU"
    "INXfIAAGtPAXn2ZJkhleGGQAAAAASUVORK5CYII=")

index.append('NG')
catalog['NG'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABHklE"
    "QVQokXWRsU5VYRCEv3M54ZpAIrkNoaCh4UGIvQ9AbXwBKxore0tfg5fgIShotKEgkWBy8u/M"
    "rMWJSiBOsc3O5pvJTnyFBYCAuPtwd7R/lARI8rA8nH85RxAYMDPzxNW7q3WdzvHueLt5A93d"
    "wLa2l+8vRVVUrutv1zMz0N9//XBsOwmb/ivHtz9vR0alTg9PCTMhaceK5epXGh4jo1zlYjAD"
    "xmopUvTCnWR4DI9KVdYDobhcshQ1rwgaayRZiBnhSNHIkF8S/kXqZ4TqGh6yyqPT/ax0OsOj"
    "uhQ5fwiy1k71n9LqUlsxYmahopODk3JJnqYJWCewt9k7e3vmttuOGUx8gkcQLLBw8/lmt79b"
    "Py3p/un+4uMFAUBwyG/TfHYgwNw2twAAAABJRU5ErkJggg==")

index.append('NI')
catalog['NI'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABO0lE"
    "QVQokV1RsWpUARCcvXuB3CkcwYQIKmmMrU0ES/2CNPmItPcFgRT+gpWFpSCIVcBfSJO0NldZ"
    "KUEfd5683ZlJ8e7gzmFgYZllmZnA1OiwgoTCmkIJ3TaBBh0uzgBAthwUaJRMRmmYHJRcdMol"
    "f76cNyjA+NGaAoWSi0g5udJluaOTfjoJ/FXz6gTH+94bQQbVn5laUpUaF91/KPlgjK9vFCQj"
    "AoBtGIY9/5DzVspmPPKDc68REW3bDnq3q10/2V3NXnz/8sdMbwKQ1GyqsbqIt0c3fLZrbOnD"
    "lhQv3+f0dfxceO3YJUX+prXUhELRZZM4fBjvPv5qbr/p+slwduckOvaZIDXJcpa7PlO6iOeP"
    "gE9oUEoMHk9QdBIlJJGMIlIQkUYxZJcAKHC6wHxdZAn/8H+7feUpANjBPS8xYruwWMQjAAAA"
    "AElFTkSuQmCC")

index.append('NL')
catalog['NL'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAAA/UlE"
    "QVQokX2OzSqFYRSF16tPUueMJAnhElyByzKQCzAyNXQN5u7lJDE4RUeU3r1+DD4OCqvd6mm3"
    "9mq34Ev+yb9OC4DTcwCxYUEKDTEUyBRDpWr0l+urAQDA3D1AgpwiVCmmmKp0pnp6pXrb2zEw"
    "YPMou/tYm0AOCSlUyJAfMB6T2No00BaLxXQ6BZAEQZC/1FqbzWaD7TGdZAm/Cq2RHLwM/d+f"
    "tARAw+HFydnx7fy1aMqdKrmo4tJNuqSDrcnN+eWA++en1z5fvBVdcqd7qahOL5007cn6KvA4"
    "oLvk7Y31sYlyyaWx1aToSJYtjS/hFOhAB/gN+G1DwJ++8g7ZJGpzVKC8RwAAAABJRU5ErkJg"
    "gg==")

index.append('NO')
catalog['NO'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABWklE"
    "QVQokW2RsWpUYRSE57/32lhIltVik6yE2FiIpY3gS9hZiGAXTBGxkzyBgTxAGou0wpKnEAIq"
    "QuyMK2iIRbgQ0vjPnDMWdxcsMsVwisOcM3zlJxa6a9989vXq8MG8bRNIAMAw6D/vAIx23wKw"
    "vfditZQyfr3jECRLFk1ZTMrk99lRBwBw/voN4E8v25rPLYJM0qRZs1ZXttPp4gLCVgCITABW"
    "NeVKs5o1SdeaZEMK6NYjSinDSwrbXvkw83Uqpdy/vCzt08/7L9fOe0ZasjIVZqTCVCqSYcmM"
    "XB3dOHh1XCJiiDf85v3Zu+eTa+NtN03T93132ra3t7d0+mNldsSw7Ysnj7MySbBm5aIG1d3b"
    "PDn+1CVgyqRtRtrOv8NGNenKZDWZEVAI6BKw1Ewmtu/c6gC069MmmBTEpFopIxCREQDKyRLh"
    "Rt+PHn68+PLo23isJeMBMJYO4B/0nlqS8nC3FwAAAABJRU5ErkJggg==")

index.append('NP')
catalog['NP'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAYAAAB24g05AAAABHNCSVQICAgIfAhkiAAAATdJ"
    "REFUKJGVkMErg3Ecxj/vEGvZZi1pxEWtxYk4OCvJRUpOjv6M5eCyUu5ylByclDhZOCAHuSo1"
    "jNW7wstqel/vfo+bNjNtn9tzeD49369FFWOpS/PS2Q1IDzejbbTKeOpcDqhgWWZg5KTUTCdQ"
    "HSqmhA/E83nrdjcW6k/tl1sSGPNB1HGQ69IeDFq544lgYnjLb1rgV14pZ7MEwmECkQj5RILD"
    "u5W2vsFVNRJY1SE5tKajhzQGmJ8+4N1+pOzYSC4yHm+FDeu3oL06fFVsPCCeyZD1rklulnh7"
    "Xq8rNWSwb1HFdFru/b3cXE65uTmFe5cbzoe6H9hUHAd8H3yfjliMneI2oejMv5IfemOTugA9"
    "LS0pv7CgM9ApaA/U1T31p6TmvlDPrKQvkIdkkAzIIAESkvA+r2o63/P7kcPE9IErAAAAAElF"
    "TkSuQmCC")

index.append('NR')
catalog['NR'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABUUlE"
    "QVQokVWRMUtXARTFz9M3SBCiQfgHg3RyCFpahKQv0BC0Bq0ObU0trroJTuFnkD5E5Ozg5BAh"
    "JZQRgmLvvXvPOdfh9Zc8nOHC/V24nNNg8hbEP9kIgkQQCoBA/OceYAtia+cNAMsuSJJNWTZp"
    "SkmTSimpT9sfWgCoOvt9KZckalw7qVtHMqnlh/NAtDAkUEVZMumQSSc9pZVUUJkCosU9zs5w"
    "FlmNClUly5JkSeT4oGTRReBv++3j58W1FzmcU4TVQGWWWU6bZdpZps12bulkUe3Kq2fvdyff"
    "f86tP3l0eHx6eT1EKpMhZSookplK6vFk4cs+ZpDM9MbT1Xevn29vvpSbrmeX1Q/uw/2gbnAX"
    "7sJBAGiBIHV0cvbrz9XXH+d9P+Q0RJKUSFGSbWs86JOuBnsHhxdX3dKD+5STnqKmxBGXADTA"
    "+t06x0Zv58Jd3QAELXK+OQKNnAAAAABJRU5ErkJggg==")

index.append('NZ')
catalog['NZ'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAAB2ElE"
    "QVQokU2RPWtTYRiG75ycJD2HNM1Jk8YQa8FGMQGx/QEWwUmloOhoR3FwEFcdpIO7Y6GI2ILo"
    "IipiBRUsOuhQ6JdFB/tB09CYxMamyUne53ne1yEg3W4urmu6A5/vTL7bTz54tHw/tRq2rHvl"
    "IzeG9MVzJ688KQEtgAEG2oACFGAHTqdvzT2f4GS6vfYdfguZbCyVeLNQXVzfJ9LM+rxb/tDo"
    "b/hMbF4/nrRnrBfB1ULkby20Vzd+O+TF26TP+upVbdQnM+4Uc83fdT8+tZMczMYBtl66I5TL"
    "h4dzFEvoo0M9I2cOEpml0LEOG0XyrJb6VO+bKg1MeEUjAiirOnY5EI093XC2At4WuTP1QTuf"
    "r4djbSV3vbWkg9nKwDV384SpXYgUARU81fB2ndSX5T++31mv0sdvOyu7kuqRxTKPO9sFU5nb"
    "8xYabr9uvjXD2z/e27O/7NvBOMfwlXuZhPr4Z4VWSqLI3NwsKGIiViTTfva4I4CyACbSSjST"
    "EIlSukNGke56RFqRXI9uMImwBpQNMLOQEmLpSoqElKhuz/wwveTpZqe3My+j3aBFbDLpKJEw"
    "C7MmFmJhEmLNLNOhsUuR4rxOixhAAsBV4ODQlwrgQ/s/YUAD7j/4kDqsVjcKdgAAAABJRU5E"
    "rkJggg==")

index.append('OM')
catalog['OM'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABGklE"
    "QVQokXWNPUpDARCE58WngvjTCYIQiB7AVtBjeAA9g6RO52ls08YcwNzAJkUsTRrBndlZixf/"
    "UL9iZpadZZsJfnD2sgJsG539ogVwuVy+zmY75+fPo9HBwV4VgKpP+8ZisWgB5Gq1e3Hx9vQU"
    "8/mfvY6maST1AGwcHi7H463BwOR/7Q7bzQQ4HQ7f5vOSikqxSJMOFsMRJivC5Ha//zCdtgZK"
    "WWRJpiAiAiTIiqhOuyABaAVUqiQzTPXIYiDYRDRca3WZ7K0PSEdYqoibW0YGk/Q6hIOmUif7"
    "erxDC8CSSZNFRsZXLxlJOmTKqUx4/UGbR0dFWnm8Q5pMyqIpS6XMTGciITT3gAABBgRcXwEB"
    "CHgFDPhj7NbAO7zseXjgtt+GAAAAAElFTkSuQmCC")

index.append('PA')
catalog['PA'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABTUlE"
    "QVQokVWQvUrcURTE517/KQJxm4WFCGJhZcDWVhAsrO3zDLaxTC1i6UMEBXshPoGVbBOIhVoo"
    "BnfV4J6PGYvrLjocDlP8zmcZjUZ4JxIASaLfJ9AipoZAB6DX6wH4918k+vOQAMh2dvj8LHdF"
    "zPLdyUnXGt+M9es8nyb6vlYXekVS/r3k41jmdJOZ3OviYrQJkr7OY3UBT5PSaEmNo5vMWwHd"
    "31ZqWl+uEDTTaEw3msldZnSfcw+g+7bf/dyK20dlKqhIBIWI3Y2N6lYilKmI6l4Gg+7srLv+"
    "w5sHXT3IU0F5ylMe5dPuD31UrTW2tyuMQUw5eehlIk/OODs8tIODdirJCpvSKUt5KkWPNzqH"
    "Qz899eOjvLiQFBEdgkkNvpRIOBGpYIkspRQAcysrn/f2QGJpqf2mYPMeRrwQFjBOPYe/LSJI"
    "kgQZZCt4BT95XQotDMJeAAAAAElFTkSuQmCC")

index.append('PE')
catalog['PE'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAAAz0lE"
    "QVQokYWRzW2CMRBEn+HrAXHgAH3QCZ3QFY1QANdENJBcvLM7OfgLf0LJaA+WPE8e7zRzVwHX"
    "K1VA3bTdFtxmAjgeAVe1KlYrG3Czl7CwfTg4wpIjvk6nCQD745NMMj3sj7pc3Lsj2mYzv+Dy"
    "7I54ddvufQBEFEwFLRPJEqFX9zPA/AflSPkPIP1GSlly9L+Bdo8U4d6R3Pt7IGJk1gCW0ojo"
    "iPeAAiWZDGARYr12BMrRYGvtXuduN3ZIZkH7fmixgPN5LliaD/v9fAXADwI9YYS3Muu2AAAA"
    "AElFTkSuQmCC")

index.append('PH')
catalog['PH'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABcUlE"
    "QVQokWWRsWuTARTEL18/tfhZyVAzNUsVhLagiODYf6CLdnXt6tS9Q/8BoUJEBQcRK6QgEdrZ"
    "qRShOjlUsIouLomgJua9e+8cvipBj+O44XfTNXZ2OqurQ+AHACCBBAgQSMAAAjZZGtKNfv9R"
    "r3fm6GiQqQiRGZGkyKSnM+jpTPfo9bYao9HX6elWhHZ3v3S774dDi4C73NM93MI8zNI92u1q"
    "b+9uAZyXhkXxYWXl1Pr6Qrs9Yyb3cKOd0GFGM7onYEVmAp+BjtRZXJja2Li+tDRjFmNLsxiP"
    "60GYRT0oge/AJ+mjdBY4rqqrm5vb3a3Fw+cDWtDpFjTS2eKFV3hdApU0J10SWtBFjcZ6Mn9r"
    "+/FNM7lrIgufvwOUmZDmpNtAU++Ode+h3hzK/R8apEgCZeZPYFa6jBcv9eC+vg0muZNCBwMR"
    "AMrMa+o/w9O3OthHs4lzFdwb7iBRJ4mI2gmUp7tXfq0t55+T/779v2v9BjchVftPWcpLAAAA"
    "AElFTkSuQmCC")

index.append('PK')
catalog['PK'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABjUlE"
    "QVQokWVRzUpbYRScm3u9kCYRaRclTQQLmiLd2K0V6gP4COILuK3bZunaTelbKFkpuHNV6M/W"
    "RcXUxEqlVGKSJt+cn6+Lq1ToWRzOYmY4M5MMwgAAgBFHjfcNjAAHFFDAAQIK8P7IkAGYzWcB"
    "5GneftMey1hh5qqmClUTcVUTMRWXzodOVsjHGN29N+7dhltzi4iNSvP7zdnEJhTSKS7z1XkQ"
    "pQJdbHU1Nyq3X26vPX09CZMsyXZWdurlOpXiAkUJ9xMR1ZXK1fpqa6512DsKHpqV5vLj5c0X"
    "m1SKCYjM3Qv5GKOYiEhtplZOyz9GlzR+vv6y+2n3YnjBSI0KouR+hy4IjLz+8zNP863WFgMZ"
    "wkn/pDvsiouYQJHB/QGBdB52j1aevNp4vjEIgyGHC7WFg28Hx/1jc4Uic/x7iS40MrL98V1/"
    "2F9/tl7Nq3tf905/n05tqmZ3HoqUkiSpV+qVtCJRxKRz1tk/3ze3ICGdSRfnFi0aFMnVzZW7"
    "u/tUp0tvl/Drv4IfFv8IfwGHykCMTQsl5QAAAABJRU5ErkJggg==")

index.append('PL')
catalog['PL'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAAAuklE"
    "QVQokY2NMW5CQQxEn2FresSduQM3g+b3oKTZ8XpSbPLFD0TKaGzZ8hs5fL/zo1qrqqDeqQEc"
    "DoDtAMAGHPYedt7oY1ka35CfB79TRFRm+yc9T1XVCmKljf/kDVAVA+J89rI4k0yvlqx0ypIl"
    "Z8bpdL9cGuDbzder1dHkurvcu7Xp8XgUtIKQ3DuZG2hLz+c5A/tMS6zQayxFDsZgBnZKjkdL"
    "5CCFFBKZzJ7JGNMF8Qn1ZLbrrxPwBcJ/XXnBf5aSAAAAAElFTkSuQmCC")

index.append('PR')
catalog['PR'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABi0lE"
    "QVQokW2Rz0uUYRSFz/u9r+OPmcGNiZBaSASlYIjmvv6B/oAgglaCUNuGFq0V3LVt0cY2sxI1"
    "ClxMiouQoYU0LgqGmoWIheA38333vve4GDUXHp7F3Vx4DscBjQ94MIV2B8gBu4ICCuDiUCAB"
    "AnC3WTkaQm0q2e1xmWSRalSxGKnaxURMhKq71aoD+KaC5m/eLh2+qgyUh3ppJEASvEiSkITZ"
    "34ODBIAay/1oHA8vvi42/4SYBHgP73kJQADeWwgOiCsr7vEjbH3hXh1Hx3w2Vp04qZXlkJKd"
    "y4iYSGF0dGd1NQB4OIt791kcYO0rTPjux5OnsTGTffN5uyNieW55biJ9ZAsIAD5tMEaurzE9"
    "RTvni1sfZ0v/vE5TpVfEur1j7BkZGd/edoC8XHCNX1RFXz+Xljg2zuR/3/MAcM619vcDYB31"
    "IrxTbr1dLg3eLJKk0VRJ0oxA9905hzR1QHvxeTavG3P4XMBplqp1NS5lVKnRLCLGzXo9AD+H"
    "30/eAL4D6ZWB8+v2LgBnnkcsD9MsvJUAAAAASUVORK5CYII=")

index.append('PT')
catalog['PT'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABXklE"
    "QVQokW2RMWuTYRSFz/fmbVIrpUI62CGCgyLipP4Dndw6unTrIJ2cM3YpiL9A3ARnCwr+AUH0"
    "D+igS52iaYmJ6XfPufc6JKFLDw+XM9yHO9wGT4Eplgmgwt8t66VUTDDcHwKIiMjwxnHT4Wqk"
    "jlTIlHI1p8fHFQVAnpz+2uj4/b7f61O7rhG7b5mnTGPS0izJZjAIoAKISA9//tC3WnXfOOfM"
    "x5zuceOFJS2NCwFkAAUBT5d0NuP6e3UPDifPjuzLdtOjYNla2goSQIFB7nRWcD6ztlw5u367"
    "LWtRWq5ZkheCFECFwUMKfR3ZrUd+59Vw++qN+e43S+uPLduVQzZkABUCneb28Yf6dzl5cnLt"
    "z8/ud3Y+WZldbENKSYsLCtH5W3z5mb1N+/Da1GMZW5JLQYQc7gAq/oHSztYORbkriQErmZsC"
    "CQkS3BcE0OABMAbOAQPOgYLR38t/vMh/jtdGdgUCTU8AAAAASUVORK5CYII=")

index.append('PY')
catalog['PY'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABH0lE"
    "QVQokW1SO0oEQRSsnhkHNRHBQITdC3gMUwPxOG5gYGLoLUQQg8UbGIqBV/CDLKz/XdTpelUG"
    "4/7A4lFUP+oV3fRLxgxa1P9WMoBeD4AlSIgwA0GTIJ2z53jU71cAAPvhERGIcM4t/1WTnRs3"
    "jXNOnY6ACuOx6xoSANvwFJgdJNsoCg2H1ZvqtbJEWf5ZYN3d/VxeKOd6dx/d7iwgpWFTF+1L"
    "p6G2Pfo43X45HO3489XzAEgVkhbcNlaW96588H2G5ZUFvw0pPb9/rdZLIbUTmlwfVnjSkGVU"
    "ZfHwNEhYvz063rofZFKZIJVpUnkmTCno7mZ9fnJdoRHDG+tFsGCYUZCKSIySFMMRoqAwLQAJ"
    "5Q2CgIB5bgXnvpjtBvwCb+9W8SwiBKMAAAAASUVORK5CYII=")

index.append('QA')
catalog['QA'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABJElE"
    "QVQokX2QzyqFURTF1719GdxkYKSQl/AulJSMTJSBKKLIQClMjQwMmHgPI15BZpJ/6Za719rr"
    "GJx7P0ZWuzM4+7fXWft0Bv2+MZTh1anJ+y8KCOB7dBK/6gz6/bFeD0AppV5d7W5n0pmpTDIl"
    "kSlZ3Lq+bap9S98c7r88PaYkSowkRYohcnJ6BkD3L217Ye9AqkSIJEMMRjDCIoCmDVfHrna2"
    "klSEqNZbESKTAtC1Xek6sHR0XNviQBGMIc0IkQAa22WIl+JyublBhuI3ukgFTaYIoOvWvqCU"
    "snJypggxFLU4epCZAtBgFKlucrG+pmijU6REk0qlDKDz+f7aG59odyilnC4vpijVv8+UnMpM"
    "Z57fPTS2P97ebNuWNT8794z/9ANrfVvlnss+EwAAAABJRU5ErkJggg==")

index.append('RO')
catalog['RO'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABFElE"
    "QVQokV2RMUpdYRCFv3vzP0l6myAW2roAF5LCJgsJ2GVJD1IG7FyEnQgWgRiCqG/mzJwU9waf"
    "Dqf95nxwJs6u+SsAZhgPV+cfD6DpppvxQp/RvGbwW5ffT4DuqT1/Ovq22exAtkB+TH+VMy05"
    "83G7HQwwd7+y2tVz9y0827lG6ZtwhDOn4+OGAbSptsoq27LDlp12uMORC0Bmw0BdbVVLqGzH"
    "63uH/QYABtEqp/wfyD0m7HSsSki9AFUtOWQVe0DYua80rUrRWYRacopV411DJpIlwWAnqTM7"
    "5axpD1ga0hFWoqIKGKCUPx9uUlZ9mOcjeJqmBEEyxKmoWtIwwQ8I0LLj/c8vY9CioUX/oS/W"
    "jZf7Byetb52vN2aSAAAAAElFTkSuQmCC")

index.append('RS')
catalog['RS'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABEAAAALCAYAAACZIGYHAAAABHNCSVQICAgIfAhkiAAAASdJ"
    "REFUKJGdkb1Kw2AUht8kX0OamDRFq8FBDUVRyeLg4DWIo5OLgjfhZYiDm65ehZPwIUKnBhws"
    "VSzaFAtJJTZpk3wuGjSlTfUZ38N5OD/cyeo6M2UF/6X54YOYsgJLK/0qhCTBkESYCcSpRCQb"
    "POhAuNIDX+QgNCSsdZJ8yVWk1meH+vt3sLtUNefL3TI4wDX03nmn1QyGUTxO0I0SlVwbOxbm"
    "qml4YLrwXj0w8NDMhcpdf6Ny73Pjx3hrgM9mvq6DC0UocREDYzF3FQCjEt5powBAlCTELy0E"
    "yYQpviD7m2LdWJbSm/DPtW1fkEmfCVAeabJnHd9OErSfRBWUUvaTgeux2sUluzk9Y4HjsDwo"
    "pWzkxYWShq2jw6luka5j2/afGrLYto1PPjGOVBw0sOsAAAAASUVORK5CYII=")

index.append('RU')
catalog['RU'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAAA8klE"
    "QVQokW2PPU7CURDEfw/+FXY0hljQ2Fh4DRNuQss9PIVXoOAuFhYUho6YSEzezNu1eEQ+ZLLZ"
    "nd3Zj2w5HJITIqKHzuIKDgZgMgHITChAZs8KjDNHeYbd7nvomzPznOQtlFIioiwWuVzmfk8E"
    "rWVrRGRraV94O6fTslp9DZtNPD+X7RY7LeSUUspar/18DjAANlL+DXS591Wl6rFiF4gB6Eer"
    "0uLU+u+ClBDDmvGTXn/qZ9hZHXJUhRzSkVQ3Kdzu/LDmbXiBsd9DH7eXd2LhVtrjPQwBI5nZ"
    "LCXcsJCK1D/Dxqa1bgHlAHFmXKZXEvALP4llHm/RUYYAAAAASUVORK5CYII=")

index.append('RW')
catalog['RW'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABRUlE"
    "QVQokW2MMYqUURCE673/zYKyKshssJHgZQQzgz2BCAZiZCKCgeCaGegBzPQQIngNMRFBHFc3"
    "GXUcu6urDH4GdsGmKaqgvmqXnjqE+SgVAQEEKEgIgULsjDBCeH4DAGyXmwwJJZdbaUp1i0fL"
    "F83x+uu945ca8/bJb5chgXIVaLNMeZu+c/hkPz50bY6WcYz7XYDlEkqgzELaWWY5y5TfrO7+"
    "Yf8WB29PbyE0MkS3KJQ8MyVXebtjvsTBo4+vKC/3J6zX7edfLKaH1meDdtq0w0447ZRjjjZb"
    "v7b6/m5cHAA27r+MgGcm/qu9X728hyGhtbQD4PnSuTZAmyTG4jEe3OSndaYyK7MiFLPJSjqz"
    "gpWpun6l3j/DwAlOt7wwDody0Yo99yqzJztzShY5sVRV9WNTWKHhNrAF4szzjAagXRQw8A/4"
    "uWjB1/pT4AAAAABJRU5ErkJggg==")

index.append('SA')
catalog['SA'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABc0lE"
    "QVQokX2RvWpUcRTE535ks5vAInGLgDFGQmqtVRRsfYtUfjR5BH2G1IKd0bxAKpsQLFQEhVsI"
    "G7tV2BXW5CZ77zkz529horFximGaHwMzGR7grwIgQMDO3YDmQmhQAnj28GkCEoKiQhHBIEVK"
    "DHe6Qgq5/PWj3RJAAkY/RxGRIwfSaXtqtLmy47SZNQnppK3r5nhl6SqAEoGUUsvm1vU765c3"
    "jpujd1/f31y90ck7RZYfDA/ubtz7PPr04u1zp58BDFJy+tFs2p3rrVy60it6h+PDlu10Nv1R"
    "TwoUJrocQA6DQpQPFgdrg7VxPamtrr5X3c589a3qFgvD8XCv2pNI8axBwZSw+/FVljIGTWZu"
    "JBu2eZZPTiakp0jngIGiy83bzdubC+ViSunPVv35vsuf7Dxu1TIuAnQGt99sMxQKkzndZFv3"
    "t/a/7Lv899AASjTw4HJ/meFOUu5yih5OcefDS1GrS9cYjAgAGdb/ORIt/q9fMHo3F1zEQB0A"
    "AAAASUVORK5CYII=")

index.append('SB')
catalog['SB'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABz0lE"
    "QVQokYWNQUiTYRzGn7Xv+0gl3MKCCS0LVgdDSQyExU4WEVGHygiCWF285KkI8VKwaJcOUQQd"
    "WjQIpnjx0mjhoGIOYY7ARtGhSQh90Sq3nM73+b/v12F17nd4Ts/ze3x+/1Ii0Ts5+XFq6kAy"
    "6WrdAAwAQADzLzuB1fsX4kOjsFKp8NjYTtvGxMTugYHtpVKThNZGxBMxQh/FOhldiA298zqu"
    "Jh4/8QFvU6mDw8Nd6fRP11WtltHaIw3pkbYfmzcvPz/UX1xZt2+X7Oz1rAVY9boJBv2iTXf3"
    "tmZTlPJEfFTO6dibc8fm0VnLVnF38Ut4VxiAFY0Gxsd74leqT1N9s7NrmUyNdGBaNy5NHx4s"
    "fGjItRfulih6pCEAq1Co5fMhKszM/Jqba5AdJ0YWzh9/1XSquVV/ctGlUcooCkUEgAWoXG5N"
    "xMtM/x6MfD51tBjZt1Td2LpX2KjU1pWnqKlE0ZCa7YEhoZQTP/Oyv2/ZBMqvv+24U3Rp2Ba3"
    "2yKi9d8Ha3SkEowtR/YWv0OevXfyK1/pUem2mxQlhqK1GA3AunjkVihwtnd/5VM99Kj8Qzx7"
    "T08XNUULhWJEjOg2RgPwFR/AdfCwjPk0sIn/8gcg3yvgjsU2IAAAAABJRU5ErkJggg==")

index.append('SD')
catalog['SD'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABOUlE"
    "QVQokZWPsUpCcRSHf957CVP0Sks4uPkALY1Ba20+QS9QW0NEUwVBDbX2BC1NQmtQb+ATOOiQ"
    "3SI1Rfyf3zmnQTODlj4+vukHh4PqHhxzdUkBpsAEGAOfwAD4AN6BHK5xs3G5/ziIaDCDqlOh"
    "dBKki/hSR81mAiKr9E82J+f3WSTqIlB1kblBXIKH4CK5Ws2ABIQF71ayw0Z6tX0Wec7d/Adb"
    "BHFs7XYEQF0Z2C31Dp6OOytjL5eXTL2cepp6mnqp9JLPRyBoKioi0kk6Jw9H+uvCEoCZRSDU"
    "SGOwsK7rpzunOcffe3czS0DM/iq+FS92L6Jp1J/0zczdZ10Qx/FwOExgoLPQK2R3WeO2EUII"
    "IYjIvCISAimk1uv1VquVYIT862rhuVBdq7KspMwgOStJ/QYAsIV/8QVvRk9166paHAAAAABJ"
    "RU5ErkJggg==")

index.append('SE')
catalog['SE'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABTElE"
    "QVQokU2RP2rUcRTE57v+NEVEQVEIEgRzAEsrKwuxEe9gERC2Mm4jKBaWNtp6hTSCpUUO4BkC"
    "IZ2KqLBs3ps/FmZNhscwxXxeMwMP3mAtvXoNA8aFxQuUUYUiVnUWuiYAL+ePANgZO0tYgeZ7"
    "j6lQptwS5bZb+jT/MAEAcvxzKQcnR0kjOvz2p+WmWiqq5G5tX98EaoJtQw5PLv5rxzWN1ZKz"
    "kpsqqqi2WgZq6ADjzgJ1lBBh3HEnHXdccZ2G9Ni4/fvLwQQCUdLxGkidYmftihumiQlGwphJ"
    "weeBc56Ke7hNDNx9/mzx5PD7L8qfH75LOq57+3tNltyrWdEtk9q5eeXr2/cTVqTVUtPrZ1Vk"
    "US0XqiEOc2bONoDjCVVNb13dbAqXbiEN9/a1y5SbpkVHsmTZgAe2nv4f8sfHfRAmbuzeBwjU"
    "+gwQMIC/3Ll5iJVVWFoAAAAASUVORK5CYII=")

index.append('SG')
catalog['SG'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABJ0lE"
    "QVQokY2SPUpDURCFz73vQjQknY34k9IiriArcD3au5asIVWKYNIpaRRMHwKCiCikkTgz7xyL"
    "p4KK4sd0881p5qQHgEDZ3oZZ1DUBAgD4YwIAUAjszOfV8bHNZry9zf0+1+u4ulKEIuDOCLkr"
    "Qu53o1EBUPX7fnkZk0k5OdkMh/nwMO3t+/RC7jKXG83kXh0cBFAAqK6rXm9zf8/xeOvsTBEv"
    "5+cyp5vcZC4zudOdQHUKtAaDfHQEKa5vfDZ7HY+bbLo1qszonrvdx9WqFKBeLGw6FRI6HUQr"
    "t9uMgHt2b4IRkTzS7m4Bkq/XudORJAiCfifn/LRcFgJJkgT8ZTfbAArJCvjPAQBEFJKfNj5I"
    "KeELCVDzzQJy8/zMd4LBb4AMEmTTgDfsYE4MubWOHgAAAABJRU5ErkJggg==")

index.append('SI')
catalog['SI'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABRklE"
    "QVQokW2MsWpUYRCFz3/vD8oWgaBgFjux1EotrXwFCxEfQ7BTLAJ2Kgoi2KhFejtbfQMbG7GJ"
    "RViFhM29uvznzBmLu4ZV/Bi+OTMDU8ZxxAa2J2+GE2RXALPZLBOrVaDk7HQPAMjMXLcNhmGo"
    "0+PHz/cPFoe9Y/fhZXRd/o9Siu3Sb3988ujC8bFu3zzz6u1iGMIJyaKplEyZtOSdc6dePv1Q"
    "43DY/7a6f+1LbF2/d+nzrdc7RE+50WxuNOnWTMbRksCPCliJN3uLs+/2xGCeb5lUUtmUbQqR"
    "FBQAXIEa7t73V9uRRTOTdFPXAgy0KGsbdAfUuos7N8YHy+WBpWgy/5iMppDcZCkYW9vzZ3hR"
    "7+J7/+uTf34FmWS2lq2tw8lGhKKsLl4BqoGOwnyeJBQQQRYSEiZLiJjKQBkBbxT+Hv85AfgN"
    "XohcMYYB/8YAAAAASUVORK5CYII=")

index.append('SK')
catalog['SK'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABgElE"
    "QVQokWWOv0uVcRTGn+97X296jSxEXByi/yIhIlFq6A+owaBwyhwa6kLR1NbUINRUQ6ijRDY0"
    "iAVN/djMKMJFb/Tbe015r/ec8zwNr0jWh8PheXg4DyfFdgEQe5AEQJBkabiHk8gBZLUaAEnl"
    "SSYASlIF0H62vv3Iy/ri4SNsbkmUB8Duq1P6j5SSu+cjE+tTFwZOr3zs3Wjo5i3Mzca799MP"
    "Gm1WwhlBD7nTg/2H8/qVlfzlq+bYSP9Yu63Lk+g7pPHxuH7j53rrQ7Mb7ua0kBs7zqHBKuJL"
    "Fg5RDOr+tFqbmpvJMv3uZE5ZwANuMocHggCQA1WJa+cmFxY+N+4V2Dlz7fyJr897LBiUMZky"
    "F51pRxWgmpaAY/U7XX214uTo7cdd9bNeW3zaevGa5jSj2e5P5geODi0tzuTDQGXjE9+u4tfq"
    "3eOn0rM31SfzA2bqdGSmUrjBIx1sHgFSAeQXL8lNZvDYFWZyV7ndFYEIRHxfXk7bAP8a7Lf/"
    "RAD+AEvfR7lINIfTAAAAAElFTkSuQmCC")

index.append('SN')
catalog['SN'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABK0lE"
    "QVQokVWRMWqWYRCEnzf5QUEEGzERYpEulZ12dhZ2HsBCsPQIqXIDbyKk8SJ2goVoJdgYFXdm"
    "dyy+X4PDtM/ssLO44FqDXnJwAMNsNnPGcO0dcP7sHJjMZNaDhgavXz4cH9xUXjhS7EhXl5c7"
    "APL5x5dO9zQRqxPlhhJllA+Vqkjr5GRgxzCTnnbao9CkEne0UkmltAFIe6Bpx257nOhKevtR"
    "v1vPT+vO4X8AG+BptTz2mOj9t3r3Sd+l+7fr6T2l9pWwB3aYHntcU24nenS3Hh/pZ9WTI2Wu"
    "L6x9JaOoujxWVxCp12dK1KmM/sXH9ga4rZZGaiVFKlFSK0qUqli46QZ2FBof3zpWy9tb0VoC"
    "g1jm1HRvHli8ggJDQfH1DfB35mGKebjfeNMf/ixbkUHqougAAAAASUVORK5CYII=")

index.append('SV')
catalog['SV'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABPElE"
    "QVQokVWRv2qUURDFz3U/99P0EVaICImkSCEBU/peopAHSJM2PolFioAQQhJSbyE2FiGoCC6G"
    "yL0zv2Nxs0schuEwzL9zpuj1XFX3BqooUJUqqqGKGhJS9Dio6vDDs14MAiKVOCFTEY50BB0c"
    "7c8HQaKbnwHOdKQjnekWjqCFW7gFrTFbfyzVcnJ5u7kxrRVbtukORrbAYAx4nD76fP69ZGYp"
    "RZJtWZZt//qyf8v4fOudH1gpZbFYDJ1tT3VwNb87//ojaG/u/uztrK0aVAowrKpX83e3p4v2"
    "Nsm9naf/bbCBcnz2+9WL8W8FbLsfHU6QLHBngP1knByfXheNl+8PNr7d1KUm1HCmW6O17CpF"
    "EumXs+mnjxeDwNZsfWjhDCId4ZZEEjHJvNcanEiiaHKq1OqRyxhLzAOApH+kWGxNzYWtjQAA"
    "AABJRU5ErkJggg==")

index.append('SY')
catalog['SY'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABDElE"
    "QVQokZ1OPUsDQRScDdeG1ClSqIe/R0UQ0Tp/Ij8kpWClhxBIGUF/wxVWOU4iEW0i3BEM2Xn7"
    "nsUmp0KwcHgM8z5md5zhG/pb7yxnAAYDAKYKVYRgEhDERCBipP3g5XicAADM5q8IASEYGXlT"
    "nkZv3hvpej0Fkrqq2u02Nj6LvBNw7mM2azWhzWyl66y8WxnjhbdwNb2uZdl4VLWlqs2ro+fR"
    "4/whm97ENitu718mw6fh9guISKJNBtjJ3lG9rk4PjuPgfP/s/fPt8vAits4MgFssFp1O5+/0"
    "ceucK4rCAej3+2VZkiTpvffeR0EyKhGKhDRN8zxPAJDS7XZJigSRaKSIRBaRsAX+gS89B00d"
    "BrzDKwAAAABJRU5ErkJggg==")

index.append('SZ')
catalog['SZ'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABxklE"
    "QVQokSXRTUuUYRQG4Pt955nJmqIsQkqaTQ3SoqDAZRCmNgW2axO0rgja6KLCQZAKatN3qwrU"
    "RVFEURFE0spdJPRlgUGBo4uQ8GvGd85zzn1adP2FKzl+/UMjUwAASCohpCpVKKqizFRFVJSZ"
    "aEtIA8PS6UoZABx0p7u7m9HUCNBhpNGNNLJ/dCKMFnoL+Zu++sdJuDnNqU5zVzC6qTO6RVLT"
    "tW094W64dcp6Lswuz8y4WfD4u2lvHFFlbOThyrcvn+/cWPw1a1GyZmwtle6PIyUQzZBouWvd"
    "5oPF16QmabMpU9M/Wzp27xwc6jixvXPkwQ+RGOMCkCqQwEqVbaubtg6/mu/rO3rsSHeMsV6v"
    "D5yvvhh/X+w8O/3y4uEz+0ORAgQFgHQiOyS51m6ZfPzoiZplWRZCqA4N1iY/fRyofj9wsrGh"
    "vHfhsgIhBUxty/N76lgjzUqM70QWYswbk6mvc9eu/K3NtY1d0jSXtLcDSBavotA1zOWaW3SP"
    "MCOjWQTNTUAh1U3dGDbuqN1+FnobT8/JHuZdcySpOXc6SZHIHOhudKPT6eLVbFeyr//t/FL2"
    "PzITFaGrQhSioEIIIVRBQon14R8k7mFOvA7V6wAAAABJRU5ErkJggg==")

index.append('TH')
catalog['TH'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABDklE"
    "QVQokX1RsUpDURTLtQ+KVB2qi1MXdXJx0N3BjxEcqz/gz3QVuklxL35F9zrUCvaeJMfhQWuh"
    "GkIIZwjhpCQ28LbfyZIAnp8BpAUnyHSCkRLIJDMiI5JMcjkalcwE0Ora5C6UUhaLRbm9e3m4"
    "v/z4WMlppWzRVIqiLFoyw7SP+/tPj+Pm7fX9+upkNvskzXDQEYpQrY5QrVrrYHAEjBvAbU8y"
    "GRnMWtvartU1MqrbCwnADfAtkYwaZmgd/zu7VkU4ogLLpnd4cXbePzjskkkqaNIMkQ6aFGnS"
    "kk9Pe9PpTVmtVp1Ox/Zfz2kBoJQyn88bd7t7wyHajhKCEEsEyA1tSJA8mZSv7SH/2bjFD1//"
    "ZYwkMQleAAAAAElFTkSuQmCC")

index.append('TJ')
catalog['TJ'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABO0lE"
    "QVQokY2PvU5bURCE51ybIEuWDBXix0kKXPEE9DwLBQUdEpKDkoYiVXgWXiBFmrT0kS1HuKHC"
    "hRE6M7tDceGGMp9Wq1lpNNopP/GPBBIAIEBvZ6db0QcwmU4BONOZiLAiQ5YsmUzJpKUkf93e"
    "9gEArvdLRyAiyYwwmaTJrExW15rkh/FYQFmv14PBoH3JNoD651szPDJ6sfrd+zx1Gdi2XUpZ"
    "LpdNV8A2DC5umq1jx5P12Ds4i783foekJjNbt20ufvTG52X7xJZT3tgrHy9j9vXVDmRmWa1W"
    "w+HQbb5hOxbf/TRzypv75dNVF980zXw+73fxnSjjC9jtdHQNCyY4/XI6e5hRZLCqVlWKNSrF"
    "KjKqgoo43Dm8u77r4xkM7Y52GVSEggxSVKrdCkVGREQGAGAHGAGb+E9eACEOOiSM812rAAAA"
    "AElFTkSuQmCC")

index.append('TM')
catalog['TM'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABj0lE"
    "QVQokYWOP2tTcRiFTy733tyk9s8krThoFwdxKEjFyVFwlI4uIvoFghSku6M6FvoNnARB0MXB"
    "qbQOTQsabdJgNFiqJiY1ue857+86JLj6DGc7PE8J1wHgzc+l3sfuELi7Aggzq1dO39ZhwBjT"
    "zTEhBrBRe7LczYtQTheqD9PeTLnya9i7XdvabR3sdxpyMZByOl/UNiMAKPD76GuUt4933i9U"
    "zuy1P8xXZx+/3FxbvZnLPn0/anTbjW9NugBECAhFSNKi3x2lSdhpHdy7sbZ9uLd+60FUiipJ"
    "2USjmZMigBgBCj4e8HSUZ6VCjtf1d0/vbND9+far1knHSBMpUgIQwxCCJ4n/GYbZajCp3mnc"
    "33p0aXG5efLluP+DookmTZJiCAo+Glgz41yfFMzNpN32PklzUjKRkpxTg+Rp4pkxqxYmmwSY"
    "i5oeKNLlwQFEGINBcyvXrqbILly2aQBNZjKjJj1yyf2fQYPDRnz2ovW655YWJdFJp1zyIJcH"
    "d3cFAShhHhjjWY7zwGdgHf/hLwvQQSbnuK52AAAAAElFTkSuQmCC")

index.append('TN')
catalog['TN'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABN0lE"
    "QVQokX2RsUpDYQyFz61dBUEoFFsHp452cfIB1KFbp4Kzo25C36GrTr6C4uAktljhLrp07aq7"
    "UF1ukv9z+ItaEEMI4ZCPhJwC/URa7f/MAknDoSRSUkqKwEPhuMsdM37Vj9vbuiQJXt8UoQhO"
    "TrS9zcYGwHRKWTIeU1WYFe12kuqSSCynj4+1WHB5yXxOu02vR2uL7i7TJ8xklqRakhSRtysl"
    "Hh/pdLi4YHOTsuTgEHOqKi+RVJMkD8wwI4L5nKMjRiMmE7pd1tcxWwLuKQOE445VpESrxc01"
    "p6cMBpQl7+/fwM9JWZI57uzv8/xCv894zN4es1n+j9xxd6mepLUsmTEacX7O2RmNBsDdHff3"
    "PDzgJg9FKAM1czWbmMlDV1cyK8zkrlx3dvIPFZGk4nPVyH88zvEFZ5M7foT/uzgAAAAASUVO"
    "RK5CYII=")

index.append('TO')
catalog['TO'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAAA9klE"
    "QVQokXVSQW4CMRBzlj1Wizhy4MiN5/ANpLZ8gfYX9AEceQ8S7RNWooeOnbiHLGhXaiPLUhTb"
    "cTRJfd9jtEoBULrFogB/ogXQdR0uF59O2G6xXtvAbpekmdSQlnzn2/nc1mAfDsgZb+/+OCIl"
    "X68mTTpohiNMptWqAA0A29jv0bZ+ea7bQRph/jhiADlUAoDNxscjDMO2HTHOrgwSQPs0n+N1"
    "769PS6Cs4fihe8RDGm5wliUzQJk1mOPs4Q21UgES6QhIE9FUDcmSqmEm1YrjAhObCGXkjGpo"
    "KCyXJqEMEWQiIaGyhJwrCpC+p4PEPwMu96/wC/4dTt9th7WGAAAAAElFTkSuQmCC")

index.append('TR')
catalog['TR'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABOUlE"
    "QVQokX2RPUpDURCFz4uxUwOxsrAQwcbaLCBoOhcgdi7BBVgHN2CvRSwsJFWqlIFg5xJiVqB5"
    "oHdm7mdxH2hAPAzDFPMxP6dCP8rr9Z9RIenmRhHkrJwVgYfCcZc7ZvzKq/G4LUlVxXKpCEUw"
    "GChnJhPe3zEjGZZICbNqfz9LbUnk3HRfXurkBDMODxkOSQlLJCuAzLLUypIi5M7Bgfp97u64"
    "uuL5mbpmNGJrGzNSM0RSS5I+vzDj9IwIZjNS4uWFx0e6XZ6e6PUawD0XgHDcWb4hcXyMJY6O"
    "eH1lOGSxYDotQFmpnaXKjJT08MDODtfX1DXAxQWrFff3zTLuuHsBNtzLTdzecn7O5iajER8f"
    "mDUHuMlDESpAC7S3h5k8NJ/LrNrdVacjM7nLvfxQEVmq6nUj//G46BtdQD5PmN5peQAAAABJ"
    "RU5ErkJggg==")

index.append('TT')
catalog['TT'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAAB0UlE"
    "QVQokU2Lv0sbYRyHP5fDJZ5kDBmyBFzbimBLoA2lBYO0HToINzgoFRGU0C4Oaav/gf9CV1c3"
    "iTg4abgm9jh7JO8ld8lLIiS9q43mh/cm77dTSz48PPAMH0y63UQiAQBAPB6ftFoTXZ8AAngA"
    "hkAfuAP+AL8BH1Bofp6urr4dHTHGpJSpVOqDruPsjE5OMB6TEDTl++Nj9SAIlEzmSTb73TSr"
    "1SpjzHacF6urdHtL5+fU/UXdLnU61OkomjZyHPUAoGQSvbu0rtu1Wrvd5pxbjL3c2CApqVyi"
    "UFAYkhCYmxvV6+pXQEmncXkJoudra9eMcc4552al8npri6Qkw/h/eGg01H1AefqMgoAsC0SZ"
    "9XWzUmk2m41Go/TTzu7ukpRULJIQiMVGnKv7AJaWKAhIhPhhgujV5mbZtj3P8zzPsKw3nz6S"
    "JLq4UGKxYaulfgGwuEi+j/GYwpDKZSJa3t4u2bbruq7rGtb1u709AKhW729u1M9AZGGBfB9C"
    "kBAUhmQYBKzkcoZl1er1muMUTfN9Po9IZFgoqHkg8ugxZqMUjWJWg6ZB0xTHgaK8zeXavj8Y"
    "DHq9nsnY8s5O//BQ6QNyCkznzIw8PU1mMvg3Vij8BZ4ENaDZLG35AAAAAElFTkSuQmCC")

index.append('TW')
catalog['TW'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABEUlE"
    "QVQokW2RsU0DURBE585nOUUCCRw4JiGiBWqwKAMKgIwuoApTAhRgUYB1BIYEgUC2bP7Mzif4"
    "YHyC1WiSnbcaaSsc3mIzMmQkY63Ht9MDwH/UALi6PAJwvDvo1fX9fBFGROxMz3ta1mSW8o8v"
    "JpOmnN5b+mS83+9X7fXi7mUlWbPW/MiJmSmnlMlqNDLQwLDNFEAmvVqJYcnfUaacWACQBhpI"
    "EZiueXEzk/30/qkwbW/d3gAAGiTZDsXzqyQzckRIHnDxmy6AZKCJ+RgPZ7ltMxOoTG7X2Paq"
    "VDJQkTklSJ1QNw0pSypATyoVtwt0MBEKRKAANYXhMJNQQARZkZBQXEJEkYFq2X0k/vvuZgXg"
    "C0lTZuDB8+IlAAAAAElFTkSuQmCC")

index.append('UA')
catalog['UA'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAAA90lE"
    "QVQokW2MQUpDUQxFz/t9CIrDTpQK4mZcjgtw7CLchmsQhO7AmYJzHVjQl+ReB/2tFgzhcBNO"
    "0rgz+5JIECSk5h47DgEdcXsNIFtuZVKUnG6pRWqKcsohR/nh/rMzAbxtXKZEyFWEHTVLozzk"
    "KF+cNr7UEZJLpEm5imFnOXbeKI9ylKMAdYbKLUXKuf/9x54ph+CDznK9PL4c9V1ySiFSCink"
    "KKUUNefVyRHnj61eYXVjv5iB0w57/Mtpunp/WneJ5rAH5KF0YEPamdAlFqQdsJf+OYOCIukS"
    "kxPOdtuAaC0gYcucbUqibZ6RUM5kn7eB3xEB/ABIh27o+OhYYQAAAABJRU5ErkJggg==")

index.append('US')
catalog['US'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABuUlE"
    "QVQokVXRvWtTYRgF8PPWBBdRKAgWqcHF1U2koMVN7aCDINVFvGoGJzGLCuLQP0Ckg4jQVoQ6"
    "dHBoQUFjLRksfixSDIUiJC3xJiG5uXnz3vfzeRykoZ7pBwfOckRUrGAYIpAHLMCAfVH4wGrQ"
    "bv2xAAEEeABRsUIUXq16x3ah7J1zLz96Y/Tz995aY4zROssylWVKKbm0NCeiYmVy+vTiGgVC"
    "IFjPgREI2vN3PEInQdqD7EMpce1C9VsFUfHTQtkbYyYf28FgMPHAdLvdk/d1rqTTNE2SpNPp"
    "tNvtEK5Lma6vfxbR7bWz0xNzZQrEgeEdDPMGjFdffWMWSkFK1hrWjty8/GXjB6LonXPuzEPT"
    "6/VOlUyr1dp3b6der+PSyr/tZrMZx3Gj0fhFYXn5TQ7g+VXhPc7N7CfPR2ZUMNXxOwE1Ojg1"
    "xVrDGBjDWh++e7W6VRW12tbY2DHnHBExMxHtxdBZlh3PY/bt69zR+afi/JWRlTLHMZIEUkJK"
    "aM1KwVoewjkq3Ti0/Vtsbv4sFE708/kDVjODmfj/eO+0HgW2Q6DRZ0/Ezq2LgfccuQsN9AEF"
    "KMABerf9C2iAYqrHmBTIAAAAAElFTkSuQmCC")

index.append('UY')
catalog['UY'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABYklE"
    "QVQokWWRsYqTURSEz725ucZCsovCFrL4CLaWgi+wb2AnqN2CjQ+wvoK2iqA2woqInYWFWGhh"
    "iAqyQVlJsfzKjxA3+c98x2KTKDjNmTnVx0xq29bMzAwsZwMzg/X5T8XManVpPyVqf8fTxsO3"
    "zfFcWAiT6BQiXCHYu/M6NU1T637Or8wEl6J3LSKFxVqsTM559GlSwOF3zoqQ2SiFvxzPiBAh"
    "QkKEA8SZQe/W/YNS66D0duaL74EP6vVF9F98aH8de0d0onM6Red0YnuzTu99SUfNz83hcLHo"
    "IqJfCzCbg/EXiSVYyfn96HPZfXJ49XJuZy5CACGFCAUClwlEABuny427B+XBo2/bW8PDH4sV"
    "w5qEVVSnkOLC2VPzx+P0cTI9v3VO4t9ClsFYfS0iSklv3o2TXXl2++ZFBwlXdCeVC1c4OEjh"
    "MgIRz3efpsnXqePLGU+cOY5j4O6YAYY7ZtnsD8yTlKlFNE/4AAAAAElFTkSuQmCC")

index.append('UZ')
catalog['UZ'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABZElE"
    "QVQokX2RPUtcYRSE54WrYLGIuaYwYAgiQUhtvZWkkhTp0hlIE61SpLAQa39AQAgEf8BC2G6L"
    "dCnlpgnpViMrQbJ+sAtx1zNnzpviZoNF4lMMw+EwxUwCNlqttysr83t7nw8OPgLe6ex8+/pz"
    "+fH8+vobwAEDDBjXJgEbw+G7TqdbVWcRsbm5OhppejpdXIz29w+dQVetpNrt7QJAv3/dbD46"
    "P79eW1s6PR2W92Z6vV9lOdPtXtJklJmTWlycBcbFK3w4ef9k0Du7L3355JIfm8t5ZHpBigyj"
    "G0XO8sEh+mkwuGk0pgDknJGRkf9HSqmqvhcRUX/nnP+af4KUIrwIxORwZ37OKWd3pOfAy9e7"
    "V8e9cJdRpJuLFCmj02V/mppberhVtYvWM5RPfxxdnlCkaG4mo2gyOi1ImYsuLZcZFQqMQflC"
    "Y4GiSx51+KT9cJdLUpZCABKakylvD1prvXLGbX4DOjxvXkiHuOcAAAAASUVORK5CYII=")

index.append('VA')
catalog['VA'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABa0lE"
    "QVQokVWRsU5UYRSE5/z3v7CuuqsbthA0JmoMpQVGSKwsNBY+i8/gE/gOPoGx08YCY2I0MRYS"
    "moWGYlciQQKu7J1/xuKyUb56Zs45c2L/6xA6ADIAgWCRoBTdOztIV3QeShk6unr7JQC4wIYL"
    "QGMh+kOg4/OMx+OMFk7gYheY9szRSYUO2baLbTsiQlIGBJd/ahSbBh1pOuXx0dbu7uvTWb16"
    "91GvtywpSYQ9zy4WrcZq7Pxj/3jz06u6RB87W9+fT39/I5kgw3SrdmM3Nu3GZXp95fKzpy9O"
    "9vZGX/r3N952+48lZQFGmXva+JmjciyeHL75NXl3q/dwJa59/vhhsHxjoaqzCLhYjUGLZ0OQ"
    "rabubizdXD8YbS9WP9cePGlUJuNJlmDxbA3PD4hszapq4OSLa6uRIuXciZpkJmET1RBmmEhE"
    "OUV0IhIiAFwYDGwAbvuP0XvoDwSIEEFChIBL9zaFpf/f3Br+AlOWRi5rOFPDAAAAAElFTkSu"
    "QmCC")

index.append('VE')
catalog['VE'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABZklE"
    "QVQokV2RPWtUYRCFz3tzQbZII2lSLCHGTv+AWppAkjLY2ysiCklhk38QtEkjbPIDYpPAFvEi"
    "bGFjYSmIXxBTqCEibrbYOWdmLO4mqPDwMF+cZoqf4PAbUGH5MiIAAAEE4gIghAiEEEK9uIf9"
    "4Wbnsb94N712/SfgmQ6opKagKpmpPPfZm/26Cjy1u8cPbO3RM+BXJgHP5F9YpmWylG4IdbOM"
    "nH8OfG3vAM+089TJaWuAIRRgsLGxcHQ0liAGlb1et9OpMnM00urqJzLMgsy5uUuDwasKCCnI"
    "JIPMXq/bNMOVlY9LSx/6/d8HB/Nm0SIFoBqQO6Q05u5Ot2mG29s/zNIstra+j8d++HLh5o33"
    "bSKgchu37t97ePrl1OVucrpaU24hyk1Bl3zmysz62yd1H6+ndC34GWSSaZZmk+JiIkJe/Ood"
    "oA6gojA7myTkEEEWEhJaS3BvCaCMJp+dgH/b/1YA/gB/9Fp0l9PEswAAAABJRU5ErkJggg==")

index.append('VN')
catalog['VN'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABC0lE"
    "QVQokX2RIU7dYRDE50+fAEETJOJpZB2GAyA4RV2v0CBAwFXaag5R1VM0gCKIhjZhZ/b7IT5S"
    "eAlhM9ms2Jmd7Czopcbm/CYWJJ2eSmIMjaFu0uqQKMHmVX+4ulpJkuD6Rt3qxlY3B2aYX6aM"
    "iyrsZb0e0koSg43tKo7DrvlZuChPguwhrYa0dCshUYcL8898MpjLYq/4/ELQvKD0dCmHM/Ot"
    "wGAOixPz59mSkiFtSaJDggsXd8XXgoJHzovfj1RNvLJkU6VkKnFZfDH35nvxw//lSTIJH5Jp"
    "EZtVcVLcmiqOzLZ5KGKl1a1J2HK0v4+ttGLZy9pKZGsn+pj5Q3UPafm7GeQ7Gc96AotzVU2K"
    "OmSCAAAAAElFTkSuQmCC")

index.append('VOLAPUK')
catalog['VOLAPUK'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABhUlE"
    "QVQokWVQwYqCUAB8PtRU0kgUs0KSToWUlw59Qqc+OuhalwpSKUvUKJPSyJfK20OHdre5DTPD"
    "DENgjMEX4jhOkkTTtG+J/Mcdx1EUxbKsMAwFQUAIKYry20C8G2azmW3blmXVarXhcKhp2na7"
    "LcvScZw8zzudjiiKk8nkEwAAhGG4WCwwxrvdjmXZLMuiKBqPx/V6Xdd1URQ/DVEULZfL6/X6"
    "eDziODZNc7PZlGWpKArP8zRNR1F0v9+n06ksyzBJkv1+L8tyq9XCGLMsS5KkJEkQQt/3Pc87"
    "HA4QQo7jXNdN0xQGQZCmaRiG8/mcIAiGYV6v1+Vy6ff7PM/rug4AOB6PgiDcbjff98lqtQoA"
    "aDabNE1TFJXnuW3bhmFkWfbePRgMyrJ0XRchZJomgTF2HGe9XjMMw3FcmqaSJAVBEATBaDTK"
    "sowgiKIoEEK9Xk9VVRIA0O12ZVn2PA8A0G63VVVdrVYQQsMwTqfT8/mkKKrRaFQqlT+3/sb5"
    "fC6KQlXVb+kHSbvUw5wmwUQAAAAASUVORK5CYII=")

index.append('VU')
catalog['VU'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABkUlE"
    "QVQokU2LTYjMARjGn//M38e6cBNa2nIy2MO4SGoPnOQwLOXjMAdOitO6rDPOo72uIkWKGuXG"
    "QbFKe9mJk5ZCLnbXxyz/eZ/nfV8Hs6V+/XoOvwf7duDtQ4wAM4D/B4EB8AdYBX4BP4AVYAko"
    "e1+w0J/+/L58MGs9j/3uKYerkOpSjUwp19zvdksA7xZz6dvHU2dqt25UP8lD8iSHGJOWZkkW"
    "o6MB1ACE55sFv9etLl0tnrg/pQ1Ts+QgzYaQAZQAzh73rZu1/F13HuvKNC5eY0c0p9xMZk66"
    "mXOX89m/Q1X5zfucuqCX86oq3r4uZxGBjCKiFlFk1MN93aZ67xFKAJ++6tiElles2VC7VZy+"
    "XJlRNCNpQ5McG+u/eo0SwPM5fli0A3vVbuF8pz+YtMER0mlhTJobk0oNNggvUAIQ1Wyw3SrO"
    "dVZtwqqDxooWxqAFmaagwlU4+igB7NmtE0d3Tt39ve3kFjZIJ9dTKQaVUsjTPdzTYSgLoDky"
    "u30cOAyMA3NAAAIMiLUtAICAjfgLJMVRmyd9f8QAAAAASUVORK5CYII=")

index.append('WS')
catalog['WS'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABKUlE"
    "QVQokWWRwUpCURRF99OXg0ZCTRxY1LChOAj6AT+gWb/RzB+p33BYgRBhEEg0kEhToiBCGiQU"
    "PM/e954GT1PzsNmTuxbnwkmAIywmAjbvbIphBP4lBdBsnu7tb9fru1eXj7Vatdt9G7+Og2JR"
    "NwXSJZ/3d6uVAgB8d2fr/OzaLJTLm53Oc4lTMkQO3eg0N3MyqVYjUABijGi3nyaTrNE46PXe"
    "zdwos+BmzqmbzULOhBAKo9Hn8cnhV+b9/pgETTS6mU9tWQCQApYqKzG7u3gYDj42FGAyisw3"
    "cEFLEUgD7hFuXS8+MFDO/NNc0PNOyAikEUhIN4O0Aq3SkFxSLhQlJ/EHrWsiFBACcqFAoVJx"
    "EgoQQSYkJOQtIYQ8EUh+Vg+JtdMuPwH4BW/TXRAimTLYAAAAAElFTkSuQmCC")

index.append('YE')
catalog['YE'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAAA+ElE"
    "QVQokZWPPUpDURCFz5XbPEhArAJik42kCVlONmBtmUcW4QpcRXYRCIh2Fkks7pk5x+KRZwoV"
    "/BhmzvzClA98o4u/FqMFIKAC6J4eAdhCCkpHIsMZjnDQEQiK4eD780sFAFhvr8h0poNgOuig"
    "SQfdmthM3tw/CKi3m40XC5xOsC1BsuRMp2w500pHWiqTyd1nLefzues6ALZhGP6NUsrhcKiS"
    "hmnbo/gRlCKpSroU/rxvF1tS3W775XJ1PB4zU9LoR2wPxel02vd9AbBer/f7PUmSrbWrwEYG"
    "GRERMZ/Pd7tdBUDGbDbj0GAwGBGXlCkNC8O3/+YLVjNhourzb1MAAAAASUVORK5CYII=")

index.append('YU')
catalog['YU'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABEAAAALCAYAAACZIGYHAAAABHNCSVQICAgIfAhkiAAAASdJ"
    "REFUKJGdkb1Kw2AUht8kX0OamDRFq8FBDUVRyeLg4DWIo5OLgjfhZYiDm65ehZPwIUKnBhws"
    "VSzaFAtJJTZpk3wuGjSlTfUZ38N5OD/cyeo6M2UF/6X54YOYsgJLK/0qhCTBkESYCcSpRCQb"
    "POhAuNIDX+QgNCSsdZJ8yVWk1meH+vt3sLtUNefL3TI4wDX03nmn1QyGUTxO0I0SlVwbOxbm"
    "qml4YLrwXj0w8NDMhcpdf6Ny73Pjx3hrgM9mvq6DC0UocREDYzF3FQCjEt5powBAlCTELy0E"
    "yYQpviD7m2LdWJbSm/DPtW1fkEmfCVAeabJnHd9OErSfRBWUUvaTgeux2sUluzk9Y4HjsDwo"
    "pWzkxYWShq2jw6luka5j2/afGrLYto1PPjGOVBw0sOsAAAAASUVORK5CYII=")

index.append('ZA')
catalog['ZA'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAAB00lE"
    "QVQokU3GT0iTcRzH8c+z/ba25xkamg1KkWA1D3UoQoSSpKCgIKGgrnWJIjoJ81JGiFC3rG5B"
    "HTtIUEYQRURUginB6lAesnnInpw5ZHv25/f98+sgQfDizRuY6MRE+4k3owvFuWo+L8AGAlpA"
    "A4iAKrAOVIA/gHfjHWziHpSzHdtPbR3sev3efJiBkGMGsyNy/7U2PW32fUP/2eq1+drS0u+P"
    "4ZfRofM76zUzM+dKi47IWXJknbWOyOvpUcADMPt2bEd+Zfyr/6tpRBojey/mku0di8suipyq"
    "U3GiTtVL++tTUx6AQqGwuy8cGsRkKf2TE2u2JQ6peEqUSTYQOe7OZJ/cmTQAmOXp86hciRdO"
    "Nm79SJXVb3K9Wg2tkBVLQlaJhDdJCyEMABE+ejh25JDeLyVLrUSTI3bqB51JURIiYVYi5Yy/"
    "BRkYAMPHvL493s2F4HPVWBvdPXg9iAWr34N6TUScqlN1opoJTPeuC+bhOPL95sps22rESNDI"
    "/nOvHiWePa6E5WUiteTIKrMyay6XLhZfGj2Oq8XAj8UHervObBt+8SA9/6kWtKHXTxMps2NW"
    "ESeiIgDUwyWAkR04MLb59uXTK0AIKKCA/TcMMACAgeRf4EIuX9eJDOYAAAAASUVORK5CYII=")

index.append('ZW')
catalog['ZW'] = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABAAAAALCAIAAAD5gJpuAAAAA3NCSVQICAjb4U/gAAABmklE"
    "QVQokU2RP2iTcRRF3y/9mvgn0Ukq0lIDdaiCLk4ODkqREmjAoUIEEUFc1bGCg5OrOlU3h4Ii"
    "iF0yaEWFoE5Fl0wdOrlYIzFfk7z73rsONRK4cM98Tmpvt+eX52VJxERCJERsxCpiIjoGRUn5"
    "n7y0v7TafDBzGs4IetCDFjQP84DTPOBhRtz5uJ7yPD8wWfICN97eWljoM0HESYxNSSWR0ky3"
    "28xEhBMP8xfli8ura/duXIhBISnNAqApTcNAaBiyKbQfSdZ9vFKoH69c+fL76a/F6enXX7dO"
    "9HMHAiDUoa4IqAMHe1gTSTutjWxrs9z4TNru+8bk+aXFWg3DIQBV1X+nAKrVaqvVyrKTZ/37"
    "B9J2Px0unKu9vF5/cmTgQEADCCgNAQ3Dvim8E8lC4tDN+xHBM53ntxuXJnpZUgZCwKSREEmZ"
    "wIIVizYnkjqdTqVSGQ5662+uXq53mCmpjD05I0sCoac0t/PzW+r3+wMMnzXvHj01Uk6LgBMW"
    "Fv6/iZO+8mozbf/Ynj02K9dEeqOoOoo9znvty/IXxrxcMJLCp60AAAAASUVORK5CYII=")

