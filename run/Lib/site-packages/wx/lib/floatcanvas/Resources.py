#----------------------------------------------------------------------
# This file was generated by /usr/local/bin/img2py
#
from wx import Image as ImageFromStream
from wx import Bitmap as BitmapFromImage

from io import BytesIO
import zlib


def getMagPlusData():
    return zlib.decompress(
b'x\xda\x01*\x01\xd5\xfe\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x18\
\x00\x00\x00\x18\x08\x06\x00\x00\x00\xe0w=\xf8\x00\x00\x00\x04sBIT\x08\x08\
\x08\x08|\x08d\x88\x00\x00\x00\xe1IDATx\x9c\xb5U\xd1\x0e\xc4 \x08\xa3n\xff\
\xff\xc5\xdb\xb8\xa7\xee<\x04\x86gFb\xb2\x88\xb6\x14\x90\x01m\x937m\x8f\x1c\
\xd7yh\xe4k\xdb\x8e*\x01<\x05\x04\x07F\x1cU\x9d"\x19\x14\\\xe7\xa1\x1e\xf07"\
\x90H+$?\x04\x16\x9c\xd1z\x04\x00J$m\x06\xdc\xee\x03Hku\x13\xd8C\x16\x84+"O\
\x1b\xa2\x07\xca"\xb7\xc6sY\xbdD\x926\xf5.\xce\x06!\xd2)x\xcb^\'\x08S\xe4\
\xe5x&5\xb4[A\xb5h\xb4j=\x9a\xc8\xf8\xecm\xd4\\\x9e\xdf\xbb?\x10\xf0P\x06\
\x12\xed?=\xb6a\xd8=\xcd\xa2\xc8T\xd5U2t\x11\x95d\xa3"\x9aQ\x9e\x12\xb7M\x19\
I\x9f\xff\x1e\xd8\xa63#q\xff\x07U\x8b\xd2\xd9\xa7k\xe9\xa1U\x94,\xbf\xe4\x88\
\xe4\xf6\xaf\x12x$}\x8a\xc2Q\xf1\'\x89\xf2\x9b\xfbKE\xae\xd8\x07+\xd2\xa7c\
\xdf\x0e\xc3D\x00\x00\x00\x00IEND\xaeB`\x82\xe2ovy' )

def getMagPlusBitmap():
    return BitmapFromImage(getMagPlusImage())

def getMagPlusImage():
    stream = BytesIO(getMagPlusData())
    return ImageFromStream(stream)

#----------------------------------------------------------------------
def getPointerData():
    return zlib.decompress(
b"x\xda\xeb\x0c\xf0s\xe7\xe5\x92\xe2b``\xe0\xf5\xf4p\t\x02\xd2\x12 \xcc\xc1\
\x06$\x1f\x94\xdb\xfe\x00R,\xc5N\x9e!\x1c@P\xc3\x91\xd2\x01\xe4o\xf5tq\x0c\
\xa9\x98\xb3\xf5\xdaE\xa1V\x05\x0e\x96\x0bw\xbf\xfc\xdf\xbfc\xd1\xf4\xd9\x87\
\xa7\xa84Mw_n\xa3\xeb&\xbcS\xf4N\xa9\xdcn\x86\x03aZ\x1bWl{\xcet\x92m\xed\x8a\
[\xd1*\x9c\x82\x91\x93\x9eMuP\xd6\xbe4\xa3\xa1\xcd\xe8\x84\xc0\t%=\x85\xe6\
\x1d\x8d\x1aF\xac.\x132\x13\xc4^\x9ek\x14\xffx\xc6K\xa3\xd1\xcd-3\xa8\xa1M'\
\x85\xf3Ck\xcb\xb9\x07\xd7\x7f\x85\x7f=\xa7Ts\xe2^\xff\x83\xfb\xf1\x97\x15\
\x15\x94\xd2\xbc/5tl\t\xb3\x11\xcc\xe7\x12\xbe0;\xfa\xef7\x85X\x87\xfc{z:S'\
\x86-}\xb6\xe0\xbb\xc2\xfc\x03\x7f\xa7\\\xf3\xb5jM/fX\xf0/\xf7\xe3\xb5\xca7\
\x8f\xe66s\xf3\x99\xe7\xf8\x9e\xb4(\xfd\t\xf4\x00\x83\xa7\xab\x9f\xcb:\xa7\
\x84&\x00\xc7Jh8" )

def getPointerBitmap():
    return BitmapFromImage(getPointerImage())

def getPointerImage():
    stream = BytesIO(getPointerData())
    return ImageFromStream(stream)

#----------------------------------------------------------------------
def getMagMinusData():
    return zlib.decompress(
b'x\xda\xeb\x0c\xf0s\xe7\xe5\x92\xe2b``\xe0\xf5\xf4p\t\x02\xd2\x12 \xcc\xc1\
\x06$\x1f\x94\xdb\xfe\x00R,\xc5N\x9e!\x1c@P\xc3\x91\xd2\x01\xe4\xdf\xf6tq\
\x0c\xa9\x98\xb354\x9a\xaf\xc5\x80#e\xd5w\xfb\x8d\xa7\xea.\xa6j\x06\xec\xeaU\
Q[vE\xb2m\xba\x83\xf5\x0b_k\xe5\xe3\xc5\xf12?o\x15.\xf2b\xf0ol`V\xe63\xd6\
\x9f\xc8\xc35\xefw\x12\xff\x0fi\xc1\x96\x0em\x15{\x16\xb1\x98E_9\x18\xa6x\
\xdc\xe2\xdaa\xcb>\xe1\xda*\xe1\x1b\xde\x82\x15O\xfc\xa5\x9d\xdc\x83\x19\xb7\
\xabD\xee\xed\x98dv\xd6n\r\x9b\xe3\x12\x91=\xa9\xeb\x85[4\xa3<\x9d\xd3b\x1d\
\xb7f$]]\x96\xe1\xf2\xf8\xc6y\x8f5\xf6\xd2\xdb\x96\xe9\xdfT\\\xd5p\xbe-7\xa2\
ls\xac\x88\xa4\xf1n\xaf6=!\xd5\x9b\xab:\xca\xa6,?\x92\x1b\xdc\xe9r\xe0\xcb\
\xe2\xe6\x15\x13v\xfco^\xe5\xfa\xf2\xca\xcb[R[\xba&\xbd\xf5\xec\xf3\xd8U?\
\xfd\x80\xf2EM\xae\xf0\xa3\xf3Ut\xde\x17\xed\x0b}\xd2U\xcb0Ugv\x82\xa1Q\xc7S\
\xa07\x19<]\xfd\\\xd69%4\x01\x00+\xecq\xf9' )

def getMagMinusBitmap():
    return BitmapFromImage(getMagMinusImage())

def getMagMinusImage():
    stream = BytesIO(getMagMinusData())
    return ImageFromStream(stream)

#----------------------------------------------------------------------
def getMoveButtonData():
    return zlib.decompress(
b'x\xda\x01,\x01\xd3\xfe\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x18\
\x00\x00\x00\x18\x08\x06\x00\x00\x00\xe0w=\xf8\x00\x00\x00\x04sBIT\x08\x08\
\x08\x08|\x08d\x88\x00\x00\x00\xe3IDATx\x9c\xb5\x96\xd1\x16\x84 \x08D\x19\
\xf5\xff\xbf\xb8\x9a}Y[\xc2P\xe8\xb4<\xaa\xcc\x15\x1c-\xa0T\x89\xc6\xb1o\x14\
\x11)\xb5!\x9aS2\xe2\x00\x04\xc0\tz\r\xd0\xc5{d K\x80\x15\xcfB\xa6\x00O<\x03\
q\x01+\xf1(\xa4\xb9\xe4\xda@\xf2\x92\xd8\x81fx\xea\xaa\x01p\xec\x1b{\x82N\
\xb4\xbb\xb4\xa2\x9e\x85\x8b]\x94\xb5\xa1\x8e\xbb\xdc\x13\xa0{\x9e\xb9H+\x08\
P\xeap\xa0\xb6\xc7:92\xdf\xd7\x94\xda\x00\x92!\xb7<\t\x92\xf1\xa7\xe2i\xb4n\
\xc7\x7f\xb5\xa8\x89\xfc<\xaf\x17x6\x8c\xccwq\x11\xe5\xa2/\xe4\xbe\xceDh\xf1\
\x0b@C\x9e\xd8\xd4\xcb\xc5\xec\x83c\xdb\xf2\xcaS\xa1\xc5=\xfb\xdaq\x92\xf4 \
\xaeM\xa3g\xb2j\xe9\xf4\x1e\xac \x91\r\xb8-2\x90\xa1]Q3\x84n\xb2\xad$\xe3\
\xb4e\x05\x06\x92\xfem\xf9\x00\x8d\xa7\xbb\x936\xe9\xf2\xae\x00\x00\x00\x00I\
END\xaeB`\x82\xed\x9c\x836' )

def getMoveButtonBitmap():
    return BitmapFromImage(getMoveButtonImage())

def getMoveButtonImage():
    stream = BytesIO(getMoveButtonData())
    return ImageFromStream(stream)

#----------------------------------------------------------------------
def getMoveCursorData():
    return zlib.decompress(
b"x\xda\xeb\x0c\xf0s\xe7\xe5\x92\xe2b``\xe0\xf5\xf4p\t\x02\xd2\xc2 \xcc\xc1\
\x06$\x8b\x02\xcc\xce\x00)\x96b'\xcf\x10\x0e \xa8\xe1H\xe9\x00\xf2\xd7z\xba8\
\x86T\xccYz\xe5\xa0\xd0a\x05\x0e\x96\x0b\xb1_\xff\xef\xb7\xe0\xb4-)\x98\xb0\
\xe0\xc6\xab\x8b/Ns\xf5\xa5\xac<q\xac8>(+y\xdb\xba7\x0e*\x1f\xefL\x97I\xe4b<\
\xc0gqTg\x892\xb3\xb3NS\xd9\x01\xf1eG\xc5\x04;z\xaaK\xd6]9\xc6!c\x10\xfd&\
\xf2\xbbH\x97P\xd0\xfa6\xdbY\xbe)\xfd\xd2g\xb3/\xf5\xad\xcd\xdab,\xb2\xa4C\
\xc6\x91y\xc5Q\xbb\xb6\xacd\xe6}\xae[9\xff\xaf\x8d|\xbf\xcc\x7f\xc7\xabe\xfe\
W\xf6\xffl]]\xcd\xd2\xf3\xfd\xc2\xff\t\x17WO,5o\x8a;Ys(~\x81\xa6\x19s\xf8\
\x05\xa1\xcf\tlKg\xb0\x96\xc7\xdd\xe2_\xd9\xbe,\xc7\xc4,\xf8=\xd0\xe1\x0c\
\x9e\xae~.\xeb\x9c\x12\x9a\x00\x0b\xb6b\x8e" )

def getMoveCursorBitmap():
    return BitmapFromImage(getMoveCursorImage())

def getMoveCursorImage():
    stream = BytesIO(getMoveCursorData())
    return ImageFromStream(stream)

#----------------------------------------------------------------------
def getMoveRLCursorData():
    return zlib.decompress(
b"x\xda\xeb\x0c\xf0s\xe7\xe5\x92\xe2b``\xe0\xf5\xf4p\t\x02\xd2\xc2 \xcc\xc1\
\x06$\x8b\x02\xcc\xce\x00)\x96b'\xcf\x10\x0e \xa8\xe1H\xe9\x00\xf2{<]\x1cC*\
\xe6\x9c\xbd\xe2\xc8\xd7\xa0\xc0\xc3r \xf6\xc1\x7f}\xb6WG\xa5Z\xa75H=\x96\
\x93\xb6Z\xb8\xa4\x91G0_u\x8fZm\xdb\xd5I\xa9K\xdf%mMQ\xbciZU*~\xb9-\xd0\xe6C\
\xd3Y\x07\xe5\t\xbb\xa4\xc4T.\xf9'\xcf\xe54\xfcx ,/\xc5\xd5\xb1\xeb\x84\xf2\
\x0b\xa6\xb6\x19\x19\xbd\xc5\xcf\xd38\x19\xca>|\x9c\xad\xaa[\xb5@\x8e\xe5W\
\xab\xad\xb3\xc3f)m\xe5\xed\x01\xedg\x9b\xc4X\xe6|[\xe3\xab\x1b\xb9\x86m\xbd\
\xdd\x91wO\xf6\xff\xbf\xc9\xf6\xc6#\xdf|\x8be\x98\x16\xd0]\x0c\x9e\xae~.\xeb\
\x9c\x12\x9a\x00\x11\x04M\x96" )

def getMoveRLCursorBitmap():
    return BitmapFromImage(getMoveRLCursorImage())

def getMoveRLCursorImage():
    stream = BytesIO(getMoveRLCursorData())
    return ImageFromStream(stream)

#----------------------------------------------------------------------
def getMoveUDCursorData():
    return zlib.decompress(
b'x\xda\xeb\x0c\xf0s\xe7\xe5\x92\xe2b``\xe0\xf5\xf4p\t\x02\xd2\xc2 \xcc\xc1\
\x06$\x8b\x02\xcc\xce\x00)\x96b\'\xcf\x10\x0e \xa8\xe1H\xe9\x00\xf2gx\xba8\
\x86T\xccY{\xc5\x91\xef\x88\x02\x07k@\xc0\xfb\xfaG\xdb\xf6\xcf6\x14t\xb1\x9b\
,\xb9\xedE\xb7\xc2\xaa[\xbb6T\xbc\xe3^\xcb\x9f\xfa:\x8a5(\xb4\xf2\x1d\xb7}\
\xa2\xb0\x90\xe0\xca\x06\xf7\x9c\xd64\x03\x83#J+\x98\xf2"\xd8\x0c/$\x88j0\
\xb7O\xfc\x1d\xc0\xf0av\xda\x8e)?\n\rg\xc4\x0bL\x9btFz\xee\xe6\xfcG\xebo\x84\
\xa9I\x9f1\x9d\xff\xad\xe7\xee\xb2\xf3\x8c\x06\xf9\xd7\xa6\xfc\xdcy\xf6M\x82\
\xf6\x96\xb99\xaf#Y{\x16\x08$?\xe0\xb4JR7h\x0e:\xd3\xcc\xb3\xe8\x06WX\xdd-\
\xf1\xf5<\x05n\xca[\xef\xfd\x01\xba\x91\xc1\xd3\xd5\xcfe\x9dSB\x13\x00/\x9bT\
s' )

def getMoveUDCursorBitmap():
    return BitmapFromImage(getMoveUDCursorImage())

def getMoveUDCursorImage():
    stream = BytesIO(getMoveUDCursorData())
    return ImageFromStream(stream)

#----------------------------------------------------------------------
def getGrabHandData():
    return zlib.decompress(
b'x\xda\x01Z\x01\xa5\xfe\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x18\
\x00\x00\x00\x18\x08\x06\x00\x00\x00\xe0w=\xf8\x00\x00\x00\x04sBIT\x08\x08\
\x08\x08|\x08d\x88\x00\x00\x01\x11IDATx\x9c\xb5U\xd1\x12\x830\x08Kh\xff\xff\
\x8b7\xb3\x97\xd1C\xa4Zw\x93;\x1fJ1\t\x98VJ\x92\xb5N<\x14\x04 I\x00\x80H\xb4\
\xbd_\x8a9_{\\\x89\xf2z\x02\x18/J\x82\xb5\xce\xed\xfd\x12\xc9\x91\x03\x00_\
\xc7\xda\x8al\x00{\xfdW\xfex\xf2zeO\x92h\xed\x80\x05@\xa45D\xc5\xb3\x98u\x12\
\xf7\xab.\xa9\xd0k\x1eK\x95\xbb\x1a]&0\x92\xf0\'\xc6]gI\xda\tsr\xab\x8aI\x1e\
\\\xe3\xa4\x0e\xb4*`7"\x07\x8f\xaa"x\x05\xe0\xdfo6B\xf3\x17\xe3\x98r\xf1\xaf\
\x07\xd1Z\'%\x95\x0erW\xac\x8c\xe3\xe0\xfd\xd8AN\xae\xb8\xa3R\x9as>\x11\x8bl\
yD\xab\x1f\xf3\xec\x1cY\x06\x89$\xbf\x80\xfb\x14\\dw\x90x\x12\xa3+\xeeD\x16%\
I\xe3\x1c\xb8\xc7c\'\xd5Y8S\x9f\xc3Zg\xcf\x89\xe8\xaao\'\xbbk{U\xfd\xc0\xacX\
\xab\xbb\xe8\xae\xfa)AEr\x15g\x86(\t\xfe\x19\xa4\xb5\xe9f\xfem\xde\xdd\xbf$\
\xf8G<>\xa2\xc7\t>\tE\xfc\x8a\xf6\x8dqc\x00\x00\x00\x00IEND\xaeB`\x82\xdb\
\xd0\x8f\n' )

def getGrabHandBitmap():
    return BitmapFromImage(getGrabHandImage())

def getGrabHandImage():
    stream = BytesIO(getGrabHandData())
    return ImageFromStream(stream)

#----------------------------------------------------------------------
def getHandData():
    return zlib.decompress(
b'x\xda\x01Y\x01\xa6\xfe\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x18\
\x00\x00\x00\x18\x08\x06\x00\x00\x00\xe0w=\xf8\x00\x00\x00\x04sBIT\x08\x08\
\x08\x08|\x08d\x88\x00\x00\x01\x10IDATx\x9c\xad\x96\xe1\x02\xc2 \x08\x849\
\xf5\xfd\x9fx\xdb\xf5\'\x8c!\xa8\xab\xee\x975\xe5\x83\x0b\\@\xa9\xb2\xab\xeb\
<\xa8\xebR\x1bv\xce\xb4\'\xc1\x81OL\x92\xdc\x81\x0c\x00\x1b\x88\xa4\x94\xda\
\xe0\x83\x8b\x88\x00\x10\x92\xcb\x8a\xca,K\x1fT\xa1\x1e\x04\xe0f_\n\x88\x02\
\xf1:\xc3\x83>\x81\x0c\x92\x02v\xe5+\xba\xce\x83\xb7f\xb8\xd1\x9c\x8fz8\xb2*\
\x93\xb7l\xa8\xe0\x9b\xa06\xb8]_\xe7\xc1\x01\x10U\xe1m\x98\xc9\xefm"ck\xea\
\x1a\x80\xa0Th\xb9\xfd\x877{V*Qk\xda,\xb4\x8b\xf4;[\xa1\xcf6\xaa4\x9cd\x85X\
\xb0\r\\j\x83\x9dd\x92\xc3 \xf6\xbd\xab\x0c2\x05\xc0p\x9a\xa7]\xf4\x14\x18]3\
7\x80}h?\xff\xa2\xa2\xe5e\x90\xact\xaf\xe8B\x14y[4\x83|\x13\xdc\x9e\xeb\x16e\
\x90\xa7\xf2I\rw\x91\x87d\xd7p\x96\xbd\xd70\x07\xda\xe3v\x9a\xf5\xc5\xb2\xb2\
+\xb24\xbc\xaew\xedZe\x9f\x02"\xc8J\xdb\x83\xf6oa\xf5\xb7\xa5\xbf8\x12\xffW\
\xcf_\xbd;\xe4\x8c\x03\x10\xdb^\x00\x00\x00\x00IEND\xaeB`\x82\xd1>\x97B' )

def getHandBitmap():
    return BitmapFromImage(getHandImage())

def getHandImage():
    stream = BytesIO(getHandData())
    return ImageFromStream(stream)

#----------------------------------------------------------------------
def getGrabHand16Data():
    return zlib.decompress(
b'x\xda\x01\x0f\x01\xf0\xfe\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\
\x10\x00\x00\x00\x10\x08\x06\x00\x00\x00\x1f\xf3\xffa\x00\x00\x00\x04sBIT\
\x08\x08\x08\x08|\x08d\x88\x00\x00\x00\xc6IDATx\x9c\x9d\x92Qn\x031\x08D\x07\
\xd6\xc7\xc0\xf7?\x98}\x8c$\xaf\x1f[,\xaf\xb5n\x9a !\r\x08\x0f\x0c\xd8\x00\
\xfc(\xa6o-"\x000?\xc4\xaf\xedp\xc6\xe9\x00\xa5\xf7\xaeZ\xab^\xcf\x07\xb5VI\
\xda\xe2\x8c\x13\x9b\x99\x06{N\xf2\x0e\xa7KB\x12\xe5\x13\xb9\xbdw\x0123\xc1\
\x18\xe4dZw1\xeb\x9c1\xe7\xcb\xe1\x0e(".\x9d\xe6\xab\xec0 @%\x17\xd4Z\xd3\'\
\xe74;K\xbd\xb5&I\xe3\x12\x7f=\xca\x8bD\x84\xc6\xe4\xa9-\xb7\xbb\xdez\xd6\
\xbf\xd6\x00xj\xfb\xef$\xb3T?\x8a\xf9\xbc\xa0\x1d\xc9\xfa\x99f\xf3K0\x91\xbc\
\xeb~K\xf0\x8d\x99\xf9qI\xbc\x9e\x0f\xf2\xa7e\xb7\xbb\xdc\x96 \x1f\xac\x85w9\
I\xfa\x01\xd6\xd0\xefe\x16\x16\xb7\x9b\x00\x00\x00\x00IEND\xaeB`\x82\x0bmo\
\xbf' )

def getGrabHand16Bitmap():
    return BitmapFromImage(getGrabHand16Image())

def getGrabHand16Image():
    stream = BytesIO(getGrabHand16Data())
    return ImageFromStream(stream)

#----------------------------------------------------------------------
def getMondrianData():
    return zlib.decompress(
b'x\xda\xeb\x0c\xf0s\xe7\xe5\x92\xe2b``\xe0\xf5\xf4p\t\x02\xd2\n \xcc\xc1\x04\
$\xffH\xbc]\x0c\xa4\x98\x8b\x9d<C888n?\xf4\x7f\x00\xe4\xa6{\xba8\x86T\xccy;\
\xd5\x93\xaf\xc1\x80\x87\xd9\xb6\xa3\xffc\xd1<\xb1u"^G\xc5\x18\x0f\xd9\xed\
\x9a\xf8\xfc\xc2\x8e\xa9\x93Z\x97\xac\xd8)\x98\xfd\xbb\xc2\xaa\xe4z\xf0-\xa3\
\x07\xec\r%\x0bo\x9db~^\xc50eo\x11\x7f\x1c\xc3\x0ba\xa3\x93\xacg\xae\x9f_\
\xbf\x92\x91\xcd#K\x84\xf7\x86\xd5.\xf6\r\xcf\xad\x192u\xd6&Z~\xfekm\xf0\xa0\
\xd27c\x9e\xa0kv\xf2\x83\x17@+\x19<]\xfd\\\xd69%4\x01\x00}A@\xa3' )

def getMondrianBitmap():
    return BitmapFromImage(getMondrianImage())

def getMondrianImage():
    stream = BytesIO(getMondrianData())
    return ImageFromStream(stream)

#----------------------------------------------------------------------
def getHand16Data():
    return zlib.decompress(
b'x\xda\x01\x02\x01\xfd\xfe\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\
\x10\x00\x00\x00\x10\x08\x06\x00\x00\x00\x1f\xf3\xffa\x00\x00\x00\x04sBIT\
\x08\x08\x08\x08|\x08d\x88\x00\x00\x00\xb9IDATx\x9c\x8dS\xd1\x0e\x84 \x0ck\
\x87\x9f\x81\xff\xff\x93j\xef\xe12\xd8\xcd\xe1\xb9\xc4H\xc6,m\xa9\xa45\xac\
\xea:\x0f\xf9\xda\xda\xc6r\x88\xd6\xc6\xa3T\xbdw\x01\x100\xb7\xe2<\xad\x81\
\xce\xe0:\x0f\x91\xf3\x10I 9\xde\xb1\x1f\x19Yf\xe4\x03\xab>I\x90\x1c\xf2\xb6\
\x95\xfex\xea\nH\x92n\x0c\x9c\xf6\xdb2`\xba\x9d\xd0!\t\xd66>\x02\xea\xbb\xfb\
\xe3\xb4\xaf\xb3\xe3\xde\x8b3\x16\x80\xb0\xef;\x00\xa0\xf7^\xd3\xad\xb2\x10\
\xd1\xfc\xee\xcb\xfbNL\x06KZ\x1b\x19p\xcdO\xa6\xe5Ysj\x1e\x98\x18\xdf\x7f\
\x1f\x03!HoAn\xfe<\xeaK\xfd\xd2\x9f\xeao\xac\xa8\xae|\xba%1\xca\xc9U\xf5>\
\x98\xdc\xd9g\xb0\x13Hr\x00\x00\x00\x00IEND\xaeB`\x82\xde\xa5p@' )

def getHand16Bitmap():
    return BitmapFromImage(getHand16Image())

def getHand16Image():
    stream = BytesIO(getHand16Data())
    return ImageFromStream(stream)

#----------------------------------------------------------------------
def getMagPlus16Data():
    return zlib.decompress(
b"x\xda\xeb\x0c\xf0s\xe7\xe5\x92\xe2b``\xe0\xf5\xf4p\t\x02\xd2\x02 \xcc\xc1\
\x06$\xe5?\xffO\x04R,\xc5N\x9e!\x1c@P\xc3\x91\xd2\x01\xe4o\xf3tq\x0c\xa9\x98\
358\x9a\xef\xb0\x01\xc7\xe3\x89\xc9S~\xc7oZ\xfb}c\x93\x86\xe2\xc5g\xeb\xb9\
\x12\x93}N\xe9xI~/m\xe2ra\xbf>+9\xc4\xe8\xf3\x1dn\x06\xed\x89\x02\x05F\x06\
\x92\x0b\x96\xdf\xeb\xea\xf1\xfa\xb6\xec\xb7U3\x03\x83\xb7`\x8d;\x13C\xc4\
\x94\x88/\xcf\xa5\xba'\x85x\x9b\x1e\xd1\xbbb\xd6\xbc\xc7\xeb\x9e\xed\xce\x9c\
\x8fE\nV\x12\x0e,/\xef\xef6\xf6\xd3\xbe\xf2Lvf\x87G\x8d\x96\xf1\xf1}q\xa7\
\xc5\r7\xdf\xf3\x9d^t\xb4PFa\xd17.\xc1G\xc6\xa5_\x85\x94\x03\x8c\xab\xf7\n\
\x9e\xcaz\xb7\xe4\xd0\xeb\xb5\x93\x7f\x19\xbf\r8\xcf\x93\xb0\xef\x10\x9f\\\
\xde\x84\xd2\x0f\xf1L\x91G\x8c\x7f0t=<{\xccE9L\x01\xe8\x03\x06OW?\x97uN\tM\
\x00\xe1\xf8b\xe3" )

def getMagPlus16Bitmap():
    return BitmapFromImage(getMagPlus16Image())

def getMagPlus16Image():
    stream = BytesIO(getMagPlus16Data())
    return ImageFromStream(stream)

#----------------------------------------------------------------------
def getMagMinus16Data():
    return zlib.decompress(
b"x\xda\xeb\x0c\xf0s\xe7\xe5\x92\xe2b``\xe0\xf5\xf4p\t\x02\xd2\x02 \xcc\xc1\
\x06$\xe5?\xffO\x04R,\xc5N\x9e!\x1c@P\xc3\x91\xd2\x01\xe4\xaf\xf4tq\x0c\xa9\
\x98\xb36\xd8Q\xa8\xc5\x80C\xf9\x80\xf1\x9b\xff\xf6+\xd3\xf8\xb5\xb75\x87\
\xdc\x9dy\xd6P5\xd3I4`\xb2\xe0\xefmABWdfrW\x881_\x8f\x9c4g\xe6\x1c6E5}\xc6'\
\x0f\xbc\x85\xcf?\xca\xeaPIW\x93\xe0\xcb\xdf}N\xefc\x96Aq}\xe4#mfSw\xd35\xcf\
VL\x8a\xe5\x99\xf7(\xec\xc2\xe30\xc6\x80o\xe2?\xc3\xb2\xd7^\xedn\x9b\xe5\xa0\
[\xb5\xe9\xd0&\x1d\x91\x89\x9fmL\x02^\x8b.\xfa\x9f\xd2)T\x93\xed\xfb-\xf7\
\xed\xfd\xc3/\xc4<\x8d\x9a\xf4'?\x99\xff\x92\xef\xe7L\xcf\xae}a\xdfg\xc5\xe6\
\xf4\xcd\xe7q\x9b|\xe3 \xfb\xa7#\x1bw\xe4\x1f\xcdj\x01:\x9a\xc1\xd3\xd5\xcfe\
\x9dSB\x13\x00<\xbf^\xf7" )

def getMagMinus16Bitmap():
    return BitmapFromImage(getMagMinus16Image())

def getMagMinus16Image():
    stream = BytesIO(getMagMinus16Data())
    return ImageFromStream(stream)

