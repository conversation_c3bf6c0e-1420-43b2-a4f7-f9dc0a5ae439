#----------------------------------------------------------------------
# This file was generated by ./encode_bitmaps.py
#
from wx.lib.embeddedimage import PyEmbeddedImage

First = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAACz0lEQVRIiX1WsUodQRQ9Z7ME"
    "AoKJxC5EEgyKGk2RP7B8IA/Uwi+wsLS18g8srVKkCQGbZyFJG5tUIoSAIAQtAhaBBBuJmXtS"
    "zNzZ2X1PF5a9b+bOnTtn7jn3ceHzwgcAM7jv0R2vuS3IOmMGwHBWk5wB8EaSSBIAJIkobJEe"
    "VJJopCRBICzGp0UfWfSXCTCgHpntfVnHIMOnsMb24JJQS8phlX54pkVAdTZQCurf6G/Ddk0Q"
    "ACg0UJSwlFB0IIg+TcBs09K4qYDozgscAUUBiaQWPF27khSPHHdUtiUtPl5U/1kfi+OLkkkK"
    "aS59Jx5OaO3lmmQxhoKEgJZdUYTDQSP9eDBw7fkad9/sYn1qnXkuAJUqrr5YxaA3YG+qRwSA"
    "oVgbQAQwQlRAkY9bQuFPiPOz47PYebuDpadLSFWd5zI8RYw6lVyuCkloVUi8H409GMPW6y1s"
    "vNpAxSpXHUUPKi9PCE0VpQGmQVLMlZI20PyTeQ56A0w+moQTsiQmQmetJaIpbZBrvqxrr3UA"
    "0+PTnjFKvvg6BbWydh7kO7irrj1OztRh6Z7AUGadOQEBVetiQqJ5iLaf4OLPBa7/XmPko7Qu"
    "JHkI7cuucrAieHZKG5xcnWDl4wqOzo9Gb1Ak2P1WThInUGl7HUnS1fWVtj9ta/NwE5e/L0vt"
    "koLaJC3syomhoEi0EMmEAEJRqCDQiXT84xj9933uf93Hbbil61QmWsyefg91xtowRJSD7wc4"
    "/XmK81/nrbmbfzfY+7KHw2+HWJ5ebu6ro2GSwLl3cycQltoMTB1qpIDd0b2KDTJZhdOaRsDA"
    "VPdJZtsdCgY2/EBbrpv+0Ei9ZalHnUgCmABjK6vcubqn6WbalfbirZNQJRZqiMlJX1QGyv5K"
    "heT649ij6YCRyamJZzbGgM7GfORCVug+FIc6YP7DAKFGwFkXEplQqOSwlJc+DgcKaLKw4Ow/"
    "oX1dfBQFFGsAAAAASUVORK5CYII=")

#----------------------------------------------------------------------
Prev = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAB3RJTUUH2QYaCSkRaCYhigAA"
    "AAlwSFlzAAALEgAACxIB0t1+/AAAAARnQU1BAACxjwv8YQUAAAMGSURBVHjadVYxS1xBEJ6Z"
    "typYBIsgJBCr6BVJoekMSSXpBDGgpd39AVHzC8Ta2tLmilSmC5gycD8gkEOsxEA0hXAgRsyb"
    "7OzO7Nt9d75j2dndmd2Zb7+ZPex86vQAoQPysW+oTT5qydSsIyIwMSBhGDN6ucKkK7L/DZwf"
    "drzyYtgfmVEssZR9Y2+EQYX8XHNQGnvFoCMHBls/Lw646DizeSqL5qVXkEPighyC5bpGxbks"
    "kYgsttI7Dfdxr1HPrbxHYl/pOjU6uddx3m8e9eMBhUfZHSR8IXo19i4qbGwqXRO/vK346mTS"
    "wg5wKBQFHKTRjUISdeQOWmsCEaNenYQWwoseYYCBVHYqhx7TnLTF54uw9moN22vitPTkSLlB"
    "Y1rVyAEGm/Nt9sksHHw4gOOPx7D0bKlYy+1EDhAlWLKQdVwwZGpiCraWtrj7pgvTE9N6KcAJ"
    "LlKoCaNtYlGVsajheWKIyCsvV3Dn7Q7PzcxZ6kFd10HfwwGJYdSwTgQXMsG8BA0xY8j803nY"
    "e7cHyy+W/RYMI5/aBoJUmuGSC7qPy+8gKFEMcdJNwu77Xdh8vQnhqsTwsQN0M4WmoXPojF6U"
    "sGTB8gEf4PzmnId/h2xwWG8tqwKcUZMT9X1PgSEVRGoqvUSusYbezx6ufl5F30MNdcCUiNCa"
    "JmNDZaU4KmUBswisdiiL2Nh1c3/D+/192Piywf3f/bERFF5rNNaTXLJ5HUqsC/mdkssS52x4"
    "ht1vXdj+vo2Xt5dlBLnXZqv7UUoMTZJwCGGRNGns5dNfp7D+dR0OfxzC7b/bogZZ/cnrlTOe"
    "20RKtCzMXJb1O76Do8ERnFycwMLMwmjtwuYJcBZOSrQqK9dVrOQ23y7XV/dXfP3nuizXunmQ"
    "wUpFXoIzeRyvC732s5o/t/q5/PVR/Bq4qF2us3IOWe3KIbICxQaR0xeNyje2qEuQXrcEQwGF"
    "yfYy1hlEJC9FFeEIL1dWixJc+rrZezvyzyO6MAJPOMDDMLC/IPb3Ixa6cpNiHbJ54IR/qlWN"
    "PPgPVhphQTIBT3gAAAAASUVORK5CYII=")

#----------------------------------------------------------------------
Next = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAACtUlEQVRIiYWWPUtcQRSG33e9"
    "ETGS2EQhWAnGhFik22I7F1KlDegPsPYP+AfEYn+ANnYBCz/QJmAVQqyy2Agq6QRBUiUgiMx5"
    "U8zHnfvhZuByz9w5e2bOM2feWS59XfoCYBGjmp54LNqCrPbNABguC5KLAD5IEkkCgCQRmS0y"
    "BpUkGilJEAjz8WneR+b9ZQIMKFpXO2rVPkgzCyvtGFwSCkkprEInrjQLqNoECkHj2/tb0y4I"
    "AgCFEkWOJUdRQ+B9yoDJpoXvpgzRkxvYgiJDIqmCp253JPmU/YxKtqTeq55mxmcg57/LhTFX"
    "+sNBMh9DToJDxe5QRMRBI2N6MHB5dplH/SOsvVnjBCb8mPNjcAAcGG267LcOhANlQielFNKN"
    "FQDn05wcm8T6+3Xsf9xH/3U/jVX8Ql9OqR/HOjG98EEBU6yUVF1zz+c06A20s7yDhRcLHoUr"
    "scFBoV9FFHHEd8QQDo4vsax1Z7vY+7THje4Gpp9NM8cVEdGRAZ+fIF912mRXzSA2ABjjmFbe"
    "ruDk84lW362qo05jk2GQnFDAgCfrOhy7KCFJOuilYmp8ivMv51GokDljOOHpTEDwE4ys65Ym"
    "CWc3Z9j8tonr39flhgeZoIhyAldOEIMnp5YJbv7cYOv7Fk5/nSbftgMW30WWVq4xpR2w3D/e"
    "Y/vnNnaHu3h4fIhao0z8lMtJmiBWT2UP/CbRy5B0fHXMwY8B7v7epbEa64pcQ2H/rIao8ggY"
    "3g5xcHGA89vzxliFda5bme3l2rUhEmTQ4cVh/JFKrn6sjiLJe24LKGhMiNINZbWUrUxZhqpc"
    "17Akua5lAJgAY6UC/lchkhpY6k8Bl99AzSoKNa48UPJXuASDndgjQwTzupMu8zJgo0Iyvow+"
    "FBs3YPrDAKGAw2UdSVuFZCut+kQcyNAkYcHlP13rmcgeVJ4HAAAAAElFTkSuQmCC")

#----------------------------------------------------------------------
Last = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAACwklEQVRIiX1WPUtcURA9s3lK"
    "bAwoIbAkEEJEsxgSLEw6MWkEC/cn6H/wH2hjbSvEzljZahobUwSEEAgSCRIIa2NjEQMWuXNS"
    "3HvnzX2764NlZ++dNzPnzNfK7KfZjwCmcdfDIR/NMkFtnCkAxXklItMAXpOkiAgAkKTAyRTJ"
    "RklSVIQkQQg02heNOtSoTyWgQDUw2ruijkb6UWgtZ+MkUZE0s0w/cqTOIBsOmIzm76iv/XIl"
    "EAAQoqbC0+KpaFAQdWqDJoumc6WjaGgCB1DhKCFZ0NOUWyQj5OiRJpNcbi+zfb8NhnjOkO4C"
    "OTM+g+7TLucfzpMabTCQCCjkllCQ6RAVyfCgkIVHC3KweIDV56sywpF4F+Ld0pMlbLzdkLUX"
    "a4IASHDvBggChEq0DFKCmysAIcIcuzeG9Zfr2H+/j7nJueLOqI1Rg4HxLsD0WhleOmCiKSYT"
    "YtU19WCKu+92uflmExOjE3QFUL8bSIaSriolRJKiCMUqJUVpDQgAK89WuPh4UXp/evE8Iijf"
    "1dRoTA6s5n1d51r3/ZGe8dFxdiY7ds5AGBLXB7FMFRha1wMQ5JFioyUF5qK2ngBRJjknyxJV"
    "xF0/t/9ucXF9USY5pPEQXF8o0DJjzrgpDXBw8vsE3b0ujn8d1w5cgM3vysHyMybKjparv1fY"
    "+ryFo59H8M3Yl4NyVqUqUpQ50DoHSuXe9z3Z/rKNm9sbf9eXA2dcagehAdPNktPLU+yc7uDs"
    "6qzv7vDHIXrXPVxeX9b5aswwkpDOh85XEK/KIZU21MABNmR7OQek7YtvlagYRbahmpC1hkxF"
    "Oa4btNi4Zl44gRYZorM6kry5mmiakTZHu/tUCH4Dsa+T0/CjN2T6TIWUZOMe9QaMnZyWuHVj"
    "NJi70SC7sSJZRyh9G9D+MICoEHDepIRKCKVc5HWkpU6mA44aGyw4/w8M23JBubTHcwAAAABJ"
    "RU5ErkJggg==")

#----------------------------------------------------------------------
PrintIt = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAADDUlEQVRIiZ2Wz2pUWRDGf9Xc"
    "yAgRxkVDFsFGRARHISADbdCNvkCEPIAL8R3c+RZKnmBmp45usuyFzKySxRCT+GehkUQik0Fb"
    "O9fuez4X554/99rJwgPNrXO6uk7VV9/9qu3S6qU/gAsct3TExwVbyLXOHODYKszsArAgSWZm"
    "AJJkZLbMQlBJMmcmSQjD+fjmvI+c95cTOCimZntc1j7Ij1W4ZIfgkigkxbCqNyHTLKBaF6gO"
    "Gp7e3/1oF4YBmEhQBFgWfl3geve6h8InQB3SEELY+sd1Bu8HwgW4PIQ4X0mCaEoDb83fYml+"
    "ibo1hEvMLD7/2fubwdvBVKhw1BD5bJqw+CpCUJGtsPdPC5gfAZEMhMXS0kU2dmOAyK7IKvMs"
    "MjMbV2OomizK4KJIXK4xdlBQgODBxgNW3642GVT7hP27T++Y0QwA42o8BSKXIEJopb9Cv9vP"
    "Cdu222SOZ893nnP3r7uhCnDQqQ3DQfdE1/rdvqdVvXI730/zWZxftLmTc1hlRuVjFrEpgo46"
    "MaO8scc3uXk2w0ysQE6+AnNmVDAshyb00xUA9qX8AhWxgk5sioOv5VdGkxE/uypVDEdD8pgF"
    "FbHjTo790T69Uz3WPqzx7M2zJnuO0Kab526y2Fvk8+Fnym/lESzymOnlwUt6p3o8ff1U5aRk"
    "6fwSBLYk7kRr8GbAo38fcfXMVW182EgvXbggsCjI7L3BPR7OPtSr/17Z/Wv3uTJ3RU+2ntjB"
    "6CAFFzZ7YlbLl5dtMplw58872t7btp2DHaj89+mCDCIcHI4P2RxtJgiAG2dvUL/V8azoJBmr"
    "JhXbe9utIRTkumpC5J2EXALi8YvH2h/u+8lSg3X6l9Pc/v12U67btqAwZxGiOKGc15bdT7uU"
    "k1LLvy0nOaWWa1A5KW33/92AeZL6pGnYxZWLa4gFnETQ8TAWEwEsm1ZhJOZYNy9Ic2W9oMrl"
    "NSuzttusiGrrbWXil6SeDKLwJjeyDhnVPwwlZ/ha8DFZO+v0hwFRULGVZR2Htsmag7wl09En"
    "Y1u0U8e2vgN1UBiOxfpC0gAAAABJRU5ErkJggg==")

#----------------------------------------------------------------------
SaveIt = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAADAElEQVRIiZ1Wv09TURT+zuuj"
    "JWmMOomOrQaDDCTGri7MQoh/RfkLmFmY2AqJA7MbDQODjP5IQ2JEJwiJSNKURBY1OLT0ns/h"
    "/nwPJMabvPTc+84797vf+c65ldk3s68BTOOmwb886m2CWlpTAIqjXESmAcyRpIgIAJCkILEp"
    "4oOSpKgISYIQqI0van2o1p9KQIH8WrQ3obZBrp5Co+2Dk0ROMoSlm3ikSUCWNqAL6n+tv161"
    "c4EAgBD2aFO1Kczfm7cU2YAEIG5TOyfEvQk+JGXvdA9nF2eWLrUniRQ5dAsPFtB+2IZLB0gG"
    "O52n696uZ3VsHmwWaMsTVCDJDJn/iEnQYKfzso9QAFOmiAIQ4tXhksyRjmT7dBsjMwoU+Xfe"
    "H4RUsyqWHi2xVqmJUGxe1KuLyKOW7YIH1f/dx9rnNYzNGO5D+y4mEqKCCipoTbXQvNO06jEo"
    "UaSRoqAKAI1bDfZe9DDWcSpeDyCIeSKbQK1SC6qjcfE8RW43cYsFirpfuxiZka27KISCiqpZ"
    "FUuPLUUepKgrNLoNguaTE/Qv+lz7aClKdO+f4F9BBa37LTTuNizqJMlWpgq40o4tAWDzdlN6"
    "L3u41Eub5EREbk4A4iiybcaDdEkOJyihC2Myn0SNtX+qA781DYMo7AYmbuBq4v9HqScVVaRJ"
    "j7Go2D3uYvXdKoaXQ/oGliQS9Yk6V56vYPHJImgHCnGCihQxB5YiiogMfg0wHA3Zftq+Umgg"
    "2HnfkcHPQfAXFcAkPUpLFEGBkx8nhSPTEMvPlq/NQedtp0hp4RKyvhkNQUPCADTk7vEutw62"
    "vHTpZczAQdKLDH2JgiSVGqimWqnmohIo8jfU+od1Hn4/lPOLc4gRbvQ2IkVJoYmK7H/b9wFl"
    "58tOuPH8CWTm1cwnEHNQEr6P+2sxCiDoOrkSU66DnV6xJA/yYnuN2fdKcOphym/wJ4JyQj+z"
    "mIJtK9ld4gG1R+Q+DG08thXxPkIpo45/GEDkMDhKUIdLO1RjRF1UiPeJ8o12LPyjPzWYSc7N"
    "KY0aAAAAAElFTkSuQmCC")

#----------------------------------------------------------------------
Left = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAACzUlEQVRIiZ1WPUsdURA9Z11B"
    "7EMIgRQJxCJGEkgidoKFpWATK3+AKFgIgo1YiSAIr4hia5N/IKRRCSSFwSbVS5MukB+g+JE7"
    "J8X9fk8tXLjs7N3ZuTNnZs4sR7+MfgYwgvsu3bEsyoKsZ88AGLotyREAbySJJAFAkohCFhmN"
    "ShKNlCQIhHn7NK8j8/oyAQa0t3p7n9feSH8UluVoXBJaScmswkP0tDCongMUjMa717d+uSGI"
    "CAdF0kgYQMtygIJTj6dII2gkXNBxQceBcKBMoAv7DmjuTKTVa3FkETvvd0Bj3ncBDufluJdk"
    "Q4BIPtQKlhimgJVXK5p/Me8dMUDO68m8jiQP1y0QtRQBgbE6ioNIEWuv1zT3fI4pUgfBwGCQ"
    "MRdlFYX3Psm5lpMngIBGDTbebmDm2UxdZAESSRVc6duimjxEliGCeecHMYjNd5uafjqNqroA"
    "nH086y3mdPbuj13snu56exGiVCV+k0PNELY/bGvyyWRutNCAADCAAZG+0arGJNmgSXDFXmlT"
    "UgQMN8PqTHQw/mi88rqUy+c+nVhZQZaFHITWVoPGewH0eVcauiuC1HwhyRDQlIk5vzrHwvEC"
    "Tv6c4EGXALk62U1qjNA0l9eXWD5axuHvwwcdkJot3HMVWeaYG7vB6vGqLq4vMPtytsJ67NMY"
    "nHOxSmLD5cqpuSpA5EA5ZW5xgPvnuH6yzoOfBwDBeIVoGQ5glOkCd7mwH/LQlLxRwhUoAVtf"
    "t7B/uo8UhOV31XfWvzxdu36IYIIsh9z51tHVzRWWJpYgp5qLSrrulQW0od0ZlD2H9HCLTNz7"
    "vofu3y7kFBspTbr0bTHxIvXECAATELklehInVwj56NcRZALFzD/VbO5fbdl5KEM25QopJlcP"
    "lasgyEz1KCCK0ysN82wwdmMKucCXUYdi9WNQ/TBAaOHQ7YWkhKHwuoIi6UQ4UECTp0f3P4CK"
    "ICuf1hoBAAAAAElFTkSuQmCC")

#----------------------------------------------------------------------
Right = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAACs0lEQVRIiZ2Wv0scQRTHv9+7"
    "BU//gZSBELwEhaQIaS0sLNJYWlnGC1xlo2ChrSAIVwhXWySNljFYnEIkpDLEVJdA/gohgs77"
    "ptiZ2ZndnGAGhns7vHv73uf9mOX86fwHAF3ctzRhW5AFWe3MABjGBckugJeSRJIAIElEIosM"
    "RiWJRkoSBMJK+7RSR1bqywQYUPzT2/u8Lo00o7BKDsYloZAUzco/BE8Tg6q9QN5o+C31rSm3"
    "CCLgoEgaCQNolexRkEYuPloEjYTzOs7rOBAOlAl0/twBrYmJtOamiP3X++g/74NGwHkcDuU2"
    "5LLBI1IZaoYlhFlHBODts7fqsIO9yz0F1jBMQCQiVEmGxUosMqXnJUqSq91VbL3aIuVxueS/"
    "Ca6iquXoSbM6wm9trcyuYIpT2L7YhjOXI1VAZBUiGLQ2u4Zet5cWbF2O1SZJy0+XMd2axsZo"
    "Q7fuNhiPiEIE9IdsoYU223nTeTl9Ts+Xniyp0+5w/dM6buymbETfKy1YmUSZQjKjd6mn9ee6"
    "zsLjBR28OdBMa6a0Y5Ccz4Fv7WokAA+KIMgEQVFwCP1TRhB3qN//WOe/z9E77uH6z3WW7AKu"
    "yrjviQevk/EJNj9u4u7urlF5nHs/9w3CC5+LZheHl5vQVhtX764y40c/jrBzulOVaf6C70Vo"
    "qpgDn3QYGAdaOYeCzZiDw8tD7o52YWbZuPZV2UQ0yXOK1RlKlMOvQww+D3J95XI5rp1iY1Rj"
    "WFCYLYoRhQrG4GKg4ZdhOrarOZbKAgoaI6J4Q1l+QwVcktA/7uvs11lW1imWcOPVIwBMgLGB"
    "J8UnCaOfo4gsVl2KqLYLuHS8JmF62V9/Sg0lo1zJgKxGPRJE4faKl3llMHRjDDnhy6BDMfsw"
    "yD4YIBRwGNeRZJVTeZ1XSNBJLqIox4rG+C+fohr4fgXssAAAAABJRU5ErkJggg==")

#----------------------------------------------------------------------
Width = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAADeUlEQVRIiZWWT2gVVxTGf2cy"
    "L5TEQDA2aUFINmKJjyiUtjQidGHblcsGQRBx1Y3BjRtFXIkLF4ILcRehWmtWQRfWUIpdJC5E"
    "07hJUpAUJWmLpf5pCYS8ez8Xd+78y4vQgeGdmTnz3Xu+Od93njWnmz8Au3nXoS1OH2MhX7vn"
    "Ac9Sama7gX2SZGYGIElGKZZZBJUk82aShDB8wDcfcuRDvrzAQ9p2t+/adQDZXIUv4gguiVRS"
    "DqvsIu60BKjaAspA42/I95vj1DAAEwUVZVrKVNQoCDkFYB6bz+57lSja8gO2oaJEiaQKPfU4"
    "kRRKDisqj1XEPR09Gn1/VHLhen//fnUn3eE9h+QDhpyEoxInJiPSYd4slpeVbCO9I0x+MWkH"
    "PzwYnjk4NHjIbn11i+HeYcNjODBXetdhOExeJHlJWbmxA8wbx3cd59qBa+zs3lnpDgSDPYNc"
    "//o6Rz86SuIT5IWcwBHOsBBJLC+7IXmpr9HH1c+v6uTwSaWWxg8d6HAi6x0aSUOnPjmlK19e"
    "YXvndslJclW6rHm7OYfYhxAeG+0f5fzH57XjvR2F0Mxs5b8VFl8ugtCevj020DVQEeaLtRd2"
    "+qfTzD6bDUIMWvnVmlPNOUl7U1LGh8c5tusYWev+78PLM/F4gsszl2m1WshrPsn4tk46Geoe"
    "ssSSIIzsKMfl63Y5HUmHDfUO0aBhuKCLjv5v+r9FfLDR2uDes3v2av0lnw58RpqkeRlmZiv/"
    "rvDwj4csv1qmq9Fl2zq35QsBrLt1u/DzBS7ev0ir1YrC/DPJv7oD7zw3Fr7nyN0jLL9erpQ/"
    "83yG8R/HOXH3BI9WH1WePf3nKYdvHObm45tFJ7XrIrkgroW/Fxi7Paap36YKjxK50GIXSdLk"
    "/KTGvhtj6a8ltRNsGkVV8RYvrW2s2Zn7Z3jw/IHOHjgbhRiNzt6sv9G56XM2vThd+E9hhBat"
    "IsXVPKTmJXcW7/Bk9Qkj/SN56bO/z3Lpl0usvl6t5tc8TBI2PDE8h9hbBc4mVFsD22J6lRaQ"
    "8nkxn5q3nKJ8QvnqhMIXJctTtesaLRW6JNJM+uAFYbFiJ3Fy1aup77Ru7aUzxZUnkFQCV8ng"
    "VAbK85UNwdhZkXuKCZhGJefDvACMYslLzqdXeCaEmWzTBMz/MCBSHEt1SuSFyaqDvNhpNSfS"
    "QYmawsqW3gIVgl7eufygTQAAAABJRU5ErkJggg==")

#----------------------------------------------------------------------
Height = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAADXklEQVRIiX1Wz2tcVRT+vjfP"
    "dKE4jNhBI1kISogMxlQZQgKW/gtdtnQZaLclq5Z2kRYyJH9DtyXQLkSxopBBitK6KKmIDBmD"
    "CxeGGLEEIWli7/lc3B/vvb6ZXri8M/eec+493z3fOcPOd50NANN41dCYaVEWZC+tGQDDdk5y"
    "GsAnkkSSACBJREkWGZ1KEo2UJAiEef80ryPz+jIBBuQjbzvm1g00AAEv7EU9Civk6FwScknJ"
    "rcKPeNN0a/Py1Y+uQhLWfllTcBq/Xt/qck4QACgUUJRhiVCcbZ/FxfcvAoAe7z3mwz8fep3C"
    "YZJpYd2EbOxDWjHbE22szK4gYwaCuP3pbbRPtQEX4HDw01CVDcgkeQj8iUqyvJwpw+rcqloT"
    "LUUIWxMtrXZXQVFwkMz7kJPCoUnOKCLCQSNjeCFkLn24hO7pLuMAAJLstrtYmlkiDIQD6Eq2"
    "DoQDPUQlKEI0KdQzb53B5enLY+lx5eMrmHt7LkElV4cri+GFBQWY0Myb6n3WU4MND1cY5Wxr"
    "sKG1z9fUfK3pbZ0kV4UrD7lLCIKBFCGTFt5d4OCfAQYYSBJnWjOYfGMSknRwfMAne08U7LA4"
    "uagHwwfJlhaIJoCdLzpbkmbHkSam28r8Cs5/cB4AsLW3hUtfXUp7r7D7OYcBgdqj89oKTpCM"
    "cBEuRGwsyoe/dbKFCogqDw0Dzr13DvPvzCdezJ6eTY879eYUri1eS3uP/niE/m/9VCYoojjA"
    "lUIKzmXCYH+AW/O30DzVRMjONNqvt3GhcwEk8ezoGe78dKcGT/xWskiuINruv7u6+cPNlDWj"
    "skiSbnx7Q7sHu1WSluQsEkNOnmiODLnMzd83ufHrBlgakWgAcPfpXfaHfcJKRHMFSf0BpbqR"
    "SFIiyvqP6xj+PayRbLg/xHp/vbCz+pSETE5INSRB5NfkpOcnz7H8zbKO/jtKEB2eHGr5y2Uc"
    "nxyPhKUCEY1IsFioIa4a8s7+Dnvf9wj42t7b7HHnr52RtUtSZT2XU3hxAcYawaJ87+k9LEwt"
    "QBLub90vsq7Sm+szhyt3IKnkXKX2Jwi4/vX1Ys87VUxtCEUHRNENPZNDE09s9A4jG1NDPzw6"
    "TB0u6lCsdcD0hwFCDoftlyGpsDE28uKmVZ0IB0rQFLzc/h/4UISz4M1LhAAAAABJRU5ErkJg"
    "gg==")

#----------------------------------------------------------------------
ZoomIn = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAELklEQVRIiX1WTWhUVxg95817"
    "GlON2ug0KRiJaJOQBEWarm0F46qLGqhIcZ1EkEYCulBcunAhCLrJSgraRaG0hSyiEXUlGcmk"
    "Tn9MLTb+jEyiScAkxsx47+nivfvmzUQdeMz37vveufc75/t57Bjp+BFACz7003su62xBtmrN"
    "ArCY9Em2ANgjSSQJAJJEJGyRDlSSaElJgkDYEJ829JEN/WUFWMB/52k/dOoQZHUUtmw7cEnw"
    "JcWwim7cSROAqtpAEaj7D/3tatsnCAAUylQ4Wnau34nuhm41fdTEoini/tx9DD8d1uLbRToq"
    "EoCxTRutW4GdI53ZWINoAw+eBlsHeWT7ERCMtQGAuZU5nc6c5p3nd5wWYSQWZQ3Kekyk0t+l"
    "eyF8kqTgVNspROC4VbiFq4+uYmJ2Ag3rGtCwrgHd27pxb/oeni8+j7mONajUZNqjwjNCAC3Z"
    "ur6Vh7cfBgCey53j8bvHsXnNZmZfZtkz0sOxmTEEXsCzn59FRAVhABrSUQMDwoCygpfcWRK6"
    "G7tBELn5HK79ew0yQn9bP7rqu7BcWsaZu2dgrEFzXTP2btkLGAAm5FtG8X20ETxZhVkRLmhb"
    "7TYBwPjLcfW19il3KAdJ6mvvU+7bnPKv8ni2+EwA0L65XTLld2UkmRBPRoIB/IgzQqFQS6Ul"
    "AFD92nrezt8GDNTf2c9MIYPMdAYBAm1au4kAFDBgFL1gQYqxyK5WvCid5CLJzGQEAPs/3a/8"
    "Ql6X71+GJI0VxnQpe0k9O3tQt6ZOAPDf/KN3njpiRDKRBrQMhbLkyJMRPl54jNqglkP7hti1"
    "tQu7f9jNK39c4dG2ozz5xUm4tJ1ZfEGaUFgaEgYVIsMC7PipIwthTxyahF11uzT01RC3rNsC"
    "SVoxK/Q9H77nw/UsSXq18orHfjuG8fx4ZR2UC3AilT6U7oVFQ6yFBWZfz2L40TA3BBvQVNeE"
    "2qCWHj28tW+RX8hjY81GAkBNUMODnx3EX4U/8WT+KV0NuEKDRYHt19qzEHa70q5oXBZIIYX6"
    "mnrQEvPL85ARLhy8gH3N++L+WDIlnPj1BEb/Ga1ugL+n0t+ke2HRKKuwKbhiCUOmjLD0ZolL"
    "b5ZojKExBjce3mDr1hY0f7yDJOnRw4GWA3ww/YBTL6ZcVhIWBS8uiqgwqgtFRqueF0tFfP/L"
    "AG4+vBlHEaQCDH45WBG9JHgyQpxeRgpTLFxzReTs5LNiqYiBnwc0Ojkat/rJ6Ukl0l6wQCr9"
    "dbqXlo0JkakodV02OLriZ9G6NZbX/75OgpianeL5kfN8XXxdflco+DJOWAGWFd1wlehVE0wS"
    "Sirh4s2L751+PkxyAkkJcCXGn5Lgsb+iIRjZ8cRDeQL6rpLjYV4GdHmtRF67UUrnQ7Hiw6Di"
    "gwGCD4PJakqilK0c5OWTVvo4OpCgJp5/mPwf/ayV5rr3q+AAAAAASUVORK5CYII=")

#----------------------------------------------------------------------
ZoomOut = PyEmbeddedImage(
    "iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAEE0lEQVRIiX1WTUxUVxT+vjdv"
    "Kj8FbcERmoCh0Q4ESBsT66ZNaE2EVY3tStO4hrgphkQXunbhwkTdsW2kJk2a1oQ0KERdNYEA"
    "StpKbSkqENACqYLIDPd+Xbx737wZqpNM3nn3nXfuud/5vnMe24bavgOQxZt+es3feluQLVmz"
    "ACymQ5JZAB9JEkkCgCQRCVukDypJtKQkQSBsFJ828pGN/GUFWCD832zflHUUZPspbMH2wSUh"
    "lBSHlbvxmSYCqmQDuaD+Gvnb7XZIEAAoFKDwsOx7ex866zrVWNnInMnh/sp9DD4Z1NrWGj0U"
    "iYCxTevWrcD2ofaJuAZugwCB+pr7eGLvCRCMawMAK5srOjd6jncX7vpaRCexKNSgUI/JVObr"
    "TDeEPUkIzrachQuO24u3cW3mGiaXJ1FXXoe68jp0NnRibGkMC2sLMdZxDYprssT2n9snIHcC"
    "S2arsrj+6XUR5IWpCxj4ayDOroxluPrJVR3ac4gz/87o6I2jlFVp1km4JoPkzpLQWd8Jgpha"
    "ncLAnwOQcdkZYCO/gfO/nIexBk3VTThQewAw0TNZRb7u3r8TOsrFrGioaAAAjP8zrp7mHvS0"
    "9CTJi7Zv2zC3NqfGqka0vtOqsbkxD4k8PSEUWOQW6Ba5nl8HANXsqOGd+TuAQSQot00aae3a"
    "sYsAlGaaLpBgQYoxXF4rYUwzl8Xo01Ec23sMh987rCv3rmB0YTTJexzPHkf1W9UCgL9XZxRD"
    "mMja60BWCGABWhImug49HuKjF49Qka5gf0c/D+4+iJRSrAwqebLlJM98fAaetk/XnpEm4jwN"
    "CRMVFwaxzbbv2yIW2bjHYH/1fvV/3s/a8lpI0qbZZBiECIMQvmdJ0vPN5zx14xTG58eLdVAQ"
    "4GQq81WmGxZ1cS0ssPxyGYMzg6xKV6GxuhEV6QoGDLBltzD/Yh47y3YSAMrSZez6oAu/Lf6K"
    "x6tP6DXgKQuLRbYOtE5A+NBjVtS4LJBCCjVlNaAlVjdWISNc6rqEjqaOuD/mTR6nfzqN4T+G"
    "SxvgvVTmy0w3LOplFTUF10fckSkjrL9a5/qrdRpjaIzBrYe32Lw7i6Z33ydJBgxwJHuED5Ye"
    "cPbZrGclYbEYxKJwwigVSsySxPNcPodvfuzFyMOR+BTpVBp9n/UVnV4SAhlBRnLBFEk9WpNx"
    "dHN28lkun0PvD70anh6OW/300rSnp/MFUpkvMt20rE8UmXLU9WzwcMXP3Lo1ljd/v0mCmF2e"
    "5cWhi3yZe1l4V1gMZXxhBVgWdcNtRS+ZYJKQVx6XRy6/dvqFMMkJVFCsV6/rL0oGj/3lhqBX"
    "sp94KEzA0Cs5HuaFgJ7XSvDatxV6H4pFHwZFHwwQQhhMl0LiKFs8yAuZFvt4OJCAJp5/mP4P"
    "yFN2/NjDF0IAAAAASUVORK5CYII=")

