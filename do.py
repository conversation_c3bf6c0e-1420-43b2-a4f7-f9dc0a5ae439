import re
import subprocess
import time
import traceback
import threading

import urllib3
from selenium.webdriver.common.by import By  # 选择器，以什么方式选择标签元素

from fileimei import write_imei, session, unlock, start_chrome, adb,config,conf_path,添加型号运营商,从数据文件获取运营商,error,create_imei_queue,sync_queue_to_file
from zmailcode import zmail1
urllib3.disable_warnings()
error错误 = {
    # 44694 不是doimei  1表示
    # 44313 没满100天`0表示
    '44676': '无法在指定设备上执行 SIM 解锁过程,产品中包含新的SIM卡需要激活', '44313': '没满100天',
    '43121': 'IMEI 似乎在进行维修更换等时出现某种处理故障',
    '44691': '可能imei输入有误,或者不符合解锁条件',
    '44692': '尝试在线处理 2011 年 4 月至 2015 年 4 月期间发布的"非 web 解锁型号',
    '44810': '在不受 SIM 解锁条件的型号上', '44678': '终端的购买日期无法指定', '44694': '不是DOimei-更换运营商重试!',
    '44813': '指定终端机不能办理WEB SIM卡解锁手续', '44688': '指定终端手机补偿锁',
    '44695': '指定终端机不能办理WEB SIM卡解锁手续', '44805': '指定终端手机补偿锁'
    , '44807': '发生系统错误'
}

blacklist = []  # 存放黑名单说明
session = session()
型号={}
docomo_url = 'https://application.ald.smt.docomo.ne.jp/VIEW_ESITE/est/sc/main.jsp?nid=ESG223001BJP&xcid=MYD_esite_from_CRP_en_SUP_procedure_simcard_unlock_dcm_btm01'


class docomo:
    def __init__(self):  # 登录并保持cookie

        self.info = re.compile(
            r'action="../../(.*?)".*?name="uji.pageid" value="(.*?)".*?name="uji.verbs" value="(.*?)"', re.S)
        self.info0 = re.compile(r'name="arcv" value="(.*?)"', re.S)
        self.info1 = re.compile(r'action="../../(.*?)".*?name="uji.pageid".*?value="(.*?)"', re.S)
        self.errorcode = re.compile(r'\((\d{5})\)', re.S)
        self.imeiok = re.compile(r'(\d{15})', re.S)
        self.endcode = re.compile(r'<th>SIM.*?</th>.*?</tr>.*?<tr>.*?<td>.*?(\d{6,19})', re.S)
        self.第二页面 = re.compile(r'SIMロック解除を行う機種.*?<td colspan="2">.*?(\S+)&', re.S)

    def tiimei(self, imeifile, 展示, email, driver):  # 这里重复提交imei
        print('imei界面')
        # imei界面
        cookies = driver.get_cookies()
        cookies_dict = {cookie['name']: cookie['value'] for cookie in cookies}
        # print(cookies_dict)
        session.cookies.update(cookies_dict)
        idinfo = driver.find_element(By.ID, 'IDBODY')
        form = idinfo.find_element(By.TAG_NAME, 'form')
        print('界面元素')
        # 提交imei部分:
        提交 = '次へ'
        更改邮箱 = 'フリーアドレス'
        data1 = {
            'uji.pageid': form.find_element(By.NAME, 'uji.pageid').get_attribute('value'),
            'uji.id': 'body',
            'uji.verbs': 'ESG223001BJP_tetsudukiKakunin',
            'ESG223001BJP-01-01/seizouBangoInput': imeifile,  # 串号
            'ESG223001BJP-01-01/checkBoxInput': 'true',
            'ESG223001BJP-02-01/checkBoxSentakuKekka': 'true',
            'ESG223001BJP-03-01/soshinSakiSentakuKekka': 更改邮箱.encode('Shift_JIS'),
            'ESG223001BJP-03-01/nyuryokuMailAddr': email,
            'ESG223001BJP-03-01/nyuryokuKakuninYoMailAddr': email,
            'ESG223001BJP_tetsudukiKakunin': 提交.encode('Shift_JIS')
        }
        url = form.get_attribute('action')
        a = session.post(url, data=data1,verify=False)  ##这里判断能不能解 如果是status_code404 说明不能解
        # with open('imei第一页面.html', 'wb') as f:
        #     f.write(a.content)
        a.encoding = 'Shift_JIS'

        # with open('imei第一页面.html', 'wb') as f:
        #     f.write(a.content)
        try:
            imei = self.imeiok.search(a.text).group(1)
        except:
            if imeifile in a.text:  # 判断是不是没提交成功!
                raise AssertionError('imei异常')
            a.encoding = 'Shift_JIS'
            errorcode = self.error(a.text)
            ecode = error错误.get(errorcode, errorcode + "错误码")
            blacklist.append(imeifile + " " + ecode)
            展示(imeifile + ' ' + ecode, 1)
            if errorcode == '44694':
                return imeifile, 从数据文件获取运营商(imeifile,'do')
            return imeifile, "blacklist"
        else:
            return self.imeicode(imei, a.text, 展示)

    def error(self, text):  # 处理黑名单
        try:
            errorcode = self.errorcode.search(text).group(1)  # 读取解锁代码
        except:
            with open(f'{error}\\do-imei第二页面.html', 'w', encoding='utf-8') as f:
                f.write(text)
            raise AssertionError('imei第二页面提取信息失败')
        else:
            return errorcode

    def imeicode(self, imei, text, 展示):  # 获取到code码的处理
        info = self.info1.search(text)
        # with open('最终.html', 'w',encoding='Shift_JIS') as f:
        #     f.write(text)
        if not info:
            with open(f'{error}\\do-imei第二页面.html', 'w', encoding='utf-8') as f:
                f.write(text)
            raise AssertionError('imei第二界面获取信息失败')
        try:
            型号信息 = ''.join(self.第二页面.findall(text)[0].split('&')).replace('lt;', ' ').replace('nbsp;',
                                                                                                      ' ').replace(
                'gt;', '').replace('セット', '')
        except:
            型号信息 = ''
        info = info.groups()
        data = {
            'uji.pageid': info[1],
            'uji.id': 'body',
            'uji.verbs': 'ESG308001BJP_kanryo',
            'ESG308001BJP_kanryo': '\u624b\u7d9a\u304d\u3092\u5b8c\u4e86\u3059\u308b'
        }
        url = 'https://application.ald.smt.docomo.ne.jp/VIEW_ESITE/' + info[0]
        a = session.post(url, data=data)  # 最终解锁界面
        # with open('最终.html', 'wb') as f:
        #     f.write(a.content)
        if a.ok:  # 判断最后一步是否正常
            try:
                docode = self.endcode.search(a.text).group(1)  # 获得解锁码

                展示(型号信息 + ' ' + imei + " " + docode)
                型号[imei] = {'型号': 型号信息, '运营商': 'do'}
                return imei, docode
            except:
                a.encoding = 'Shift_JIS'
                if '受付番号' in a.text:
                    展示(型号信息 + ' ' + imei + " " + '特殊机型-已解-需实测')
                    型号[imei]={'型号':型号信息,'运营商':'do'}
                    return imei, 'Success'
                with open(f'{error}\\do-出现意外.html', 'wb') as f:
                    f.write(a.content)
                blacklist.append(imei + " " + '意外,可能要重试imei')
                展示(imei + ' 出现意外', 1)
                return imei, "意外,可能要重试-imei"


def do(info, 上次=0):
    print('开始解')
    device_id = info['账号'][0]
    driver = info['账号'][1][0]
    wait = info['账号'][1][1]
    ok=False
    imeierror = []
    展示 = info['展示']
    error1 = 0
    a = docomo()
    start_time=time.time()

    # 创建双端队列和锁
    imei_queue = create_imei_queue(info['imei列表'])
    queue_lock = threading.Lock()
    info['imei队列'] = imei_queue
    info['队列锁'] = queue_lock

    processed_count = 0
    total_count = len(imei_queue)
    info['processed_count'] = processed_count

    # 更新进度条范围
    info['进度条'](0)
    progress_obj = info.get('进度条对象')
    if progress_obj and hasattr(progress_obj, 'SetRange'):
        progress_obj.SetRange(total_count)

    while imei_queue and info['停止变量'][0] == 0:
        if error1 > 3:
            展示(f'检测到异常3次,已停止!请检查后再次进行解锁', 1)
            try:
                mail_obj = zmail1(info['邮件'], info['邮件密码'])
                mail_obj.error(info['异常通知'], 'do')
            except Exception as e:
                展示(f'邮件通知失败' + str(e), 1)
            break

        # 线程安全地获取下一个IMEI
        with queue_lock:
            if not imei_queue:
                break
            imei = imei_queue.popleft()

        # 更新进度
        processed_count += 1
        info['processed_count'] = processed_count
        info['进度条'](processed_count)

        if len(imei) != 15:
            展示(f'此 {imei} 异常,不满足15位', 1)
            continue

        try:
            if unlock(driver, wait):
                unlockcode = a.tiimei(imei, 展示, info['do接收解锁码邮箱'], driver)
            else:
                print(f'界面出错,imei: {imei}将写回')
                imeierror.append(imei)
                continue
        except Warning as f:
            展示(f'{f} 解锁异常,请重试', 1)
            # 将当前IMEI重新放回队列前端
            with queue_lock:
                imei_queue.appendleft(imei)
            processed_count -= 1
            info['processed_count'] = processed_count
            break
        except:
            error3 = traceback.format_exc()

            展示(f'解锁异常,将跳过若反复异常,请停止程序,解锁中请不要触动屏幕', 1)
            imeierror.append(imei)
            print(f'{imei}将写回{error3} ')
            error1 += 1
            if device_id != 'do':
                print('重启网页')
                start_chrome(device_id)
                time.sleep(1)
            continue
        else:
            if error1 > 0:
                error1 = 0
                print('目前正常,故障次数已重置')
            try:
                unlockcode = unlockcode[0] + " " + unlockcode[1] + "\n"
            except:
                展示(f'{unlockcode} 得到的解锁码异常', 1)
                imeierror.append(imei)
                print(f'{imei}将写回')
                continue
            else:
                ok=True

                # 同步队列到文件并获取队列快照
                with queue_lock:
                    sync_queue_to_file(imei_queue, info['imei位置'])
                    queue_snapshot = list(imei_queue)

                write_imei(unlockcode, info['imei保存位置'], info['imei位置'], queue_snapshot)
                if time.time() - start_time >= 1800:  # 1800秒 = 30分钟
                    展示('已运行30分钟，暂停5分钟...', 1)
                    for _ in range(150):  # 5分钟 = 300秒，每次sleep 2秒，循环150次
                        time.sleep(2)
                        if info['停止变量'][0] == 1:
                            break
                    start_time = time.time()  # 重置计时器
    # 处理剩余的队列内容
    with queue_lock:
        remaining_imeis = list(imei_queue)
        if remaining_imeis:
            sync_queue_to_file(imei_queue, info['imei位置'])

    if len(blacklist) > 0:
        展示('有黑名单等未解锁%d台,名单为:' % len(blacklist))
        for i in blacklist:
            展示(i)
        blacklist.clear()
    if imeierror:
        展示('有异常跳过的imei,%d ,已写回要解锁的文档.名单为:\n' % len(imeierror))
        # 将异常IMEI追加写入文件（保持原有逻辑）
        try:
            with open(info['imei位置'], 'a', encoding='utf-8') as f:
                for i in imeierror:
                    展示(i)
                    f.write('\n' + i)
        except Exception as e:
            print(f'写入异常IMEI失败: {e}')
        imeierror.clear()

    info['进度条'](processed_count)
    # info['停止变量'][1]['do'] = info['账号']
    session.close()
    展示('已执行完毕***do解锁已退出***')
    info['停止']()
    if ok:
        try:
            driver.get('https://www.docomo.ne.jp/mydocomo/')
            time.sleep(1)
        except:
            print('访问mydocomo错误')
            pass
        else:
            print('do-cookies已保存')
            config.set('do-cookies', 'cookies', str(driver.get_cookies()))
            f = open(conf_path, 'w', encoding='gbk')
            config.write(f)  # 将cookies重新写入
            f.close()
    try:
        driver.quit()
    except:
        pass
    添加型号运营商(型号)
    # 结束 Chrome 进程
    if device_id != 'do':
        subprocess.run(f'{adb} -s {device_id} shell am force-stop com.android.chrome', shell=True)
        # 锁定屏幕
        subprocess.run(f'{adb} -s {device_id}  shell input keyevent 26', shell=True)
