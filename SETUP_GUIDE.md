# IMEI Unlock Application Setup Guide

## 🚀 Quick Start

### 1. Install Python Dependencies

```bash
# Install core dependencies
pip install -r requirements.txt

# Install optional dependencies (if needed)
pip install DrissionPage
```

### 2. Install Additional Dependencies

Some dependencies may need manual installation:

- **ChromeDriver**: Download from [Chrome for Testing](https://googlechromelabs.github.io/chrome-for-testing/)
- **ADB**: Android Debug Bridge (included in Android SDK)

### 3. Configuration Setup

The application will create a default configuration file at `tool/解锁配置.ini` on first run. 

**Important**: Edit this file with your actual settings before using the application.

## 📁 Directory Structure

```
unlock1/
├── run.py              # Main application entry point
├── txt.py              # Text processing window
├── fileimei.py         # Core IMEI processing functionality
├── update.py           # Auto-update functionality
├── requirements.txt    # Python dependencies
├── tool/
│   ├── 解锁配置.ini    # Configuration file (auto-created)
│   ├── adb/            # Android Debug Bridge
│   ├── chromedriver.exe # Chrome WebDriver
│   └── Notepad4.exe    # Text editor
└── error/              # Error logs directory
```

## ⚙️ Configuration

### Email Settings
```ini
[通过此邮箱发送异常通知邮件]
mail = <EMAIL>
password = your_app_password
```

### Browser Settings
```ini
[do杂项]
手机or电脑(填写手机或者电脑) = 电脑
界面是否显示(填写是或者否) = 是
do接收解锁码邮箱 = <EMAIL>
chrome驱动路径 = 
```

## 🔧 Troubleshooting

### Common Issues

1. **Import Errors**: Make sure all dependencies are installed
2. **Configuration Errors**: Check the configuration file format
3. **Browser Issues**: Ensure Chrome and ChromeDriver are compatible
4. **Permission Issues**: Run as administrator if needed

### Error Logs

Check the `error/` directory for detailed error logs and HTML dumps.

## 🛡️ Security Notes

- Keep your email credentials secure
- Use app-specific passwords for email authentication
- Regularly update dependencies for security patches

## 📞 Support

If you encounter issues:
1. Check the error logs in the `error/` directory
2. Verify your configuration settings
3. Ensure all dependencies are properly installed
4. Check that Chrome and ChromeDriver versions are compatible
