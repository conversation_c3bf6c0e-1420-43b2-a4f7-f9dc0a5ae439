import configparser
import csv
import ctypes
import inspect
import json
import os
import random
import re
import subprocess
import sys
import threading
import time
import copy
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import required packages with error handling
try:
    import requests
except ImportError as e:
    logger.error("requests library not found. Please install it with: pip install requests")
    sys.exit(1)

try:
    import wx
except ImportError as e:
    logger.error("wxPython not found. Please install it with: pip install wxpython")
    sys.exit(1)

try:
    from collections import deque
except ImportError as e:
    logger.error("collections module not available")
    sys.exit(1)

# Optional imports with graceful fallback
try:
    from selenium import webdriver
    from selenium.webdriver.chrome.service import Service as ChromeService
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.support.wait import WebDriverWait
    SELENIUM_AVAILABLE = True
    logger.info("Selenium webdriver imported successfully")
except ImportError as e:
    logger.warning(f"Selenium not available: {e}. Some features may not work.")
    SELENIUM_AVAILABLE = False

try:
    from CloudflareBypasser import CloudflareBypasser
    CLOUDFLARE_BYPASSER_AVAILABLE = True
    logger.info("CloudflareBypasser imported successfully")
except ImportError as e:
    logger.warning(f"CloudflareBypasser not available: {e}. Some features may not work.")
    CLOUDFLARE_BYPASSER_AVAILABLE = False

try:
    from DrissionPage import ChromiumPage, ChromiumOptions
    DRISSION_PAGE_AVAILABLE = True
    logger.info("DrissionPage imported successfully")
except ImportError as e:
    logger.warning(f"DrissionPage not available: {e}. Some features may not work.")
    DRISSION_PAGE_AVAILABLE = False
cwd = os.getcwd()
adb = os.path.join(cwd, 'tool', 'adb', 'adb.exe')
error = os.path.join(cwd, 'error')

# 检查文件夹是否存在
if not os.path.exists(error):
    # 如果文件夹不存在，则创建它
    os.makedirs(error)
    logger.info(f"Created error directory: {error}")

# Initialize configuration with better error handling
config = configparser.RawConfigParser()
conf_path = os.path.join(cwd, 'tool', '解锁配置.ini')

def create_default_config():
    """Create a default configuration file if it doesn't exist"""
    tool_dir = os.path.join(cwd, 'tool')
    if not os.path.exists(tool_dir):
        os.makedirs(tool_dir)
        logger.info(f"Created tool directory: {tool_dir}")

    # Create default configuration
    default_config = configparser.RawConfigParser()

    # Add default sections and values
    default_config.add_section('通过此邮箱发送异常通知邮件')
    default_config.set('通过此邮箱发送异常通知邮件', 'mail', '<EMAIL>')
    default_config.set('通过此邮箱发送异常通知邮件', 'password', 'your_password')

    default_config.add_section('异常通知')
    default_config.set('异常通知', '收解锁异常通知信息邮箱地址', '<EMAIL>')

    default_config.add_section('do杂项')
    default_config.set('do杂项', '手机or电脑(填写手机或者电脑)', '电脑')
    default_config.set('do杂项', '界面是否显示(填写是或者否)', '是')
    default_config.set('do杂项', 'do接收解锁码邮箱', '<EMAIL>')
    default_config.set('do杂项', 'chrome驱动路径', '')

    default_config.add_section('do-cookies')
    default_config.set('do-cookies', 'cookies', '{}')

    default_config.add_section('sb-cookies')
    default_config.set('sb-cookies', 'cookies', '{}')

    default_config.add_section('au-cookies')

    default_config.add_section('au-账号信息')
    default_config.set('au-账号信息', '登录账号,登录密码,邮箱,邮箱密码', '[]')

    default_config.add_section('解锁码标识')
    default_config.set('解锁码标识', '解锁码每次解锁记录是否需要标识(填写是或者否)', '是')

    try:
        with open(conf_path, 'w', encoding='gbk') as f:
            default_config.write(f)
        logger.info(f"Created default configuration file: {conf_path}")
        return True
    except Exception as e:
        logger.error(f"Failed to create default configuration file: {e}")
        return False

if not os.path.exists(conf_path):
    logger.warning(f"Configuration file not found: {conf_path}")
    if create_default_config():
        logger.info("Default configuration file created. Please edit it with your settings.")
    else:
        logger.error("Failed to create configuration file. Exiting.")
        sys.exit(1)
# Load configuration with improved error handling
def load_config():
    """Load configuration with proper error handling and defaults"""
    global 邮件, 邮件密码, 异常通知, 平台选择, 浏览器界面是否显示
    global do登录, do接收解锁码邮箱, chrome驱动路径, sb登录, au登录, 解锁码标识

    try:
        config.read(conf_path, encoding='gbk')
        logger.info("Configuration file loaded successfully")

        # Load email settings with defaults
        邮件 = config.get('通过此邮箱发送异常通知邮件', 'mail', fallback='')
        邮件密码 = config.get('通过此邮箱发送异常通知邮件', 'password', fallback='')
        异常通知 = config.get('异常通知', '收解锁异常通知信息邮箱地址', fallback='')

        # Load DO settings with defaults
        平台选择 = config.get('do杂项', '手机or电脑(填写手机或者电脑)', fallback='电脑')
        浏览器界面是否显示 = config.get('do杂项', '界面是否显示(填写是或者否)', fallback='是')
        do接收解锁码邮箱 = config.get('do杂项', 'do接收解锁码邮箱', fallback='')
        chrome驱动路径 = config.get('do杂项', 'chrome驱动路径', fallback='')

        # Load cookies with safe evaluation
        try:
            do登录_str = config.get('do-cookies', 'cookies', fallback='{}')
            do登录 = eval(do登录_str) if do登录_str.strip() else {}
        except Exception as e:
            logger.warning(f"Failed to parse do-cookies: {e}. Using empty dict.")
            do登录 = {}

        try:
            sb登录_str = config.get('sb-cookies', 'cookies', fallback='{}')
            sb登录 = eval(sb登录_str) if sb登录_str.strip() else {}
        except Exception as e:
            logger.warning(f"Failed to parse sb-cookies: {e}. Using empty dict.")
            sb登录 = {}

        # Load AU settings
        au登录 = {}
        try:
            if config.has_section('au-cookies'):
                for key, value in config.items('au-cookies'):
                    try:
                        au登录[key] = eval(value) if value.strip() else {}
                    except Exception as e:
                        logger.warning(f"Failed to parse au-cookies for {key}: {e}")
                        au登录[key] = {}
        except Exception as e:
            logger.warning(f"Failed to load au-cookies section: {e}")

        # Load unlock code settings
        解锁码标识 = config.get('解锁码标识', '解锁码每次解锁记录是否需要标识(填写是或者否)', fallback='是')

        logger.info("All configuration values loaded successfully")
        return True

    except Exception as e:
        logger.error(f'Configuration file error: {e}')
        logger.error("Please check the configuration file and restart the application")
        return False

# Load the configuration
if not load_config():
    logger.error("Failed to load configuration. Exiting.")
    sys.exit(1)
UA = [
    "Mozilla/5.0 (Linux; U; Android 10; zh-CN; Redmi K20 Pro Build/QKQ1.190825.002) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/57.0.2987.108 UCBrowser/12.0.4.988 Mobile Safari/537.36",
    "(Linux; Android 9; SC-03J) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.81 Mobile Safari/537.36",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1"]
imei = re.compile(r'([0-9]{15})')
docomo_url = r'https://application.ald.smt.docomo.ne.jp/VIEW_ESITE/est/sc/main.jsp?nid=ESG223001BJP&xcid=MYD_esite_from_CRP_en_SUP_procedure_simcard_unlock_dcm_btm01'

wait = None
driver = None

def create_browser():
    """创建浏览器实例 - 使用 test.py 的配置"""
    if not DRISSION_PAGE_AVAILABLE:
        logger.error("DrissionPage not available. Cannot create browser instance.")
        return None

    try:
        # 按照 test.py 的方式：环境变量 + Windows 默认路径
        browser_path = os.getenv('CHROME_PATH', r"C:/Program Files/Google/Chrome/Application/chrome.exe")

        # 浏览器启动参数 - 完全复制 test.py 的配置
        arguments = [
            "-no-first-run",                    # 跳过首次运行向导
            "-force-color-profile=srgb",        # 强制使用 sRGB 颜色配置
            "-metrics-recording-only",          # 仅记录指标，不上传
            "-password-store=basic",            # 使用基本密码存储
            "-use-mock-keychain",              # 使用模拟钥匙串
            "-export-tagged-pdf",              # 导出标记的 PDF
            "-no-default-browser-check",       # 不检查默认浏览器
            "-disable-background-mode",        # 禁用后台模式
            "-enable-features=NetworkService,NetworkServiceInProcess,LoadCryptoTokenExtension,PermuteTLSExtensions",  # 启用网络服务特性
            "-disable-features=FlashDeprecationWarning,EnablePasswordsAccountStorage",  # 禁用 Flash 警告和密码存储
            "-deny-permission-prompts",        # 拒绝权限提示
            "-disable-gpu",                    # 禁用 GPU 加速
            "-accept-lang=en-US",             # 设置语言为英文
        ]

        # 创建浏览器选项
        options = ChromiumOptions().auto_port()
        options.set_paths(browser_path=browser_path)
        for arg in arguments:
            options.set_argument(arg)

        return ChromiumPage(addr_or_opts=options)
    except Exception as e:
        logger.error(f"Failed to create browser instance: {e}")
        return None

class CloudflareSession:
    """Cloudflare 绕过会话 - 复用浏览器对象"""

    def __init__(self):
        """初始化浏览器会话"""
        self.driver = create_browser()
        self.cf_bypasser = CloudflareBypasser(self.driver)

    def get_content(self, url):
        """获取页面内容"""
        try:
            # 1. 访问页面
            self.driver.get(url)
            time.sleep(2)
            # 2. 绕过 Cloudflare（如果需要）
            if not self.cf_bypasser.is_bypassed():
                self.cf_bypasser.bypass()

            # 3. 提取内容
            pre_element = self.driver.ele('tag:pre')
            if pre_element:
                # 尝试解析 JSON
                try:
                    return json.loads(pre_element.text)
                except:
                    return pre_element.text
            else:
                # 返回整个页面
                return self.driver.html

        except Exception as e:
            print(f"获取内容出错: {e}")
            return None

    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()

def get_content(url):
    """单次使用的简单函数"""
    driver = create_browser()
    try:
        driver.get(url)
        cf_bypasser = CloudflareBypasser(driver)
        cf_bypasser.bypass()

        pre_element = driver.ele('tag:pre')
        if pre_element:
            try:
                return json.loads(pre_element.text)
            except:
                return pre_element.text
        else:
            return driver.html
    finally:
        driver.quit()
def start_chrome(device_id=None):
    try:
        cmd = [adb]
        if device_id:
            cmd += ['-s', device_id]
        subprocess.getstatusoutput(f'{" ".join(cmd)} shell input keyevent 82')
        subprocess.getstatusoutput(f'{" ".join(cmd)} shell input keyevent 82')
        time.sleep(1)
        command = fr'{" ".join(cmd)} shell am start -n com.android.chrome/com.google.android.apps.chrome.Main'
        if subprocess.getstatusoutput(command)[0] != 0:
            return False
        return True
        # command = cmd + ['shell', 'dumpsys', 'activity', 'top']
        # result = subprocess.check_output(command, timeout=10)
        # activity_lines = result.decode('utf-8').strip().split('\n')
        # for line in activity_lines:
        #     if 'ACTIVITY' in line and 'com.android.chrome' in line:
        #         return True
        # return False
    except Exception as e:
        print(e)
        return False


def get_device_ids():
    try:
        result = subprocess.check_output([adb, 'devices'], timeout=20)
        devices = result.decode('utf-8').replace('\r', '').strip().split('\n')[1:]
        device_ids = [device.split('\t')[0] for device in devices if
                      len(device.split('\t')) == 2 and device.split('\t')[1] == 'device']
    except subprocess.TimeoutExpired:
        print("错误：获取设备 ID 超时。")
        return []
    except subprocess.CalledProcessError:
        print("错误：运行 adb 命令失败。")
        return []

    if not device_ids:
        print("警告：未找到连接的设备。")
    else:
        for device_id in device_ids:
            print(f"已找到设备 {device_id}。")

    return device_ids


def get_imei(fname):  # 返回一个串号列表
    """
    :param fname: 出码串号存储地址
    :return: 返回去除重复原顺序的串号列表
    """
    if fname == None:
        return []
    a = []
    try:
        with open(fname, 'r', encoding='utf-8', errors='ignore') as f:
            b = f.readlines()
            for i in b:
                i = imei.findall(i)
                if i:
                    k = i.copy()
                    if len(i) > 1:  # 如果一行有多个串号,判断串号是否是串号 根据第一个的开头进行判断
                        开头 = i[0][:2]
                        for j in i:
                            if not j.startswith(开头):
                                print(f'不符号imei的-{j}')
                                k.remove(j)

                    a.extend(k)
    except:
        raise Warning('读取串号文本出错,请检查,确保文件是否存在')
    imeilist = list(set(a))  # 去除重复
    if len(a) != len(imeilist):
        print(f'检测到重复IMEI{len(a) - len(imeilist)}个')
    return sorted(imeilist, key=lambda x: x[:8])


def create_imei_queue(imei_list):
    """创建IMEI双端队列"""
    return deque(imei_list)


def sync_queue_to_file(queue, file_path):
    """将队列同步到文件"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(queue))
    except Exception as e:
        print(f'同步队列到文件失败: {e}')


def add_imei_to_queue(queue, imei_list, position='back', queue_lock=None):
    """
    向队列添加IMEI
    :param queue: 双端队列
    :param imei_list: 要添加的IMEI列表
    :param position: 'front'前插入, 'back'后插入
    :param queue_lock: 队列锁
    :return: 添加的IMEI数量
    """
    if not imei_list:
        return 0

    # 过滤和验证IMEI（按照原有逻辑）
    valid_imeis = []
    for imei_str in imei_list:
        found_imeis = imei.findall(imei_str.strip())
        if found_imeis:
            # 复制列表用于处理
            k = found_imeis.copy()
            if len(found_imeis) > 1:  # 如果一行有多个串号,判断串号是否是串号 根据第一个的开头进行判断
                开头 = found_imeis[0][:2]
                for j in found_imeis:
                    if not j.startswith(开头):
                        print(f'不符号imei的-{j}')
                        k.remove(j)

            # 添加有效的IMEI到列表
            for found_imei in k:
                if len(found_imei) == 15 and found_imei not in queue:
                    valid_imeis.append(found_imei)

    if not valid_imeis:
        return 0

    # 线程安全的队列操作
    if queue_lock:
        with queue_lock:
            if position == 'front':
                for imei_item in reversed(valid_imeis):  # 反向插入保持顺序
                    queue.appendleft(imei_item)
            else:
                queue.extend(valid_imeis)
    else:
        if position == 'front':
            for imei_item in reversed(valid_imeis):
                queue.appendleft(imei_item)
        else:
            queue.extend(valid_imeis)

    return len(valid_imeis)


def write_imei(unlockcode, fname, imeipath, imeilist):
    """
    :param unlockcode: 解锁码
    :param fname: 解锁码存储地址
    :param imeipath: 出码串号存储地址
    :param imeilist: 没出码的串号,请传输参数时+1
    :return:
    """
    try:
        with open(fname, 'a', encoding='utf-8') as f:
            f.write(unlockcode)
        str = '\n'
        with open(imeipath, 'w', encoding='utf-8') as f:
            f.write(str.join(imeilist))
    except:
        raise Warning('读取串号文本出错,请检查,确保文件是否存在')


def addwriteimei(path, imeilist):
    str = '\n'
    with open(path, 'w', encoding='utf-8') as f:
        f.write(str.join(imeilist))


def session():
    headers = {
        'User-Agent': random.choice(UA)
    }
    session = requests.session()
    session.headers = headers

    return session


def browser(device_id=None):
    options = webdriver.ChromeOptions()
    options.add_experimental_option('androidPackage', 'com.android.chrome')
    options.add_experimental_option('androidUseRunningApp', True)
    if device_id:  # 检查是否指定设备id
        options.add_experimental_option('androidDeviceSerial', device_id)
    cwd = os.getcwd()
    chromedriver_path = os.path.join(cwd, 'tool/chromedriver.exe')
    service = ChromeService(executable_path=chromedriver_path)
    return webdriver.Chrome(service=service, options=options)


def PC_chrome():
    global chrome驱动路径
    options = webdriver.ChromeOptions()
    # 禁用图片加载
    prefs = {"profile.managed_default_content_settings.images": 2}
    options.add_experimental_option("prefs", prefs)
    # 禁用自动化提示
    options.add_argument("--disable-infobars")
    # 禁用扩展
    options.add_argument("--disable-extensions")
    # # 启用无头模式（不显示浏览器界面）
    if 浏览器界面是否显示 == '否':
        options.add_argument("--headless")
    try:
        driver = webdriver.Chrome(options=options, service=ChromeService(chrome驱动路径))
    except Exception as e:
        print(f'chrome驱动错误-将重新下载驱动--受限网络 有点慢{e}')
        driver = webdriver.Chrome(options=options)
    return driver


def unlock(driver, wait):
    stop = 0
    while True:
        print('开始启动网页')
        driver.get(docomo_url)
        try:
            wait.until(EC.presence_of_element_located((By.ID, "seizouBangouInput2")))
        except:
            print('未再正确解锁界面-继续等待')
            if 'dアカウント - ログイン' in driver.title:
                print('请登录....')
                wx.MessageBox("发现账号已掉-请登录后点确定.", "请登录...", wx.OK | wx.ICON_INFORMATION)
            stop += 1
            if stop > 4:
                if 'メンテナンス中' in driver.title:
                    raise Warning('do官网维护中')

                raise Warning('已检测很久 未发现再解锁界面')
                return False
            continue
        else:
            print('网页正常')
            return True


def login(device_id, 展示):
    global driver, chrome驱动路径
    展示('解锁浏览器初始化。。比较慢')
    if 平台选择 == '电脑':
        try:
            print('电脑浏览器登录')
            driver = PC_chrome()
        except Exception as e:
            driver = None
            print(e)
            return 1
        config.set('do杂项', 'chrome驱动路径', driver.service.path)
        f = open(conf_path, 'w', encoding='gbk')
        config.write(f)
        f.close()
        chrome驱动路径 = driver.service.path
        print(driver.service.path)
    else:
        if not start_chrome(device_id):
            展示('前台未检测到chrome浏览器启动，请确保已经解锁并处于chrome打开状态。。', 1)
            return 1
        try:
            driver = browser(device_id)
            # 关闭除当前标签页以外的所有标签页
        except Exception as e:
            driver = None
            print(e)
            pattern = r'upports Chrome version (.*?)\nCurrent browser version is (.*?)with'
            match = re.findall(pattern, str(e), re.S)
            if driver:
                print('退出chrome进程')
                driver.quit()
            if match:
                print(match)
                展示('浏览器驱动版本不符合', 1)
                return list(match[0])
            if 'session not created' in str(e):
                展示('浏览器驱动版本不符合', 1)
                return [subprocess.getstatusoutput('.\chromedriver.exe --version')[1].split()[1],
                        '打开浏览器点击关于查看版本']
            return 1
        # driver.close()
        # driver.quit()
    # 获得所有句柄
    handles = driver.window_handles

    # # 关闭除第一个以外的所有标签页
    # for handle in handles[1:]:
    #     driver.switch_to.window(handle)
    #     driver.close()

    # 切换回第一个标签页
    driver.switch_to.window(handles[0])
    展示('检测并等待解锁界面。。')
    # 添加 cookie 到浏览器
    # a={}
    # driver.delete_all_cookies()

    driver.get(docomo_url)
    print(driver.title)
    if do登录:
        driver.delete_all_cookies()
        if isinstance(do登录, list):
            print('cookies登录list')
            for i in do登录:
                try:
                    driver.add_cookie(i)
                except:
                    print(i)
                    continue
        elif isinstance(do登录, dict):
            print('cookies登录dict')
            for name, value in do登录.items():
                driver.add_cookie({'name': name, 'value': value, 'domain': '.docomo.ne.jp'})
    # a={'name':'docomo.ne.jp','value': cookies,
    # 'domain': 'docomo.ne.jp', 'path' : '/' }
    else:
        print('do-cookies异常')
    # driver.get(docomo_url)

    # 重新加载页面以应用 cookie
    driver.refresh()
    driver.get(docomo_url)

    try:
        wait = WebDriverWait(driver, 20, 0.5)
        wait.until(EC.presence_of_element_located((By.ID, "seizouBangouInput2")))
    except:
        title = driver.title
        print(title)
        if 'メンテナンス中' in title:
            展示('do官网维护中....', 1)
        else:
        #//*[@id="main"]/div/div[1]/p
        # page_source = driver.page_source
            elements = driver.find_elements(By.XPATH, '//*[@id="main"]/div[2]/div[1]')
            if elements:
                text = elements[0].text
                if 'メンテナンス中' in text or 'Y2181' in text:
                    展示('do官网维护中....', 1)
                else:
                    展示(text, 1)
            else:
                展示('遇到错误。。重试后不行请联系管理员提供新cookies', 1)
                with open(f'{error}\\do-不在解锁界面.html', 'w', encoding='utf-8') as f:
                    f.write(driver.page_source)
            if 'dアカウント - ログイン' in title:
                展示('请先输入账号名指纹密码登录验证到解锁界面后重试', 1)
        if driver:
            print('退出chrome进程')
            driver.quit()
            print(driver)
        return 2
    else:
        wait = WebDriverWait(driver, 15, 0.5)
        展示('正在开始解锁...')
        return driver, wait


def _async_raise(tid, exctype):
    """raises the exception, performs cleanup if needed"""
    tid = ctypes.c_long(tid)
    print(tid, 1)
    if not inspect.isclass(exctype):
        exctype = type(exctype)
    res = ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, ctypes.py_object(exctype))
    if res == 0:
        raise ValueError("invalid thread id")
    elif res != 1:
        # """if it returns a number greater than one, you're in trouble,
        # and you should call it again with exc=NULL to revert the effect"""
        ctypes.pythonapi.PyThreadState_SetAsyncExc(tid, None)
        raise SystemError("PyThreadState_SetAsyncExc failed")


def stop_thread(thread):
    _async_raise(thread.ident, SystemExit)


def 查询imei信息(imei, 展示):
    print(f'{imei} -- 正在联网查询......')
    try:
        response = get_content(f'https://alpha.imeicheck.com/api/modelBrandName?imei={imei.strip()}&format=json')
    except Exception as e:
        展示(f'查询失败 遇到错误',1)
        print(e)
        return False
    try:
        if isinstance(response, dict):
            data= response
            # 判断状态并展示 object 信息
            if data["status"] == "succes":
                object_info = data["object"]
                展示(
                    f"\n{imei}-设备信息：\n品牌: {object_info['brand']}\n名称: {object_info['name']}\n型号: {object_info['model']}")
            else:
                展示(f"查询失败-无法展示设备信息。{data['result']}", 1)
        else:
            展示(f"查询失败-无法展示设备信息。请查看日志", 1)
            print( response)
    except Exception as e:
        if '1015' in str(response):
            print(f'无法查询- 操作太过频繁 ip被封了')
            return
        print(f'查询失败 遇到错误{e}{response}')


def 查询型号(imei):
    if imei.startswith('0044'):
        return False
    try:
        response = get_content(f'https://alpha.imeicheck.com/api/modelBrandName?imei={imei.strip()}&format=json')
    except Exception as e:
        print(f'查询失败 遇到错误{e}')
        return False
    try:
        print(f'在线查询 - {imei}')
        if isinstance(response, dict):
            data = response
            # 判断状态并展示 object 信息
            if data["status"] == "succes":
                object_info = data["object"]
                return f"{object_info['brand'].strip().replace(' ', '-')}-{object_info['name'].strip().replace(' ', '-')}-{object_info['model'].strip().replace(' ', '-')}"
            return False
        else:
            print(imei+' '+ response)
    except Exception as e:
        if '1015' in str(response):
            print(f'无法查询- 操作太过频繁 ip被封了')
            return (f'糟糕!在线无法查询- 操作太过频繁 ip被封了\n解封去https://imeicheck.com/这里注册账号',)
        print(f'查询失败 遇到错误{e}{response}')


class 型号数据csv:
    def __init__(self, folder_path='tool', file_name='型号.csv'):
        self.file_path = os.path.join(folder_path, file_name)
        self.cached_data = None  # 用于缓存读取的数据
        self.last_modified_time = None  # 缓存文件的最后修改时间
        self.lock = threading.Lock()  # 用于同步读写操作的锁

        # 如果文件不存在，创建文件并写入表头
        if not os.path.exists(self.file_path):
            self._create_file_with_header()

    def _create_file_with_header(self):
        """创建新文件并写入表头"""
        try:
            with open(self.file_path, mode='w', newline='', encoding='utf-8-sig') as file:
                csv_writer = csv.DictWriter(file, fieldnames=['imei段', '型号', '运营商'])
                csv_writer.writeheader()
        except Exception as e:
            print(f"创建文件失败: {e}")

    def read_data(self):
        """读取数据，如果文件未修改，则返回缓存的数据"""
        try:
            # 获取文件的最后修改时间
            current_modified_time = os.path.getmtime(self.file_path)

            # 如果文件没有修改过，直接返回缓存的数据
            if self.last_modified_time == current_modified_time and self.cached_data is not None:
                return self.cached_data

            # 如果文件修改过或第一次读取，读取文件并缓存数据
            data_dict = {}
            with open(self.file_path, mode='r', newline='', encoding='utf-8-sig') as file:
                csv_reader = csv.DictReader(file)
                for row in csv_reader:
                    imei = row['imei段']
                    if len(imei) >= 8 and imei.isdigit():
                        imei = imei[:8]

                        if imei.startswith('0044'):
                            imei = imei[:10]
                        data_dict[imei] = {'型号': row['型号'], '运营商': row['运营商']}

            # 更新缓存和最后修改时间
            self.cached_data = data_dict
            self.last_modified_time = current_modified_time
            return data_dict

        except Exception as e:
            print(f"读取{self.file_path}-文件失败: {e}")
            return {}

    def add_data(self, data_dict):
        """添加一行数据到CSV文件"""
        with self.lock:
            try:
                if self.cached_data is None:
                    self.read_data()
                data= copy.deepcopy(self.cached_data)
                # 更新缓存中的数据
                self.cached_data.update(data_dict)
                if data != self.cached_data:
                # 覆盖写入整个文件
                    with open(self.file_path, mode='w', newline='', encoding='utf-8-sig') as file:
                        csv_writer = csv.DictWriter(file, fieldnames=['imei段', '型号', '运营商'])
                        csv_writer.writeheader()
                        for imei, info in self.cached_data.items():
                            csv_writer.writerow({'imei段': imei, '型号': info['型号'], '运营商': info['运营商']})
                            #print(f"已添加数据: IMEI={imei}, 型号={info['型号']}, 运营商={info['运营商']}")
                    # 更新缓存和文件的最后修改时间
                    self.last_modified_time = os.path.getmtime(self.file_path)

            except Exception as e:
                    print(f"添加数据失败: {e}")

def 添加型号运营商(info:dict):
    if info:
        型号数据 = 型号数据csv()
        数据=型号数据.read_data()
        add_dict={}
        for imei in info.keys():
            if imei[:8] in 数据:
                add_dict[imei[:8]] = {'型号':数据[imei[:8]]['型号'],'运营商':info[imei]['运营商']}
            else:
                add_dict[imei[:8]] = info[imei]
        型号数据.add_data(add_dict)
def 从数据文件获取运营商(imei,运营商):
    型号数据 = 型号数据csv()
    数据 = 型号数据.read_data()
    imei=imei[:8]
    if s:=数据.get(imei,False):
        if y:=s.get('运营商',False):
            if 运营商 == y.lower():
                return ' blacklist'
            return f' {s.get("型号","")} 请用运营商-{y}-解 imei'
        return f' {s.get("型号","")} NOT {运营商} imei'
    return f' NOT {运营商} imei'

def 查询解锁码(imei):
    url = "https://unlockoko.com/imei/search/"
    data = {'imei': imei}
    写入时间, 更新时间, unlockcode = '', '', ''
    try:
        response = requests.post(url, data=data)
        msg1 = response.json()
        print(msg1)
        data=msg1.get('data')
        if data:
            msg=data[0]
            unlockcode=msg['password']
            写入时间 = msg.get('created_at','')  # 假设字段名为 created_at
            更新时间 = msg.get('updated_at','')  # 假设字段名为 updated_at
        else:
            unlockcode= msg1.get("msg",'')
        print(imei,unlockcode, 写入时间, 更新时间)
        return {'解锁码': unlockcode, '写入时间': 写入时间, '更新时间': 更新时间}
    except Exception as e:
        print(f'查询失败：{e}')
        return False
def 批量查询解锁码(imei列表):
    # 批量IMEI查询
    url = 'https://unlockoko.com/imei/searchlist/'
    headers = {'Content-Type': 'application/json'}
    payload = {
        'imei_list': imei列表
    }

    # 发送请求
    response = requests.post(url, json=payload, headers=headers)

    # 提取数据
    if response.status_code == 200:
        result = response.json()

        # 检查请求是否成功
        if result['code'] == "100":
           return result
        else:
            print(f"查询失败: {result['msg']}")
            return False
    else:
        print(f"请求失败，状态码: {response.status_code}")
        print(response.text)