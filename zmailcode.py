import poplib
import re
import time
import zmail
yan = {
    'au': '<EMAIL>',
    'auinfo': '<EMAIL>',
    'do': '<EMAIL>',
    'doinfo': '<EMAIL>'
}
class zmail1:
    def __init__(self, name, password):
        self.name = name
        self.password = password
        self.server = zmail.server(self.name, self.password)
        try:
            self.server.stat()
        except poplib.error_proto as e:
            raise Warning ('登录邮箱太过频繁,可能登录需要图片验证码识别,晚点再试下')
        self.num =0 #邮件内容
        self.count=0 #邮件数量
        self.parent = re.compile(r'\d{6}')
        self.auhttp=re.compile(r'https://[^\s]*')
        self.dohttp=re.compile(r'機種：\s+(\S+).*?製造番号：\s+(\d+).*?解除コード：\s+(\d+)',re.S)
    def docodemail(self,imei):
        time.sleep(1)
        i = 5
        while i:
            count=self.server.stat()[0]
            mail=self.server.get_mail(count)
            if yan['doinfo'] in mail['From']:  # 判断是不是有新邮件!
                self.count=count
                a=self.dohttp.findall(mail['content_text'][0])
                if imei in a[0]:
                    self.num = a[0]
                    return a[0]
            time.sleep(1)
            i -= 1
            continue
    def doyanmail(self):
        if self.num == 0:
            self.count = self.server.stat()[0]
            time.sleep(2)
        i = 4
        while i:
            count = self.server.stat()[0]
            mail = self.server.get_mail(count)
            if yan['do'] in mail['From'] and count != self.count:  # 判断是不是有新邮件!
                self.count = count
                num = self.parent.findall(mail['content_text'][0])
                if num[0] != self.num:
                    self.num = num[0]
                    return num[0]
            time.sleep(2)
            i -= 1
            continue
    def auyanmail(self):
        if self.num == 0:
            self.count = self.server.stat()[0]
            time.sleep(1)
        time.sleep(2)
        i = 5
        while i:
            count = self.server.stat()[0]
            mail = self.server.get_mail(count)
            if yan['au'] in mail['From'] and count != self.count:  # 判断是不是有新邮件!
                self.count = count
                a = self.parent.findall(mail['content_text'][0])
                if a and a[0] != self.num:
                    self.num = a[0]
                    return a[0]
            time.sleep(2)
            i -= 1
            continue

    def aucodemail(self):
        if self.num == 0:
            self.count = self.server.stat()[0]
            time.sleep(1)
        i = 4
        while i:
            count=self.server.stat()[0]
            mail=self.server.get_mail(count)
            if yan['auinfo'] in mail['From'] and count != self.count:  # 判断是不是有新邮件!
                self.count=count
                a=self.auhttp.findall(mail['content_text'][0])
                if a and a[0] != self.num:
                    self.num = a[0]
                    return a[0]
            time.sleep(2)
            i -= 1
            continue
    def showemail(self):
        print(self.server.get_latest()['content_text'][0])

    def error(self,email,运营商):
        msg = {
            'subject': 运营商 + '解锁异常通知',
            'content_text': 运营商 + '--解锁异常已达到3次.已停止当前解锁任务,请检查核实!'
        }
        self.server.send_mail(email, msg)