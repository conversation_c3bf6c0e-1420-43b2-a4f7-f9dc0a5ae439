import time
from DrissionPage import ChromiumPage


class CloudflareBypasser:
    def __init__(self, driver: ChromiumPage, max_retries=3, log=True):
        self.driver = driver
        self.max_retries = max_retries
        self.log = log

    # 递归搜索函数已移除 - 实际使用中基础搜索已足够

    def locate_cf_button(self):
        button = None
        eles = self.driver.eles("tag:input")
        for ele in eles:
            if "name" in ele.attrs.keys() and "type" in ele.attrs.keys():
                if "turnstile" in ele.attrs["name"] and ele.attrs["type"] == "hidden":
                    button = ele.parent().shadow_root.child()("tag:body").shadow_root("tag:input")
                    break

        if button:
            return button
        else:
            self.log_message("未找到验证按钮。")
            return None

    def log_message(self, message):
        if self.log:
            print(message)

    def click_verification_button(self):
        try:
            button = self.locate_cf_button()
            if button:
                self.log_message("找到验证按钮，正在尝试点击。")
                button.click()
            else:
                self.log_message("未找到验证按钮。")

        except Exception as e:
            self.log_message(f"点击验证按钮时出错: {e}")

    def is_bypassed(self):
        try:
            title = self.driver.title.lower()
            return "just a moment" not in title
        except Exception as e:
            self.log_message(f"检查页面标题时出错: {e}")
            return False

    def bypass(self):

        try_count = 0

        while not self.is_bypassed():
            if 0 < self.max_retries + 1 <= try_count:
                self.log_message("超过最大重试次数，绕过失败。")
                break

            self.log_message(f"第 {try_count + 1} 次尝试: 检测到验证页面，正在尝试绕过...")
            self.click_verification_button()

            try_count += 1
            time.sleep(2)

        if self.is_bypassed():
            self.log_message("绕过成功。")
        else:
            self.log_message("绕过失败。")
